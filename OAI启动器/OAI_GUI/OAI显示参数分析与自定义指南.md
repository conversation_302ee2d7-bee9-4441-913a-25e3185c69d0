# OAI显示参数分析与自定义指南

## 概述

本文档详细分析了OpenAirInterface (OAI) 5G系统中 `-d` 参数的工作原理，以及如何自定义和扩展显示参数。

## `-d` 参数的工作原理

### 1. 参数定义位置

`-d` 参数在 `executables/softmodem-common.h` 文件中定义：

```c
{"d" , CONFIG_HLP_SOFTS, PARAMFLAG_BOOL, .uptr=&do_forms, .defintval=0, TYPE_UINT, 0},
```

其中 `CONFIG_HLP_SOFTS` 定义为：
```c
#define CONFIG_HLP_SOFTS "Enable soft scope and L1 and L2 stats (Xforms)\n"
```

### 2. 参数处理流程

1. **命令行解析**: 在 `get_common_options()` 函数 (`executables/softmodem-common.c:70`) 中处理
2. **标志设置**: 当检测到 `-d` 参数时，在第137-139行设置：
   ```c
   if (do_forms) {
     IS_SOFTMODEM_DOSCOPE = true;
   }
   ```
3. **Scope初始化**: 在 `nr-softmodem.c:802-804` 中调用：
   ```c
   if (IS_SOFTMODEM_DOSCOPE) {
     load_softscope("nr", &p);
   }
   ```

### 3. Scope系统架构

OAI的Scope系统有三种实现方式：

#### 3.1 传统XForms Scope (nr_phy_scope.c)
- 使用Forms库创建传统GUI界面
- 主要函数：`create_phy_scope_gnb()` (第465行), `create_phy_scope_nrue()` (第1050行)
- 显示参数在这些函数中定义

#### 3.2 现代ImGui Scope (imscope.cpp)
- 使用ImGui/ImPlot创建现代化界面
- 主要函数：`ShowGnbScope()`, `ShowUeScope()`
- 支持更丰富的可视化效果

#### 3.3 Web Scope (websrv_scope.c)
- 基于Web的可视化界面
- 可通过浏览器访问

## 当前可显示的参数详细分析

### gNB端参数 (create_phy_scope_gnb函数中定义)

1. **时域信号 (Time Domain)**
   - **代码位置**: `nr_phy_scope.c:485`
   - **参数**: `fdui->graph[0] = gNBcommonGraph(gNBWaterFall, WATERFALL, 0, curY, 400, 100, "Received Signal (Time-Domain, one frame)", FL_RED);`
   - **功能**: 显示接收信号的时域波形，以水印图形式展示

2. **PUSCH LLR (Log-Likelihood Ratios)**
   - **代码位置**: `nr_phy_scope.c:500`
   - **参数**: `fdui->graph[3] = gNBcommonGraph(puschLLR, FL_POINTS_XYPLOT, 0, curY, 500, 200, "PUSCH Log-Likelihood Ratios (LLR, mag)", FL_YELLOW);`
   - **功能**: 显示PUSCH信道的对数似然比，用于信道解码质量分析

3. **PUSCH I/Q数据**
   - **代码位置**: `nr_phy_scope.c:501`
   - **参数**: `fdui->graph[4] = gNBcommonGraph(puschIQ, FL_POINTS_XYPLOT, 500, curY, 300, 200, "PUSCH I/Q of MF Output", FL_YELLOW);`
   - **功能**: 显示PUSCH的I/Q星座图，用于调制质量分析

4. **PUSCH信道估计**
   - **代码位置**: `nr_phy_scope.c:504`
   - **参数**: `fdui->graph[5] = gNBcommonGraph(puschChanEst, FL_POINTS_XYPLOT, 0, curY, 500, 200, "PUSCH Channel Estimates (Frequency Domain)", FL_BLUE);`
   - **功能**: 显示PUSCH信道的频域估计结果

5. **PUSCH时域信道估计**
   - **代码位置**: `nr_phy_scope.c:505`
   - **参数**: `fdui->graph[6] = gNBcommonGraph(puschTimeChanEst, FL_POINTS_XYPLOT, 500, curY, 300, 200, "PUSCH Channel Estimates (Time Domain)", FL_BLUE);`
   - **功能**: 显示PUSCH信道的时域估计结果

### UE端参数 (create_phy_scope_nrue函数中定义)

1. **PSS相关性**
   - **代码位置**: `nr_phy_scope.c:1080`
   - **参数**: `fdui->graph[0] = nrUEcommonGraph(nrUEPssCorrelation, FL_NORMAL_XYPLOT, 0, curY, 300, 200, "PSS correlation", FL_YELLOW);`
   - **功能**: 显示主同步信号的相关性

2. **PDSCH LLR**
   - **代码位置**: `nr_phy_scope.c:1084`
   - **参数**: `fdui->graph[2] = nrUEcommonGraph(nrUEPdschLlr, FL_POINTS_XYPLOT, 0, curY, 300, 200, "PDSCH LLR", FL_YELLOW);`
   - **功能**: 显示PDSCH的对数似然比

3. **PDSCH I/Q数据**
   - **代码位置**: `nr_phy_scope.c:1085`
   - **参数**: `fdui->graph[3] = nrUEcommonGraph(nrUEPdschIQ, FL_POINTS_XYPLOT, 300, curY, 300, 200, "PDSCH I/Q", FL_YELLOW);`
   - **功能**: 显示PDSCH的I/Q星座图

## 自定义显示参数的详细方法

### 方法1: 修改XForms Scope

**步骤1**: 在 `openair1/PHY/TOOLS/nr_phy_scope.c` 中添加新的显示函数

```c
// 添加RSSI显示函数
void gNBRSSI(OAIgraph_t *graph, scopeData_t *p, int UE_id) {
    if (p->gNB == NULL) return;
    
    // 计算RSSI值
    float rssi_values[100];
    for (int i = 0; i < 100; i++) {
        rssi_values[i] = calculate_rssi_for_sample(p->gNB, i);
    }
    
    // 更新图表数据
    fl_set_xyplot_data(graph->graph, rssi_values, NULL, 100, 
                       "RSSI", "Sample", "dBm");
}
```

**步骤2**: 在 `create_phy_scope_gnb()` 函数中添加新图表

```c
// 在适当位置添加 (例如第510行后)
curY += 220;
fdui->graph[新索引] = gNBcommonGraph(gNBRSSI, FL_NORMAL_XYPLOT, 
                                    0, curY, 500, 200, 
                                    "RSSI Measurement (dBm)", FL_GREEN);
```

### 方法2: 修改ImGui Scope

**步骤1**: 在 `openair1/PHY/TOOLS/imscope/imscope.cpp` 中添加新的显示窗口

```cpp
void ShowGnbScope(void *data_void_ptr, float t) {
    // 现有代码...

    // 添加新的RSSI监控窗口
    if (ImGui::Begin("RSSI Monitor")) {
        static auto rssi_plot = new LinePlot();
        static std::vector<float> rssi_history;

        // 获取当前RSSI值
        float current_rssi = getCurrentRSSI(data_void_ptr);
        rssi_history.push_back(current_rssi);

        // 限制历史数据长度
        if (rssi_history.size() > 1000) {
            rssi_history.erase(rssi_history.begin());
        }

        // 绘制RSSI曲线
        rssi_plot->Draw(t, rssi_history.data(), rssi_history.size(), "RSSI (dBm)");

        // 显示当前值
        ImGui::Text("Current RSSI: %.2f dBm", current_rssi);
    }
    ImGui::End();

    // 添加SNR监控窗口
    if (ImGui::Begin("SNR Monitor")) {
        static auto snr_plot = new LinePlot();
        static std::vector<float> snr_history;

        float current_snr = getCurrentSNR(data_void_ptr);
        snr_history.push_back(current_snr);

        if (snr_history.size() > 1000) {
            snr_history.erase(snr_history.begin());
        }

        snr_plot->Draw(t, snr_history.data(), snr_history.size(), "SNR (dB)");
        ImGui::Text("Current SNR: %.2f dB", current_snr);
    }
    ImGui::End();
}
```

### 方法3: 添加新的数据收集机制

**步骤1**: 在 `phy_scope_interface.h` 中定义新的数据类型

```c
typedef enum {
    // 现有类型...
    gNBWaterFall,
    puschLLR,
    puschIQ,
    puschChanEst,
    puschTimeChanEst,
    // 新增类型
    gNBRSSI,
    gNBSNR,
    gNBThroughput,
    gNBErrorRate,
    gNBConnectionStatus,
    MAX_SCOPE_TYPES
} scopeDataType;
```

**步骤2**: 实现数据收集函数

```c
// 在适当的PHY处理文件中添加
void collectRSSIData(scopeData_t *scope, PHY_VARS_gNB *gNB, int UE_id) {
    if (!scope || !gNB) return;

    // 计算RSSI
    float rssi_value = 0.0;
    for (int aa = 0; aa < gNB->frame_parms.nb_antennas_rx; aa++) {
        int32_t *rxdata = &gNB->common_vars.rxdata[aa][0];
        float power = 0.0;
        for (int i = 0; i < gNB->frame_parms.samples_per_subframe; i++) {
            power += (float)(rxdata[i] * rxdata[i]);
        }
        rssi_value += 10.0 * log10(power / gNB->frame_parms.samples_per_subframe);
    }
    rssi_value /= gNB->frame_parms.nb_antennas_rx;

    // 存储数据
    metadata meta = {gNB->proc.frame_rx, gNB->proc.slot_rx};
    scope->copyData(scope, gNBRSSI, &rssi_value,
                   sizeof(float), 1, 1, 0, &meta);
}

void collectSNRData(scopeData_t *scope, PHY_VARS_gNB *gNB, int UE_id) {
    if (!scope || !gNB || UE_id >= NUMBER_OF_UE_MAX) return;

    // 从信道估计计算SNR
    float snr_value = 0.0;
    if (gNB->pusch_vars[UE_id]) {
        // 计算信号功率和噪声功率
        float signal_power = 0.0;
        float noise_power = 0.0;

        // 这里需要根据实际的信道估计数据结构来计算
        // 示例计算逻辑
        snr_value = signal_power / noise_power;
        snr_value = 10.0 * log10(snr_value);
    }

    metadata meta = {gNB->proc.frame_rx, gNB->proc.slot_rx};
    scope->copyData(scope, gNBSNR, &snr_value,
                   sizeof(float), 1, 1, 0, &meta);
}
```

**步骤3**: 在PHY处理函数中调用数据收集

```c
// 在 openair1/PHY/NR_TRANSPORT/nr_ulsch_decoding.c 中的适当位置添加
void nr_ulsch_decoding(...) {
    // 现有解码逻辑...

    // 添加scope数据收集
    if (IS_SOFTMODEM_DOSCOPE && gNB->scopeData) {
        collectRSSIData(gNB->scopeData, gNB, UE_id);
        collectSNRData(gNB->scopeData, gNB, UE_id);
    }

    // 继续现有逻辑...
}
```

## 编译和测试

### 编译选项

确保启用scope支持：
```bash
cd cmake_targets
./build_oai -w USRP --gNB --nrUE --build-lib "nrscope"
```

### 测试命令

```bash
sudo ./nr-softmodem -O ../targets/PROJECTS/GENERIC-NR-5GC/CONF/gnb.sa.band77.162prb.usrpn310.4x4.conf --gNBs.[0].min_rxtxtime 6 --rfsim -d
```

## 注意事项

1. **性能影响**: Scope显示会消耗额外的CPU资源
2. **线程安全**: 数据收集和显示需要考虑线程同步
3. **内存管理**: 避免内存泄漏，特别是在长时间运行时
4. **数据精度**: 确保显示的数据准确反映系统状态

## 总结

通过理解OAI的Scope系统架构，可以有效地添加和自定义显示参数。主要步骤包括：

1. 确定要显示的参数类型
2. 在适当的位置收集数据
3. 修改scope显示函数添加新图表
4. 编译测试验证效果

这种方法可以帮助开发者更好地监控和调试5G系统的运行状态。
