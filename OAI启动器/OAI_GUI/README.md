一、修改配置文件

对于oai_launcher.py，只需修改start_oai_all.sh的路径即可。（后续如果有展示日志的需要，可以自行修改日志的位置。）

修改201行的路径：

~~~
 script_path = os.path.expanduser('~/start_oai_all.sh')
~~~

对于start_oai_all.sh，修改10-14行的路径

~~~bash
GNB_DIR="~/openairinterface5g/cmake_targets/ran_build/build"         # gNB/nr-softmodem 所在目录
UE_DIR="~/openairinterface5g/cmake_targets/ran_build/build"         # nr-uesoftmodem 所在目录
CN_DIR="$HOME/oai-cn5g"            # OAI CN5G 核心网目录
GNB_CONF="../../../targets/PROJECTS/GENERIC-NR-5GC/CONF/gnb.sa.band78.fr1.106PRB.usrpb210.conf"
UE_CONF="../../../targets/PROJECTS/GENERIC-NR-5GC/CONF/ue.conf"
~~~

分别修改21、30、40行的密码，这样sudo就不用再自己输入密码了：

~~~bash
echo "123" | sudo -S ./nr-softmodem -O $GNB_CONF --gNBs.[0].min_rxtxtime 6 --rfsim -d; \     #修改密码“123”
~~~



二、配置环境

可以先试运行一下start_oai_all.sh：

~~~bash
./start_oai_all.sh
~~~



配置环境：

~~~bash
sudo apt update

sudo apt install python3-pip


pip3 install PyQt5 -i https://pypi.tuna.tsinghua.edu.cn/simple

pip3 install pyinstaller

pyinstaller --onefile --windowed oai_launcher.py

sudo apt install libxcb-xinerama0 libxcb-xinerama0-dev libxcb1 libxcb-util1 libx11-xcb1 libxrender1 libxi6 libsm6 libxext6

sudo apt install libgl1-mesa-glx

sudo apt install libxcb-cursor0

~~~

然后输入指令，即可运行：

~~~bash
python3 oai_launcher.py
~~~

