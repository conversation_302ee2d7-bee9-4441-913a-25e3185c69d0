/*
 * OAI Scope 自定义显示参数示例代码
 * 
 * 此文件展示如何在 nr_phy_scope.c 中添加新的显示参数
 * 包括：RSSI、SNR、CQI、连接状态等
 */

#include "nr_phy_scope.h"

// ==================== 新增显示函数 ====================

/**
 * 显示每个UE的RSSI值
 */
static void gNBRSSI(OAIgraph_t *graph, scopeData_t *p, int nb_UEs) {
    PHY_VARS_gNB *gNB = p->gNB;
    float rssi_values[MAX_MOBILES_PER_GNB];
    float ue_index[MAX_MOBILES_PER_GNB];
    
    for (int ue = 0; ue < nb_UEs && ue < MAX_MOBILES_PER_GNB; ue++) {
        ue_index[ue] = (float)ue;
        
        // 计算RSSI (基于接收功率)
        if (gNB->pusch_vars && gNB->pusch_vars[ue].rxdataF_comp) {
            int32_t *rxdata = (int32_t*)gNB->pusch_vars[ue].rxdataF_comp[0];
            double power_sum = 0.0;
            int samples = gNB->frame_parms.N_RB_UL * 12; // 每个RB 12个子载波
            
            for (int i = 0; i < samples; i++) {
                power_sum += (double)(rxdata[i] * rxdata[i]);
            }
            
            // 转换为dBm
            rssi_values[ue] = 10.0 * log10(power_sum / samples) - 30.0; // 假设参考功率
        } else {
            rssi_values[ue] = -120.0; // 默认值
        }
    }
    
    oai_xygraph(graph, ue_index, rssi_values, nb_UEs, 0, 10);
}

/**
 * 显示每个UE的SNR值
 */
static void gNBSNR(OAIgraph_t *graph, scopeData_t *p, int nb_UEs) {
    PHY_VARS_gNB *gNB = p->gNB;
    float snr_values[MAX_MOBILES_PER_GNB];
    float ue_index[MAX_MOBILES_PER_GNB];
    
    for (int ue = 0; ue < nb_UEs && ue < MAX_MOBILES_PER_GNB; ue++) {
        ue_index[ue] = (float)ue;
        
        // 从PUSCH解调中获取SNR估计
        if (gNB->pusch_vars && gNB->pusch_vars[ue].ulsch_power) {
            // 简化的SNR计算
            double signal_power = gNB->pusch_vars[ue].ulsch_power[0];
            double noise_power = gNB->measurements.n0_power[0];
            
            if (noise_power > 0) {
                snr_values[ue] = 10.0 * log10(signal_power / noise_power);
            } else {
                snr_values[ue] = 0.0;
            }
        } else {
            snr_values[ue] = -10.0; // 默认值
        }
    }
    
    oai_xygraph(graph, ue_index, snr_values, nb_UEs, 0, 10);
}

/**
 * 显示UE连接状态和数量
 */
static void gNBConnectionStatus(OAIgraph_t *graph, scopeData_t *p, int nb_UEs) {
    PHY_VARS_gNB *gNB = p->gNB;
    float status_values[MAX_MOBILES_PER_GNB];
    float ue_index[MAX_MOBILES_PER_GNB];
    
    for (int ue = 0; ue < MAX_MOBILES_PER_GNB; ue++) {
        ue_index[ue] = (float)ue;
        
        // 检查UE是否活跃
        if (ue < nb_UEs && gNB->UE_info.active[ue]) {
            status_values[ue] = 1.0; // 连接
        } else {
            status_values[ue] = 0.0; // 未连接
        }
    }
    
    oai_xygraph(graph, ue_index, status_values, MAX_MOBILES_PER_GNB, 0, 10);
}

/**
 * 显示实时吞吐量统计
 */
static void gNBRealTimeThroughput(OAIgraph_t *graph, scopeData_t *p, int nb_UEs) {
    static float throughput_history[TPUT_WINDOW_LENGTH];
    static float time_axis[TPUT_WINDOW_LENGTH];
    static int history_index = 0;
    static uint64_t last_bytes = 0;
    static uint64_t last_time = 0;
    
    PHY_VARS_gNB *gNB = p->gNB;
    uint64_t current_time = get_time_us();
    uint64_t total_bytes = 0;
    
    // 计算总字节数
    for (int ue = 0; ue < nb_UEs; ue++) {
        if (gNB->UE_info.active[ue]) {
            // 假设从MAC层获取字节统计
            total_bytes += gNB->UE_info.mac_stats[ue].total_bytes_tx;
        }
    }
    
    // 计算吞吐量 (Mbps)
    if (last_time > 0 && current_time > last_time) {
        uint64_t time_diff = current_time - last_time;
        uint64_t bytes_diff = total_bytes - last_bytes;
        float throughput_mbps = (float)(bytes_diff * 8) / (float)time_diff; // Mbps
        
        throughput_history[history_index] = throughput_mbps;
        time_axis[history_index] = (float)history_index;
        
        history_index = (history_index + 1) % TPUT_WINDOW_LENGTH;
    }
    
    last_bytes = total_bytes;
    last_time = current_time;
    
    oai_xygraph(graph, time_axis, throughput_history, TPUT_WINDOW_LENGTH, 0, 10);
}

/**
 * 显示频谱效率
 */
static void gNBSpectralEfficiency(OAIgraph_t *graph, scopeData_t *p, int nb_UEs) {
    PHY_VARS_gNB *gNB = p->gNB;
    float efficiency_values[MAX_MOBILES_PER_GNB];
    float ue_index[MAX_MOBILES_PER_GNB];
    
    for (int ue = 0; ue < nb_UEs && ue < MAX_MOBILES_PER_GNB; ue++) {
        ue_index[ue] = (float)ue;
        
        if (gNB->UE_info.active[ue]) {
            // 计算频谱效率 = 吞吐量 / 带宽
            float throughput = gNB->UE_info.mac_stats[ue].current_throughput; // bps
            float bandwidth = gNB->frame_parms.N_RB_UL * 180000.0; // Hz (每个RB 180kHz)
            
            if (bandwidth > 0) {
                efficiency_values[ue] = throughput / bandwidth; // bps/Hz
            } else {
                efficiency_values[ue] = 0.0;
            }
        } else {
            efficiency_values[ue] = 0.0;
        }
    }
    
    oai_xygraph(graph, ue_index, efficiency_values, nb_UEs, 0, 10);
}

// ==================== 修改 create_phy_scope_gnb 函数 ====================

/*
 * 在原有的 create_phy_scope_gnb() 函数中添加新的图表
 * 需要在 fdui->graph[8].graph=NULL; 之前添加以下代码：
 */

/*
// 添加RSSI显示
fdui->graph[8] = gNBcommonGraph(gNBRSSI, FL_NORMAL_XYPLOT, 0, curY, 400, 100,
                                "RSSI per UE [dBm]", FL_GREEN);
fdui->graph[8].chartid = SCOPEMSG_DATAID_TRESP;
fdui->graph[8].datasetid = 3;
fl_get_object_bbox(fdui->graph[8].graph, &x, &y, &w, &h);
curY += h;

// 添加SNR显示
fdui->graph[9] = gNBcommonGraph(gNBSNR, FL_NORMAL_XYPLOT, 400, curY-h, 400, 100,
                                "SNR per UE [dB]", FL_BLUE);
fdui->graph[9].chartid = SCOPEMSG_DATAID_TRESP;
fdui->graph[9].datasetid = 4;

// 添加连接状态显示
fdui->graph[10] = gNBcommonGraph(gNBConnectionStatus, FL_NORMAL_XYPLOT, 0, curY, 400, 100,
                                 "UE Connection Status", FL_MAGENTA);
fdui->graph[10].chartid = SCOPEMSG_DATAID_TRESP;
fdui->graph[10].datasetid = 5;

// 添加实时吞吐量显示
fdui->graph[11] = gNBcommonGraph(gNBRealTimeThroughput, FL_NORMAL_XYPLOT, 400, curY, 400, 100,
                                 "Real-time Throughput [Mbps]", FL_CYAN);
fdui->graph[11].chartid = SCOPEMSG_DATAID_TRESP;
fdui->graph[11].datasetid = 6;
fl_get_object_bbox(fdui->graph[11].graph, &x, &y, &w, &h);
curY += h;

// 添加频谱效率显示
fdui->graph[12] = gNBcommonGraph(gNBSpectralEfficiency, FL_NORMAL_XYPLOT, 0, curY, 800, 100,
                                 "Spectral Efficiency [bps/Hz]", FL_YELLOW);
fdui->graph[12].chartid = SCOPEMSG_DATAID_TRESP;
fdui->graph[12].datasetid = 7;

// 结束标记
fdui->graph[13].graph = NULL;
*/

// ==================== 辅助函数 ====================

/**
 * 获取当前时间（微秒）
 */
static uint64_t get_time_us(void) {
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return (uint64_t)ts.tv_sec * 1000000ULL + (uint64_t)ts.tv_nsec / 1000ULL;
}

/**
 * 计算平均值
 */
static float calculate_average(float *data, int length) {
    float sum = 0.0;
    for (int i = 0; i < length; i++) {
        sum += data[i];
    }
    return sum / length;
}

/**
 * 计算标准差
 */
static float calculate_std_dev(float *data, int length, float mean) {
    float sum_sq_diff = 0.0;
    for (int i = 0; i < length; i++) {
        float diff = data[i] - mean;
        sum_sq_diff += diff * diff;
    }
    return sqrt(sum_sq_diff / length);
}

// ==================== 配置选项 ====================

/*
 * 可以添加配置选项来控制显示行为
 */
typedef struct {
    bool enable_rssi;
    bool enable_snr;
    bool enable_connection_status;
    bool enable_throughput;
    bool enable_spectral_efficiency;
    int update_interval_ms;
    int history_length;
} scope_config_t;

static scope_config_t scope_config = {
    .enable_rssi = true,
    .enable_snr = true,
    .enable_connection_status = true,
    .enable_throughput = true,
    .enable_spectral_efficiency = true,
    .update_interval_ms = 100,
    .history_length = TPUT_WINDOW_LENGTH
};

/*
 * 使用说明：
 * 1. 将这些函数添加到 nr_phy_scope.c 文件中
 * 2. 在 create_phy_scope_gnb() 函数中添加新的图表定义
 * 3. 重新编译 OAI
 * 4. 使用 -d 参数启动时将看到新的显示参数
 */
