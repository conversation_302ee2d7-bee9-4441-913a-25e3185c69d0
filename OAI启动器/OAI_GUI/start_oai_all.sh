#!/bin/bash

# =============================
# 一键启动 OAI gNB、CN5G、nrue 脚本
# =============================
# 请根据实际环境修改以下路径和参数

set -e  # 遇到错误立即退出

GNB_DIR="../../build"         # gNB/nr-softmodem 所在目录
UE_DIR="../../build"         # nr-uesoftmodem 所在目录
CN_DIR="$HOME/oai-cn5g"            # OAI CN5G 核心网目录
GNB_CONF="../../targets/PROJECTS/GENERIC-NR-5GC/CONF/gnb.sa.band78.fr1.189PRB.rfsim.conf"
UE_CONF="../../targets/PROJECTS/GENERIC-NR-5GC/CONF/ue.conf"
GNB_IP="**************"            # gNB 网卡IP（用于核心网路由）

# 检查必要文件是否存在
echo "检查必要文件..."
if [ ! -f "$GNB_DIR/nr-softmodem" ]; then
    echo "错误: $GNB_DIR/nr-softmodem 不存在"
    exit 1
fi

if [ ! -f "$UE_DIR/nr-uesoftmodem" ]; then
    echo "错误: $UE_DIR/nr-uesoftmodem 不存在"
    exit 1
fi

if [ ! -f "$GNB_CONF" ]; then
    echo "错误: gNB配置文件 $GNB_CONF 不存在"
    exit 1
fi

if [ ! -f "$UE_CONF" ]; then
    echo "错误: UE配置文件 $UE_CONF 不存在"
    exit 1
fi

# 函数：等待端口开启
wait_for_port() {
    local host=$1
    local port=$2
    local timeout=${3:-30}
    local count=0

    echo "等待 $host:$port 端口开启..."
    while [ $count -lt $timeout ]; do
        if nc -z $host $port 2>/dev/null; then
            echo "端口 $host:$port 已开启"
            return 0
        fi
        sleep 1
        count=$((count + 1))
        echo -n "."
    done
    echo ""
    echo "超时：端口 $host:$port 未在 $timeout 秒内开启"
    return 1
}

# 清理函数
cleanup() {
    echo "清理进程..."
    pkill -f nr-softmodem || true
    pkill -f nr-uesoftmodem || true
}

# 设置信号处理
trap cleanup EXIT INT TERM

echo "开始启动 OAI 系统..."

# 1. 启动 RF 模拟器 gNB (服务器模式)
echo "1. 启动 gNB RF模拟器..."
gnome-terminal --title="OAI gNB" -- bash -c "\
cd $GNB_DIR; \
echo '启动 gNB RF模拟器 (服务器模式)...'; \
echo '配置文件: $GNB_CONF'; \
echo '监听端口: 4043'; \
echo '设置库路径...'; \
export LD_LIBRARY_PATH=\$PWD:\$LD_LIBRARY_PATH; \
echo "123" | sudo LD_LIBRARY_PATH=\$PWD:\$LD_LIBRARY_PATH ./nr-softmodem -O $GNB_CONF --gNBs.[0].min_rxtxtime 6 --rfsim --rfsimulator.serveraddr server --rfsimulator.serverport 4043 --sa -d; \
echo 'gNB 已退出，按任意键关闭窗口...'; \
read; \
"

# 等待gNB启动并监听4043端口
echo "等待 gNB 启动..."
if ! wait_for_port 127.0.0.1 4043 60; then
    echo "错误: gNB 未能在60秒内启动并监听4043端口"
    echo "请检查 gNB 日志窗口中的错误信息"
    exit 1
fi

echo "gNB 已成功启动并监听4043端口"

# 2. 启动 OAI CN5G 核心网
echo "2. 启动 OAI CN5G 核心网..."
if [ -d "$CN_DIR" ]; then
    gnome-terminal --title="OAI CN5G" -- bash -c "\
    cd $CN_DIR; \
    echo '启动 OAI CN5G 核心网...'; \
    docker compose up -d; \
    sleep 5; \
    echo '添加路由...'; \
    echo "123" | sudo -S ip route add ********/24 via $GNB_IP 2>/dev/null || echo '路由可能已存在'; \
    echo '跟踪 oai-amf 日志...'; \
    docker logs oai-amf -f; \
    "

    # 等待核心网启动
    echo "等待核心网启动..."
    sleep 10
else
    echo "警告: CN5G目录 $CN_DIR 不存在，跳过核心网启动"
fi

# 3. 启动 OAI nrUE (客户端模式)
echo "3. 启动 OAI nrUE..."
sleep 5  # 额外等待确保gNB完全就绪

gnome-terminal --title="OAI nrUE" -- bash -c "\
cd $UE_DIR; \
echo '启动 OAI nrUE (客户端模式)...'; \
echo '连接到: 127.0.0.1:4043'; \
echo '配置文件: $UE_CONF'; \
echo '设置库路径...'; \
export LD_LIBRARY_PATH=\$PWD:\$LD_LIBRARY_PATH; \
echo "123" | sudo LD_LIBRARY_PATH=\$PWD:\$LD_LIBRARY_PATH ./nr-uesoftmodem --rfsim --rfsimulator.serveraddr 127.0.0.1 --rfsimulator.serverport 4043 -r 189 --numerology 1 --band 78 -C 3334620000 --sa -O $UE_CONF -d; \
echo 'nrUE 已退出，按任意键关闭窗口...'; \
read; \
"

echo "所有服务已在新终端窗口启动。"
echo ""
echo "启动顺序："
echo "1. gNB (RF仿真器服务器) - 监听 127.0.0.1:4043"
echo "2. CN5G (核心网) - 如果目录存在"
echo "3. nrUE (RF仿真器客户端) - 连接到 127.0.0.1:4043"
echo ""
echo "如果遇到连接问题，请："
echo "1. 检查 gNB 窗口是否有错误"
echo "2. 确认端口4043是否被正确监听: ss -tlnp | grep 4043"
echo "3. 检查防火墙设置"
echo ""
echo "按 Ctrl+C 可以清理所有进程"

# 保持脚本运行，直到用户中断
while true; do
    sleep 1
done
