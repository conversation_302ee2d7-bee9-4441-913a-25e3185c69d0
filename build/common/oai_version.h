/*
 * Licensed to the OpenAirInterface (OAI) Software Alliance under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The OpenAirInterface Software Alliance licenses this file to You under
 * the OAI Public License, Version 1.1  (the "License"); you may not use this file
 * except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.openairinterface.org/?page_id=698
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *-------------------------------------------------------------------------------
 * For more information about the OpenAirInterface (OAI) Software Alliance:
 *      <EMAIL>
 */

#ifndef OAI_VERSION_H_
#define OAI_VERSION_H_

#define GIT_<PERSON>ANCH "develop"
#define GIT_COMMIT_HASH "f5d548c471"
#define GIT_COMMIT_DATE "Thu Jul 10 16:37:18 2025 +0800"

#define OAI_FIRMWARE_VERSION GIT_COMMIT_HASH
#define OAI_PACKAGE_VERSION "Branch: " GIT_BRANCH " Abrev. Hash: " GIT_COMMIT_HASH " Date: " GIT_COMMIT_DATE

#endif /* OAI_VERSION_H_ */
