#include <stdint.h>
#include "PHY/sse_intrin.h"
static inline void nrLDPC_bnProcPc_BG1_R13_AVX2(int8_t* bnProcBuf,int8_t* bnProcBufRes,int8_t* llrRes ,  int8_t* llrProc<PERSON>uf, uint16_t Z ) {
   simde__m256i ymm0, ymm1, ymmRes0, ymmRes1;  
        simde__m128i* p_bnProcBuf; 
        simde__m128i* p_llrProcBuf;
        simde__m256i* p_llrRes; 
         uint32_t M ;
// Process group with 1 CNs 
// Process group with 2 CNs 
// Process group with 3 CNs 
// Process group with 4 CNs 
 M = (1*Z + 31)>>5;
    p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [16128];
    p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [16128];
    p_llrRes        = (simde__m256i*) &llrRes       [16128];
        for (int i=0,j=0;i<M;i++,j+=2) {
        ymmRes0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf [j]);
        ymmRes1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf [j +1]);
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[24 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[24 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[48 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[48 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[72 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[72 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm256_cvtepi8_epi16(p_llrProcBuf[j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm256_cvtepi8_epi16(p_llrProcBuf[j +1 ]);
        ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1);
        ymm0 = simde_mm256_packs_epi16(ymmRes0, ymmRes1);
            p_llrRes[i] = simde_mm256_permute4x64_epi64(ymm0, 0xD8);
}
// Process group with 5 CNs 
 M = (1*Z + 31)>>5;
    p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [17664];
    p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [16512];
    p_llrRes        = (simde__m256i*) &llrRes       [16512];
        for (int i=0,j=0;i<M;i++,j+=2) {
        ymmRes0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf [j]);
        ymmRes1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf [j +1]);
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[24 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[24 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[48 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[48 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[72 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[72 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[96 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[96 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm256_cvtepi8_epi16(p_llrProcBuf[j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm256_cvtepi8_epi16(p_llrProcBuf[j +1 ]);
        ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1);
        ymm0 = simde_mm256_packs_epi16(ymmRes0, ymmRes1);
            p_llrRes[i] = simde_mm256_permute4x64_epi64(ymm0, 0xD8);
}
// Process group with 6 CNs 
 M = (2*Z + 31)>>5;
    p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [19584];
    p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [16896];
    p_llrRes        = (simde__m256i*) &llrRes       [16896];
        for (int i=0,j=0;i<M;i++,j+=2) {
        ymmRes0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf [j]);
        ymmRes1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf [j +1]);
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[48 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[48 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[96 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[96 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[144 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[144 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[192 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[192 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[240 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[240 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm256_cvtepi8_epi16(p_llrProcBuf[j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm256_cvtepi8_epi16(p_llrProcBuf[j +1 ]);
        ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1);
        ymm0 = simde_mm256_packs_epi16(ymmRes0, ymmRes1);
            p_llrRes[i] = simde_mm256_permute4x64_epi64(ymm0, 0xD8);
}
// Process group with 7 CNs 
 M = (4*Z + 31)>>5;
    p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [24192];
    p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [17664];
    p_llrRes        = (simde__m256i*) &llrRes       [17664];
        for (int i=0,j=0;i<M;i++,j+=2) {
        ymmRes0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf [j]);
        ymmRes1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf [j +1]);
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[96 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[96 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[192 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[192 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[288 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[288 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[384 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[384 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[480 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[480 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[576 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[576 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm256_cvtepi8_epi16(p_llrProcBuf[j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm256_cvtepi8_epi16(p_llrProcBuf[j +1 ]);
        ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1);
        ymm0 = simde_mm256_packs_epi16(ymmRes0, ymmRes1);
            p_llrRes[i] = simde_mm256_permute4x64_epi64(ymm0, 0xD8);
}
// Process group with 8 CNs 
 M = (3*Z + 31)>>5;
    p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [34944];
    p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [19200];
    p_llrRes        = (simde__m256i*) &llrRes       [19200];
        for (int i=0,j=0;i<M;i++,j+=2) {
        ymmRes0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf [j]);
        ymmRes1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf [j +1]);
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[72 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[72 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[144 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[144 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[216 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[216 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[288 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[288 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[360 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[360 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[432 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[432 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[504 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[504 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm256_cvtepi8_epi16(p_llrProcBuf[j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm256_cvtepi8_epi16(p_llrProcBuf[j +1 ]);
        ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1);
        ymm0 = simde_mm256_packs_epi16(ymmRes0, ymmRes1);
            p_llrRes[i] = simde_mm256_permute4x64_epi64(ymm0, 0xD8);
}
// Process group with 9 CNs 
 M = (1*Z + 31)>>5;
    p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [44160];
    p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [20352];
    p_llrRes        = (simde__m256i*) &llrRes       [20352];
        for (int i=0,j=0;i<M;i++,j+=2) {
        ymmRes0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf [j]);
        ymmRes1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf [j +1]);
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[24 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[24 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[48 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[48 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[72 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[72 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[96 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[96 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[120 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[120 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[144 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[144 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[168 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[168 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[192 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[192 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm256_cvtepi8_epi16(p_llrProcBuf[j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm256_cvtepi8_epi16(p_llrProcBuf[j +1 ]);
        ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1);
        ymm0 = simde_mm256_packs_epi16(ymmRes0, ymmRes1);
            p_llrRes[i] = simde_mm256_permute4x64_epi64(ymm0, 0xD8);
}
// Process group with 10 CNs 
 M = (4*Z + 31)>>5;
    p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [47616];
    p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [20736];
    p_llrRes        = (simde__m256i*) &llrRes       [20736];
        for (int i=0,j=0;i<M;i++,j+=2) {
        ymmRes0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf [j]);
        ymmRes1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf [j +1]);
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[96 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[96 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[192 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[192 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[288 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[288 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[384 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[384 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[480 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[480 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[576 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[576 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[672 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[672 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[768 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[768 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[864 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[864 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm256_cvtepi8_epi16(p_llrProcBuf[j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm256_cvtepi8_epi16(p_llrProcBuf[j +1 ]);
        ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1);
        ymm0 = simde_mm256_packs_epi16(ymmRes0, ymmRes1);
            p_llrRes[i] = simde_mm256_permute4x64_epi64(ymm0, 0xD8);
}
// Process group with 11 CNs 
 M = (3*Z + 31)>>5;
    p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [62976];
    p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [22272];
    p_llrRes        = (simde__m256i*) &llrRes       [22272];
            for (int i=0,j=0;i<M;i++,j+=2) {
            ymmRes0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf [j]);
            ymmRes1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf [j +1]);
            ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[72 + j]);
            ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
            ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[72 + j +1]);
           ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
            ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[144 + j]);
            ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
            ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[144 + j +1]);
           ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
            ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[216 + j]);
            ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
            ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[216 + j +1]);
           ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
            ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[288 + j]);
            ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
            ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[288 + j +1]);
           ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
            ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[360 + j]);
            ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
            ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[360 + j +1]);
           ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
            ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[432 + j]);
            ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
            ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[432 + j +1]);
           ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
            ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[504 + j]);
            ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
            ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[504 + j +1]);
           ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
            ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[576 + j]);
            ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
            ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[576 + j +1]);
           ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
            ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[648 + j]);
            ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
            ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[648 + j +1]);
           ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
            ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[720 + j]);
            ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
            ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[720 + j +1]);
           ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
            ymm0    = simde_mm256_cvtepi8_epi16(p_llrProcBuf[j]);
            ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
            ymm1    = simde_mm256_cvtepi8_epi16(p_llrProcBuf[j +1 ]);
            ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1);
            ymm0 = simde_mm256_packs_epi16(ymmRes0, ymmRes1);
            p_llrRes[i] = simde_mm256_permute4x64_epi64(ymm0, 0xD8);
}
// Process group with 12 CNs 
 M = (4*Z + 31)>>5;
    p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [75648];
    p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [23424];
    p_llrRes        = (simde__m256i*) &llrRes       [23424];
            for (int i=0,j=0;i<M;i++,j+=2) {
            ymmRes0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf [j]);
            ymmRes1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf [j +1]);
            ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[96 + j]);
            ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
            ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[96 + j +1]);
           ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
            ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[192 + j]);
            ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
            ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[192 + j +1]);
           ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
            ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[288 + j]);
            ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
            ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[288 + j +1]);
           ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
            ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[384 + j]);
            ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
            ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[384 + j +1]);
           ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
            ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[480 + j]);
            ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
            ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[480 + j +1]);
           ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
            ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[576 + j]);
            ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
            ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[576 + j +1]);
           ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
            ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[672 + j]);
            ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
            ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[672 + j +1]);
           ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
            ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[768 + j]);
            ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
            ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[768 + j +1]);
           ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
            ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[864 + j]);
            ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
            ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[864 + j +1]);
           ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
            ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[960 + j]);
            ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
            ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[960 + j +1]);
           ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
            ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[1056 + j]);
            ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
            ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[1056 + j +1]);
           ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
            ymm0    = simde_mm256_cvtepi8_epi16(p_llrProcBuf[j]);
            ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
            ymm1    = simde_mm256_cvtepi8_epi16(p_llrProcBuf[j +1 ]);
            ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1);
            ymm0 = simde_mm256_packs_epi16(ymmRes0, ymmRes1);
            p_llrRes[i] = simde_mm256_permute4x64_epi64(ymm0, 0xD8);
}
// Process group with 13 CNs 
 M = (1*Z + 31)>>5;
    p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [94080];
    p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [24960];
    p_llrRes        = (simde__m256i*) &llrRes       [24960];
        for (int i=0,j=0;i<M;i++,j+=2) {
        ymmRes0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf [j]);
        ymmRes1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf [j +1]);
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[24 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[24 + j +1]);
        ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[48 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[48 + j +1]);
        ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[72 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[72 + j +1]);
        ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[96 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[96 + j +1]);
        ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[120 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[120 + j +1]);
        ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[144 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[144 + j +1]);
        ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[168 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[168 + j +1]);
        ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[192 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[192 + j +1]);
        ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[216 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[216 + j +1]);
        ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[240 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[240 + j +1]);
        ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[264 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[264 + j +1]);
        ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[288 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[288 + j +1]);
        ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm256_cvtepi8_epi16(p_llrProcBuf[j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm256_cvtepi8_epi16(p_llrProcBuf[j +1 ]);
        ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1);
        ymm0 = simde_mm256_packs_epi16(ymmRes0, ymmRes1);
            p_llrRes[i] = simde_mm256_permute4x64_epi64(ymm0, 0xD8);
}
// Process group with 14 CNs 
// Process group with 15 CNs 
// Process group with 16 CNs 
// Process group with 17 CNs 
// Process group with 18 CNs 
// Process group with 19 CNs 
// Process group with 20 CNs 
// Process group with 21 CNs 
// Process group with 22 CNs 
// Process group with <23 CNs 
// Process group with 24 CNs 
// Process group with 25 CNs 
// Process group with 26 CNs 
// Process group with 27 CNs 
// Process group with 28 CNs 
 M = (1*Z + 31)>>5;
    p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [99072];
    p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [25344];
    p_llrRes        = (simde__m256i*) &llrRes       [25344];
        for (int i=0,j=0;i<M;i++,j+=2) {
        ymmRes0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf [j]);
        ymmRes1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf [j +1]);
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[24 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[24 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[48 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[48 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[72 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[72 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[96 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[96 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[120 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[120 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[144 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[144 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[168 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[168 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[192 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[192 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[216 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[216 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[240 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[240 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[264 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[264 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[288 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[288 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[312 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[312 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[336 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[336 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[360 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[360 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[384 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[384 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[408 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[408 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[432 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[432 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[456 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[456 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[480 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[480 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[504 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[504 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[528 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[528 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[552 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[552 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[576 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[576 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[600 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[600 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[624 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[624 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[648 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[648 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm256_cvtepi8_epi16(p_llrProcBuf[j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm256_cvtepi8_epi16(p_llrProcBuf[j +1 ]);
        ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1);
        ymm0 = simde_mm256_packs_epi16(ymmRes0, ymmRes1);
            p_llrRes[i] = simde_mm256_permute4x64_epi64(ymm0, 0xD8);
}
// Process group with 29 CNs 
// Process group with 30 CNs 
 M = (1*Z + 31)>>5;
    p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [109824];
    p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [25728];
    p_llrRes        = (simde__m256i*) &llrRes       [25728];
        for (int i=0,j=0;i<M;i++,j+=2) {
        ymmRes0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf [j]);
        ymmRes1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf [j +1]);
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[24 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[24 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[48 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[48 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[72 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[72 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[96 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[96 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[120 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[120 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[144 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[144 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[168 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[168 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[192 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[192 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[216 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[216 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[240 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[240 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[264 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[264 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[288 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[288 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[312 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[312 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[336 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[336 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[360 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[360 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[384 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[384 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[408 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[408 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[432 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[432 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[456 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[456 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[480 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[480 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[504 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[504 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[528 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[528 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[552 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[552 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[576 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[576 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[600 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[600 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[624 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[624 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[648 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[648 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[672 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[672 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[696 + j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm256_cvtepi8_epi16(p_bnProcBuf[696 + j +1]);
       ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm256_cvtepi8_epi16(p_llrProcBuf[j]);
        ymmRes0 = simde_mm256_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm256_cvtepi8_epi16(p_llrProcBuf[j +1 ]);
        ymmRes1 = simde_mm256_adds_epi16(ymmRes1, ymm1);
        ymm0 = simde_mm256_packs_epi16(ymmRes0, ymmRes1);
            p_llrRes[i] = simde_mm256_permute4x64_epi64(ymm0, 0xD8);
}
}
