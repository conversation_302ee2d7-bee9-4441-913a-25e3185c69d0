#include <stdint.h>
#include "PHY/sse_intrin.h"
static inline void nrLDPC_bnProcPc_BG1_R23_128(int8_t* bnProcBuf,int8_t* bnProcBufRes,int8_t* llrRes ,  int8_t* llrProc<PERSON>uf, uint16_t Z ) {
  // Process group with 1 CN
        uint32_t M = (9*Z + 15)>>4;
        simde__m128i* p_bnProcBuf    = (simde__m128i*) &bnProcBuf    [0];
        simde__m128i* p_bnProcBufRes = (simde__m128i*) &bnProcBufRes [0];
        simde__m128i* p_llrProcBuf   = (simde__m128i*) &llrProcBuf   [0];
        simde__m128i* p_llrRes       = (simde__m128i*) &llrRes       [0];
        simde__m128i ymm0, ymm1, ymmRes0, ymmRes1;
        for (int i=0;i<M;i++) {
          p_bnProcBufRes[i] = p_llrProcBuf[i];
          ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf [i]);
          ymm1 = simde_mm_cvtepi8_epi16(p_llrProcBuf[i]);
          ymmRes0 = simde_mm_adds_epi16(ymm0, ymm1);
          ymm0 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf [i],8));
          ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_llrProcBuf[i],8));
          ymmRes1 = simde_mm_adds_epi16(ymm0, ymm1);
          *p_llrRes = simde_mm_packs_epi16(ymmRes0, ymmRes1);
          p_llrRes++;
        }
  M = (1*Z + 15)>>4;
  p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [3456];
  p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [3456];
  p_llrRes        = (simde__m128i*) &llrRes       [3456];
        for (int i=0;i<M;i++) {
        ymmRes0 = simde_mm_cvtepi8_epi16(p_bnProcBuf [i]);
        ymmRes1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf [i],8));
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[24 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[24 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm_cvtepi8_epi16(p_llrProcBuf[i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_llrProcBuf[i],8));
        ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1);
        *p_llrRes = simde_mm_packs_epi16(ymmRes0, ymmRes1);
        p_llrRes++;
   }
  M = (5*Z + 15)>>4;
  p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [4224];
  p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [3840];
  p_llrRes        = (simde__m128i*) &llrRes       [3840];
        for (int i=0;i<M;i++) {
        ymmRes0 = simde_mm_cvtepi8_epi16(p_bnProcBuf [i]);
        ymmRes1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf [i],8));
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[120 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[120 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[240 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[240 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm_cvtepi8_epi16(p_llrProcBuf[i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_llrProcBuf[i],8));
        ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1);
        *p_llrRes = simde_mm_packs_epi16(ymmRes0, ymmRes1);
        p_llrRes++;
   }
  M = (3*Z + 15)>>4;
  p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [9984];
  p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [5760];
  p_llrRes        = (simde__m128i*) &llrRes       [5760];
        for (int i=0;i<M;i++) {
        ymmRes0 = simde_mm_cvtepi8_epi16(p_bnProcBuf [i]);
        ymmRes1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf [i],8));
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[72 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[72 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[144 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[144 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[216 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[216 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm_cvtepi8_epi16(p_llrProcBuf[i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_llrProcBuf[i],8));
        ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1);
        *p_llrRes = simde_mm_packs_epi16(ymmRes0, ymmRes1);
        p_llrRes++;
   }
  M = (7*Z + 15)>>4;
  p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [14592];
  p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [6912];
  p_llrRes        = (simde__m128i*) &llrRes       [6912];
        for (int i=0;i<M;i++) {
        ymmRes0 = simde_mm_cvtepi8_epi16(p_bnProcBuf [i]);
        ymmRes1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf [i],8));
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[168 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[168 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[336 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[336 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[504 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[504 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[672 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[672 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm_cvtepi8_epi16(p_llrProcBuf[i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_llrProcBuf[i],8));
        ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1);
        *p_llrRes = simde_mm_packs_epi16(ymmRes0, ymmRes1);
        p_llrRes++;
   }
  M = (8*Z + 15)>>4;
  p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [28032];
  p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [9600];
  p_llrRes        = (simde__m128i*) &llrRes       [9600];
        for (int i=0;i<M;i++) {
        ymmRes0 = simde_mm_cvtepi8_epi16(p_bnProcBuf [i]);
        ymmRes1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf [i],8));
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[192 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[192 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[384 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[384 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[576 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[576 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[768 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[768 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[960 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[960 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm_cvtepi8_epi16(p_llrProcBuf[i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_llrProcBuf[i],8));
        ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1);
        *p_llrRes = simde_mm_packs_epi16(ymmRes0, ymmRes1);
        p_llrRes++;
   }
  M = (1*Z + 15)>>4;
  p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [46464];
  p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [12672];
  p_llrRes        = (simde__m128i*) &llrRes       [12672];
        for (int i=0;i<M;i++) {
        ymmRes0 = simde_mm_cvtepi8_epi16(p_bnProcBuf [i]);
        ymmRes1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf [i],8));
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[24 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[24 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[48 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[48 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[72 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[72 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[96 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[96 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[120 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[120 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[144 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[144 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[168 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[168 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[192 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[192 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[216 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[216 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[240 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[240 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm_cvtepi8_epi16(p_llrProcBuf[i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_llrProcBuf[i],8));
        ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1);
        *p_llrRes = simde_mm_packs_epi16(ymmRes0, ymmRes1);
        p_llrRes++;
   }
  M = (1*Z + 15)>>4;
  p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [50688];
  p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [13056];
  p_llrRes        = (simde__m128i*) &llrRes       [13056];
        for (int i=0;i<M;i++) {
        ymmRes0 = simde_mm_cvtepi8_epi16(p_bnProcBuf [i]);
        ymmRes1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf [i],8));
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[24 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[24 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[48 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[48 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[72 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[72 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[96 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[96 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[120 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[120 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[144 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[144 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[168 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[168 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[192 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[192 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[216 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[216 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[240 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[240 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[264 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[264 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm_cvtepi8_epi16(p_llrProcBuf[i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_llrProcBuf[i],8));
        ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1);
        *p_llrRes = simde_mm_packs_epi16(ymmRes0, ymmRes1);
        p_llrRes++;
   }
}
