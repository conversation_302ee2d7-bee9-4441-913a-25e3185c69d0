#include <stdint.h>
#include "PHY/sse_intrin.h"
static inline void nrLDPC_bnProcPc_BG2_R15_128(int8_t* bnProcBuf,int8_t* bnProcBufRes,int8_t* llrRes ,  int8_t* llrProc<PERSON>uf, uint16_t Z  ) {
  // Process group with 1 CN
        uint32_t M = (38*Z + 15)>>4;
        simde__m128i* p_bnProcBuf    = (simde__m128i*) &bnProcBuf    [0];
        simde__m128i* p_bnProcBufRes = (simde__m128i*) &bnProcBufRes [0];
        simde__m128i* p_llrProcBuf   = (simde__m128i*) &llrProcBuf   [0];
        simde__m128i* p_llrRes       = (simde__m128i*) &llrRes       [0];
        simde__m128i ymm0, ymm1, ymmRes0, ymmRes1;
        for (int i=0;i<M;i++) {
          p_bnProcBufRes[i] = p_llrProcBuf[i];
          ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf [i]);
          ymm1 = simde_mm_cvtepi8_epi16(p_llrProcBuf[i]);
          ymmRes0 = simde_mm_adds_epi16(ymm0, ymm1);
          ymm0 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf [i],8));
          ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_llrProcBuf[i],8));
          ymmRes1 = simde_mm_adds_epi16(ymm0, ymm1);
          *p_llrRes = simde_mm_packs_epi16(ymmRes0, ymmRes1);
          p_llrRes++;
        }
  M = (2*Z + 15)>>4;
  p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [14592];
  p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [14592];
  p_llrRes        = (simde__m128i*) &llrRes       [14592];
        for (int i=0;i<M;i++) {
        ymmRes0 = simde_mm_cvtepi8_epi16(p_bnProcBuf [i]);
        ymmRes1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf [i],8));
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[48 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[48 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[96 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[96 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[144 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[144 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[192 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[192 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm_cvtepi8_epi16(p_llrProcBuf[i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_llrProcBuf[i],8));
        ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1);
        *p_llrRes = simde_mm_packs_epi16(ymmRes0, ymmRes1);
        p_llrRes++;
   }
  M = (1*Z + 15)>>4;
  p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [18432];
  p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [15360];
  p_llrRes        = (simde__m128i*) &llrRes       [15360];
        for (int i=0;i<M;i++) {
        ymmRes0 = simde_mm_cvtepi8_epi16(p_bnProcBuf [i]);
        ymmRes1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf [i],8));
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[24 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[24 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[48 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[48 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[72 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[72 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[96 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[96 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[120 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[120 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm_cvtepi8_epi16(p_llrProcBuf[i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_llrProcBuf[i],8));
        ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1);
        *p_llrRes = simde_mm_packs_epi16(ymmRes0, ymmRes1);
        p_llrRes++;
   }
  M = (1*Z + 15)>>4;
  p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [20736];
  p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [15744];
  p_llrRes        = (simde__m128i*) &llrRes       [15744];
        for (int i=0;i<M;i++) {
        ymmRes0 = simde_mm_cvtepi8_epi16(p_bnProcBuf [i]);
        ymmRes1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf [i],8));
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[24 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[24 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[48 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[48 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[72 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[72 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[96 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[96 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[120 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[120 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[144 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[144 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm_cvtepi8_epi16(p_llrProcBuf[i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_llrProcBuf[i],8));
        ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1);
        *p_llrRes = simde_mm_packs_epi16(ymmRes0, ymmRes1);
        p_llrRes++;
   }
  M = (1*Z + 15)>>4;
  p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [23424];
  p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [16128];
  p_llrRes        = (simde__m128i*) &llrRes       [16128];
        for (int i=0;i<M;i++) {
        ymmRes0 = simde_mm_cvtepi8_epi16(p_bnProcBuf [i]);
        ymmRes1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf [i],8));
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[24 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[24 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[48 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[48 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[72 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[72 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[96 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[96 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[120 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[120 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[144 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[144 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[168 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[168 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm_cvtepi8_epi16(p_llrProcBuf[i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_llrProcBuf[i],8));
        ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1);
        *p_llrRes = simde_mm_packs_epi16(ymmRes0, ymmRes1);
        p_llrRes++;
   }
  M = (2*Z + 15)>>4;
  p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [26496];
  p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [16512];
  p_llrRes        = (simde__m128i*) &llrRes       [16512];
        for (int i=0;i<M;i++) {
        ymmRes0 = simde_mm_cvtepi8_epi16(p_bnProcBuf [i]);
        ymmRes1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf [i],8));
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[48 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[48 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[96 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[96 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[144 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[144 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[192 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[192 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[240 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[240 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[288 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[288 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[336 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[336 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[384 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[384 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm_cvtepi8_epi16(p_llrProcBuf[i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_llrProcBuf[i],8));
        ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1);
        *p_llrRes = simde_mm_packs_epi16(ymmRes0, ymmRes1);
        p_llrRes++;
   }
  M = (1*Z + 15)>>4;
  p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [33408];
  p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [17280];
  p_llrRes        = (simde__m128i*) &llrRes       [17280];
        for (int i=0;i<M;i++) {
        ymmRes0 = simde_mm_cvtepi8_epi16(p_bnProcBuf [i]);
        ymmRes1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf [i],8));
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[24 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[24 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[48 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[48 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[72 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[72 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[96 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[96 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[120 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[120 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[144 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[144 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[168 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[168 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[192 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[192 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[216 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[216 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm_cvtepi8_epi16(p_llrProcBuf[i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_llrProcBuf[i],8));
        ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1);
        *p_llrRes = simde_mm_packs_epi16(ymmRes0, ymmRes1);
        p_llrRes++;
   }
  M = (1*Z + 15)>>4;
  p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [37248];
  p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [17664];
  p_llrRes        = (simde__m128i*) &llrRes       [17664];
        for (int i=0;i<M;i++) {
        ymmRes0 = simde_mm_cvtepi8_epi16(p_bnProcBuf [i]);
        ymmRes1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf [i],8));
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[24 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[24 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[48 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[48 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[72 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[72 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[96 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[96 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[120 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[120 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[144 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[144 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[168 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[168 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[192 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[192 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[216 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[216 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[240 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[240 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[264 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[264 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm_cvtepi8_epi16(p_llrProcBuf[i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_llrProcBuf[i],8));
        ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1);
        *p_llrRes = simde_mm_packs_epi16(ymmRes0, ymmRes1);
        p_llrRes++;
   }
  M = (1*Z + 15)>>4;
  p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [41856];
  p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [18048];
  p_llrRes        = (simde__m128i*) &llrRes       [18048];
        for (int i=0;i<M;i++) {
        ymmRes0 = simde_mm_cvtepi8_epi16(p_bnProcBuf [i]);
        ymmRes1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf [i],8));
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[24 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[24 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[48 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[48 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[72 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[72 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[96 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[96 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[120 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[120 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[144 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[144 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[168 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[168 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[192 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[192 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[216 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[216 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[240 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[240 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[264 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[264 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[288 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[288 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm_cvtepi8_epi16(p_llrProcBuf[i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_llrProcBuf[i],8));
        ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1);
        *p_llrRes = simde_mm_packs_epi16(ymmRes0, ymmRes1);
        p_llrRes++;
   }
  M = (1*Z + 15)>>4;
  p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [46848];
  p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [18432];
  p_llrRes        = (simde__m128i*) &llrRes       [18432];
        for (int i=0;i<M;i++) {
        ymmRes0 = simde_mm_cvtepi8_epi16(p_bnProcBuf [i]);
        ymmRes1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf [i],8));
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[24 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[24 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[48 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[48 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[72 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[72 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[96 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[96 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[120 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[120 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[144 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[144 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[168 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[168 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[192 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[192 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[216 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[216 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[240 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[240 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[264 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[264 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[288 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[288 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[312 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[312 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm_cvtepi8_epi16(p_llrProcBuf[i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_llrProcBuf[i],8));
        ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1);
        *p_llrRes = simde_mm_packs_epi16(ymmRes0, ymmRes1);
        p_llrRes++;
   }
  M = (1*Z + 15)>>4;
  p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [52224];
  p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [18816];
  p_llrRes        = (simde__m128i*) &llrRes       [18816];
        for (int i=0;i<M;i++) {
        ymmRes0 = simde_mm_cvtepi8_epi16(p_bnProcBuf [i]);
        ymmRes1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf [i],8));
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[24 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[24 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[48 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[48 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[72 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[72 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[96 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[96 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[120 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[120 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[144 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[144 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[168 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[168 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[192 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[192 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[216 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[216 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[240 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[240 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[264 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[264 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[288 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[288 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[312 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[312 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[336 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[336 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[360 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[360 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm_cvtepi8_epi16(p_llrProcBuf[i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_llrProcBuf[i],8));
        ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1);
        *p_llrRes = simde_mm_packs_epi16(ymmRes0, ymmRes1);
        p_llrRes++;
   }
  M = (1*Z + 15)>>4;
  p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [58368];
  p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [19200];
  p_llrRes        = (simde__m128i*) &llrRes       [19200];
        for (int i=0;i<M;i++) {
        ymmRes0 = simde_mm_cvtepi8_epi16(p_bnProcBuf [i]);
        ymmRes1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf [i],8));
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[24 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[24 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[48 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[48 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[72 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[72 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[96 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[96 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[120 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[120 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[144 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[144 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[168 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[168 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[192 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[192 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[216 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[216 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[240 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[240 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[264 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[264 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[288 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[288 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[312 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[312 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[336 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[336 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[360 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[360 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[384 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[384 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[408 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[408 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[432 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[432 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[456 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[456 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[480 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[480 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[504 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[504 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm_cvtepi8_epi16(p_llrProcBuf[i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_llrProcBuf[i],8));
        ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1);
        *p_llrRes = simde_mm_packs_epi16(ymmRes0, ymmRes1);
        p_llrRes++;
   }
  M = (1*Z + 15)>>4;
  p_bnProcBuf     = (simde__m128i*) &bnProcBuf    [66816];
  p_llrProcBuf    = (simde__m128i*) &llrProcBuf   [19584];
  p_llrRes        = (simde__m128i*) &llrRes       [19584];
        for (int i=0;i<M;i++) {
        ymmRes0 = simde_mm_cvtepi8_epi16(p_bnProcBuf [i]);
        ymmRes1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf [i],8));
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[24 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[24 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[48 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[48 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[72 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[72 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[96 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[96 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[120 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[120 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[144 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[144 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[168 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[168 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[192 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[192 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[216 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[216 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[240 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[240 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[264 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[264 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[288 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[288 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[312 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[312 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[336 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[336 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[360 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[360 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[384 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[384 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[408 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[408 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[432 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[432 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[456 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[456 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[480 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[480 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[504 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[504 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0 = simde_mm_cvtepi8_epi16(p_bnProcBuf[528 + i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1 = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_bnProcBuf[528 + i],8));
       ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1); 
        ymm0    = simde_mm_cvtepi8_epi16(p_llrProcBuf[i]);
        ymmRes0 = simde_mm_adds_epi16(ymmRes0, ymm0);
        ymm1    = simde_mm_cvtepi8_epi16(simde_mm_srli_si128(p_llrProcBuf[i],8));
        ymmRes1 = simde_mm_adds_epi16(ymmRes1, ymm1);
        *p_llrRes = simde_mm_packs_epi16(ymmRes0, ymmRes1);
        p_llrRes++;
   }
}
