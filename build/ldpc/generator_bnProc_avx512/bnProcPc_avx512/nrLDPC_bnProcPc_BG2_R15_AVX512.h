static inline void nrLDPC_bnProcPc_BG2_R15_AVX512(int8_t* bnProc<PERSON>uf,int8_t* llrRes ,  int8_t* llrProcBuf, uint16_t Z ) {
   simde__m512i zmm0,zmm1,zmmRes0,zmmRes1;  
        simde__m256i* p_bnProcBuf; 
        simde__m256i* p_llrProcBuf;
        simde__m512i* p_llrRes; 
         uint32_t M ;
// Process group with 1 CNs 
 M = (38*Z + 63)>>6;
    p_bnProcBuf     = (simde__m256i*) &bnProcBuf    [0];
    p_llrProcBuf    = (simde__m256i*) &llrProcBuf   [0];
    p_llrRes        = (simde__m512i*) &llrRes       [0];
            for (int i=0,j=0;i<M;i++,j+=2) {
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf [j]);
           zmm1 = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j]);
           zmm0    = simde_mm512_cvtepi8_epi16(p_bnProcBuf[j+1]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1    = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j +1 ]);
           zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1);
           zmm0 = simde_mm512_packs_epi16(zmmRes0,zmmRes1);
            p_llrRes[i] = simde_mm512_permutex_epi64(zmm0, 0xD8);
}
// Process group with 2 CNs 
// Process group with 3 CNs 
// Process group with 4 CNs 
// Process group with 5 CNs 
 M = (2*Z + 63)>>6;
    p_bnProcBuf     = (simde__m256i*) &bnProcBuf    [14592];
    p_llrProcBuf    = (simde__m256i*) &llrProcBuf   [14592];
    p_llrRes        = (simde__m512i*) &llrRes       [14592];
        for (int i=0,j=0;i<M;i++,j+=2) {
       zmmRes0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf [j]);
       zmmRes1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf [j +1]);
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[24 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[24 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[48 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[48 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[72 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[72 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[96 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[96 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0    = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1    = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j +1 ]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1);
       zmm0 = simde_mm512_packs_epi16(zmmRes0,zmmRes1);
            p_llrRes[i] = simde_mm512_permutex_epi64(zmm0, 0xD8);
}
// Process group with 6 CNs 
 M = (1*Z + 63)>>6;
    p_bnProcBuf     = (simde__m256i*) &bnProcBuf    [18432];
    p_llrProcBuf    = (simde__m256i*) &llrProcBuf   [15360];
    p_llrRes        = (simde__m512i*) &llrRes       [15360];
        for (int i=0,j=0;i<M;i++,j+=2) {
       zmmRes0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf [j]);
       zmmRes1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf [j +1]);
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[12 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[12 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[24 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[24 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[36 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[36 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[48 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[48 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[60 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[60 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0    = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1    = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j +1 ]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1);
       zmm0 = simde_mm512_packs_epi16(zmmRes0,zmmRes1);
            p_llrRes[i] = simde_mm512_permutex_epi64(zmm0, 0xD8);
}
// Process group with 7 CNs 
 M = (1*Z + 63)>>6;
    p_bnProcBuf     = (simde__m256i*) &bnProcBuf    [20736];
    p_llrProcBuf    = (simde__m256i*) &llrProcBuf   [15744];
    p_llrRes        = (simde__m512i*) &llrRes       [15744];
        for (int i=0,j=0;i<M;i++,j+=2) {
       zmmRes0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf [j]);
       zmmRes1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf [j +1]);
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[12 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[12 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[24 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[24 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[36 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[36 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[48 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[48 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[60 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[60 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[72 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[72 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0    = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1    = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j +1 ]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1);
       zmm0 = simde_mm512_packs_epi16(zmmRes0,zmmRes1);
            p_llrRes[i] = simde_mm512_permutex_epi64(zmm0, 0xD8);
}
// Process group with 8 CNs 
 M = (1*Z + 63)>>6;
    p_bnProcBuf     = (simde__m256i*) &bnProcBuf    [23424];
    p_llrProcBuf    = (simde__m256i*) &llrProcBuf   [16128];
    p_llrRes        = (simde__m512i*) &llrRes       [16128];
        for (int i=0,j=0;i<M;i++,j+=2) {
       zmmRes0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf [j]);
       zmmRes1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf [j +1]);
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[12 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[12 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[24 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[24 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[36 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[36 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[48 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[48 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[60 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[60 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[72 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[72 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[84 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[84 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0    = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1    = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j +1 ]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1);
       zmm0 = simde_mm512_packs_epi16(zmmRes0,zmmRes1);
            p_llrRes[i] = simde_mm512_permutex_epi64(zmm0, 0xD8);
}
// Process group with 9 CNs 
 M = (2*Z + 63)>>6;
    p_bnProcBuf     = (simde__m256i*) &bnProcBuf    [26496];
    p_llrProcBuf    = (simde__m256i*) &llrProcBuf   [16512];
    p_llrRes        = (simde__m512i*) &llrRes       [16512];
        for (int i=0,j=0;i<M;i++,j+=2) {
       zmmRes0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf [j]);
       zmmRes1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf [j +1]);
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[24 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[24 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[48 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[48 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[72 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[72 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[96 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[96 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[120 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[120 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[144 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[144 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[168 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[168 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[192 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[192 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0    = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1    = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j +1 ]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1);
       zmm0 = simde_mm512_packs_epi16(zmmRes0,zmmRes1);
            p_llrRes[i] = simde_mm512_permutex_epi64(zmm0, 0xD8);
}
// Process group with 10 CNs 
 M = (1*Z + 63)>>6;
    p_bnProcBuf     = (simde__m256i*) &bnProcBuf    [33408];
    p_llrProcBuf    = (simde__m256i*) &llrProcBuf   [17280];
    p_llrRes        = (simde__m512i*) &llrRes       [17280];
        for (int i=0,j=0;i<M;i++,j+=2) {
       zmmRes0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf [j]);
       zmmRes1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf [j +1]);
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[12 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[12 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[24 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[24 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[36 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[36 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[48 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[48 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[60 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[60 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[72 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[72 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[84 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[84 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[96 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[96 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[108 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[108 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0    = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1    = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j +1 ]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1);
       zmm0 = simde_mm512_packs_epi16(zmmRes0,zmmRes1);
            p_llrRes[i] = simde_mm512_permutex_epi64(zmm0, 0xD8);
}
// Process group with 11 CNs 
// Process group with 12 CNs 
 M = (1*Z + 63)>>6;
    p_bnProcBuf     = (simde__m256i*) &bnProcBuf    [37248];
    p_llrProcBuf    = (simde__m256i*) &llrProcBuf   [17664];
    p_llrRes        = (simde__m512i*) &llrRes       [17664];
            for (int i=0,j=0;i<M;i++,j+=2) {
           zmmRes0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf [j]);
           zmmRes1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf [j +1]);
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[12 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[12 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[24 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[24 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[36 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[36 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[48 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[48 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[60 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[60 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[72 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[72 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[84 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[84 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[96 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[96 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[108 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[108 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[120 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[120 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[132 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[132 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0    = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1    = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j +1 ]);
           zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1);
           zmm0 = simde_mm512_packs_epi16(zmmRes0,zmmRes1);
            p_llrRes[i] = simde_mm512_permutex_epi64(zmm0, 0xD8);
}
// Process group with 13 CNs 
 M = (1*Z + 63)>>6;
    p_bnProcBuf     = (simde__m256i*) &bnProcBuf    [41856];
    p_llrProcBuf    = (simde__m256i*) &llrProcBuf   [18048];
    p_llrRes        = (simde__m512i*) &llrRes       [18048];
        for (int i=0,j=0;i<M;i++,j+=2) {
       zmmRes0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf [j]);
       zmmRes1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf [j +1]);
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[12 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[12 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[24 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[24 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[36 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[36 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[48 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[48 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[60 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[60 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[72 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[72 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[84 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[84 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[96 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[96 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[108 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[108 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[120 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[120 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[132 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[132 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[144 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[144 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0    = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1    = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j +1 ]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1);
       zmm0 = simde_mm512_packs_epi16(zmmRes0,zmmRes1);
            p_llrRes[i] = simde_mm512_permutex_epi64(zmm0, 0xD8);
}
// Process group with 14 CNs 
 M = (1*Z + 63)>>6;
    p_bnProcBuf     = (simde__m256i*) &bnProcBuf    [46848];
    p_llrProcBuf    = (simde__m256i*) &llrProcBuf   [18432];
    p_llrRes        = (simde__m512i*) &llrRes       [18432];
        for (int i=0,j=0;i<M;i++,j+=2) {
       zmmRes0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf [j]);
       zmmRes1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf [j +1]);
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[12 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[12 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[24 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[24 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[36 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[36 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[48 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[48 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[60 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[60 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[72 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[72 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[84 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[84 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[96 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[96 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[108 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[108 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[120 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[120 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[132 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[132 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[144 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[144 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[156 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[156 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0    = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1    = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j +1 ]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1);
       zmm0 = simde_mm512_packs_epi16(zmmRes0,zmmRes1);
            p_llrRes[i] = simde_mm512_permutex_epi64(zmm0, 0xD8);
}
// Process group with 15 CNs 
// Process group with 16 CNs 
 M = (1*Z + 63)>>6;
    p_bnProcBuf     = (simde__m256i*) &bnProcBuf    [52224];
    p_llrProcBuf    = (simde__m256i*) &llrProcBuf   [18816];
    p_llrRes        = (simde__m512i*) &llrRes       [18816];
        for (int i=0,j=0;i<M;i++,j+=2) {
       zmmRes0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf [j]);
       zmmRes1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf [j +1]);
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[12 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[12 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[24 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[24 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[36 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[36 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[48 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[48 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[60 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[60 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[72 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[72 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[84 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[84 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[96 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[96 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[108 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[108 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[120 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[120 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[132 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[132 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[144 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[144 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[156 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[156 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[168 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[168 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[180 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[180 + j +1]);
      zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0    = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1    = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j +1 ]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1);
       zmm0 = simde_mm512_packs_epi16(zmmRes0,zmmRes1);
            p_llrRes[i] = simde_mm512_permutex_epi64(zmm0, 0xD8);
}
// Process group with 17 CNs 
// Process group with 18 CNs 
// Process group with 19 CNs 
// Process group with 20 CNs 
// Process group with 21 CNs 
// Process group with 22 CNs 
 M = (1*Z + 63)>>6;
    p_bnProcBuf     = (simde__m256i*) &bnProcBuf    [58368];
    p_llrProcBuf    = (simde__m256i*) &llrProcBuf   [19200];
    p_llrRes        = (simde__m512i*) &llrRes       [19200];
            for (int i=0,j=0;i<M;i++,j+=2) {
           zmmRes0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf [j]);
           zmmRes1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf [j +1]);
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[12 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[12 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[24 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[24 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[36 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[36 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[48 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[48 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[60 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[60 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[72 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[72 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[84 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[84 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[96 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[96 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[108 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[108 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[120 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[120 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[132 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[132 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[144 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[144 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[156 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[156 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[168 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[168 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[180 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[180 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[192 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[192 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[204 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[204 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[216 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[216 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[228 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[228 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[240 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[240 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[252 + j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[252 + j +1]);
          zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
           zmm0    = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j]);
           zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
           zmm1    = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j +1 ]);
           zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1);
           zmm0 = simde_mm512_packs_epi16(zmmRes0,zmmRes1);
            p_llrRes[i] = simde_mm512_permutex_epi64(zmm0, 0xD8);
}
// Process group with <23 CNs 
 M = (1*Z + 63)>>6;
    p_bnProcBuf     = (simde__m256i*) &bnProcBuf    [66816];
    p_llrProcBuf    = (simde__m256i*) &llrProcBuf   [19584];
    p_llrRes        = (simde__m512i*) &llrRes       [19584];
        for (int i=0,j=0;i<M;i++,j+=2) {
       zmmRes0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf [j]);
       zmmRes1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf [j +1]);
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[12 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[12 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[24 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[24 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[36 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[36 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[48 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[48 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[60 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[60 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[72 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[72 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[84 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[84 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[96 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[96 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[108 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[108 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[120 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[120 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[132 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[132 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[144 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[144 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[156 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[156 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[168 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[168 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[180 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[180 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[192 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[192 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[204 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[204 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[216 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[216 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[228 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[228 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[240 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[240 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[252 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[252 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[264 + j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1 = simde_mm512_cvtepi8_epi16(p_bnProcBuf[264 + j +1]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1); 
       zmm0    = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j]);
       zmmRes0 = simde_mm512_adds_epi16(zmmRes0,zmm0);
       zmm1    = simde_mm512_cvtepi8_epi16(p_llrProcBuf[j +1 ]);
       zmmRes1 = simde_mm512_adds_epi16(zmmRes1,zmm1);
       zmm0 = simde_mm512_packs_epi16(zmmRes0,zmmRes1);
            p_llrRes[i] = simde_mm512_permutex_epi64(zmm0, 0xD8);
}
// Process group with 24 CNs 
// Process group with 25 CNs 
// Process group with 26 CNs 
// Process group with 27 CNs 
// Process group with 28 CNs 
// Process group with 29 CNs 
// Process group with 30 CNs 
}
