#include <stdint.h>
#include "PHY/sse_intrin.h"
static inline void nrLDPC_cnProc_BG1_R23_AVX2(int8_t* cnProcBuf, int8_t* cnProcBufRes, uint16_t Z) {
//Process group with 3 BNs
                simde__m256i ymm0, min, sgn,ones,maxLLR;
                ones   = simde_mm256_set1_epi8((int8_t)1);
                maxLLR = simde_mm256_set1_epi8((int8_t)127);
                uint32_t  M;
 M = (1*Z + 31)>>5;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[12+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[24+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[0+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[0+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[24+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[12+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[0+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[12+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[24+i] = simde_mm256_sign_epi8(min, sgn);
            }
//Process group with 4 BNs
//Process group with 5 BNs
//Process group with 6 BNs
//Process group with 7 BNs
M = (3*Z + 31)>>5;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1992+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2052+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2112+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2172+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2232+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2292+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[1932+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1932+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2052+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2112+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2172+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2232+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2292+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[1992+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1932+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1992+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2112+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2172+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2232+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2292+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2052+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1932+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1992+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2052+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2172+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2232+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2292+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2112+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1932+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1992+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2052+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2112+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2232+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2292+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2172+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1932+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1992+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2052+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2112+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2172+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2292+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2232+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1932+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1992+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2052+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2112+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2172+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2232+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2292+i] = simde_mm256_sign_epi8(min, sgn);
            }
//Process group with 8 BNs
M = (2*Z + 31)>>5;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2376+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2400+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2424+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2448+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2472+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2496+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2520+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2352+i] = simde_mm256_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2352+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2400+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2424+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2448+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2472+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2496+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2520+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2376+i] = simde_mm256_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2352+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2376+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2424+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2448+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2472+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2496+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2520+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2400+i] = simde_mm256_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2352+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2376+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2400+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2448+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2472+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2496+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2520+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2424+i] = simde_mm256_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2352+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2376+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2400+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2424+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2472+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2496+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2520+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2448+i] = simde_mm256_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2352+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2376+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2400+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2424+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2448+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2496+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2520+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2472+i] = simde_mm256_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2352+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2376+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2400+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2424+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2448+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2472+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2520+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2496+i] = simde_mm256_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2352+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2376+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2400+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2424+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2448+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2472+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2496+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2520+i] = simde_mm256_sign_epi8(min, sgn);
              }
//Process group with 9 BNs
M = (2*Z + 31)>>5;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2568+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2592+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2616+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2640+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2664+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2688+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2712+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2736+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2544+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2544+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2592+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2616+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2640+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2664+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2688+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2712+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2736+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2568+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2544+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2568+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2616+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2640+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2664+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2688+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2712+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2736+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2592+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2544+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2568+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2592+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2640+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2664+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2688+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2712+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2736+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2616+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2544+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2568+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2592+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2616+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2664+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2688+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2712+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2736+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2640+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2544+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2568+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2592+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2616+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2640+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2688+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2712+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2736+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2664+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2544+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2568+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2592+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2616+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2640+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2664+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2712+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2736+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2688+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2544+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2568+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2592+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2616+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2640+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2664+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2688+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2736+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2712+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2544+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2568+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2592+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2616+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2640+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2664+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2688+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2712+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2736+i] = simde_mm256_sign_epi8(min, sgn);
            }
//Process group with 10 BNs
 M = (1*Z + 31)>>5;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2772+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2784+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2796+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2808+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2820+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2832+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2844+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2856+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2868+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2760+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2760+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2784+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2796+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2808+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2820+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2832+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2844+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2856+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2868+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2772+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2760+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2772+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2796+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2808+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2820+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2832+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2844+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2856+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2868+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2784+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2760+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2772+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2784+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2808+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2820+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2832+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2844+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2856+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2868+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2796+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2760+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2772+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2784+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2796+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2820+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2832+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2844+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2856+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2868+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2808+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2760+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2772+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2784+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2796+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2808+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2832+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2844+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2856+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2868+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2820+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2760+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2772+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2784+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2796+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2808+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2820+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2844+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2856+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2868+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2832+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2760+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2772+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2784+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2796+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2808+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2820+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2832+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2856+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2868+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2844+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2760+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2772+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2784+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2796+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2808+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2820+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2832+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2844+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2868+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2856+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2760+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2772+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2784+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2796+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2808+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2820+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2832+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2844+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2856+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2868+i] = simde_mm256_sign_epi8(min, sgn);
            }
//Process group with 19 BNs
 M = (4*Z + 31)>>5;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2928+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2976+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3024+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3072+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3120+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3168+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3216+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3264+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3312+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3360+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3408+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3456+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3504+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3552+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3600+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3648+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3696+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3744+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2880+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2880+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2976+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3024+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3072+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3120+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3168+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3216+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3264+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3312+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3360+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3408+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3456+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3504+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3552+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3600+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3648+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3696+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3744+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2928+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2880+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2928+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3024+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3072+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3120+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3168+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3216+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3264+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3312+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3360+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3408+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3456+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3504+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3552+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3600+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3648+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3696+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3744+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2976+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2880+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2928+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2976+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3072+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3120+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3168+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3216+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3264+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3312+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3360+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3408+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3456+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3504+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3552+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3600+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3648+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3696+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3744+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[3024+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2880+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2928+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2976+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3024+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3120+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3168+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3216+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3264+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3312+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3360+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3408+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3456+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3504+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3552+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3600+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3648+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3696+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3744+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[3072+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2880+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2928+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2976+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3024+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3072+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3168+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3216+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3264+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3312+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3360+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3408+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3456+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3504+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3552+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3600+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3648+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3696+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3744+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[3120+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2880+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2928+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2976+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3024+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3072+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3120+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3216+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3264+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3312+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3360+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3408+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3456+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3504+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3552+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3600+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3648+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3696+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3744+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[3168+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2880+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2928+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2976+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3024+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3072+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3120+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3168+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3264+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3312+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3360+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3408+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3456+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3504+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3552+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3600+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3648+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3696+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3744+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[3216+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2880+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2928+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2976+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3024+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3072+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3120+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3168+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3216+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3312+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3360+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3408+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3456+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3504+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3552+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3600+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3648+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3696+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3744+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[3264+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2880+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2928+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2976+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3024+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3072+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3120+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3168+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3216+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3264+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3360+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3408+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3456+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3504+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3552+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3600+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3648+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3696+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3744+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[3312+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2880+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2928+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2976+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3024+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3072+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3120+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3168+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3216+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3264+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3312+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3408+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3456+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3504+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3552+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3600+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3648+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3696+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3744+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[3360+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2880+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2928+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2976+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3024+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3072+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3120+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3168+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3216+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3264+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3312+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3360+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3456+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3504+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3552+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3600+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3648+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3696+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3744+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[3408+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2880+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2928+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2976+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3024+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3072+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3120+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3168+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3216+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3264+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3312+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3360+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3408+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3504+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3552+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3600+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3648+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3696+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3744+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[3456+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2880+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2928+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2976+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3024+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3072+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3120+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3168+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3216+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3264+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3312+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3360+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3408+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3456+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3552+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3600+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3648+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3696+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3744+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[3504+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2880+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2928+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2976+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3024+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3072+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3120+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3168+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3216+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3264+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3312+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3360+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3408+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3456+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3504+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3600+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3648+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3696+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3744+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[3552+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2880+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2928+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2976+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3024+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3072+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3120+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3168+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3216+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3264+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3312+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3360+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3408+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3456+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3504+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3552+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3648+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3696+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3744+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[3600+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2880+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2928+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2976+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3024+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3072+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3120+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3168+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3216+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3264+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3312+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3360+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3408+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3456+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3504+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3552+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3600+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3696+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3744+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[3648+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2880+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2928+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2976+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3024+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3072+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3120+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3168+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3216+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3264+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3312+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3360+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3408+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3456+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3504+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3552+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3600+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3648+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3744+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[3696+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2880+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2928+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2976+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3024+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3072+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3120+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3168+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3216+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3264+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3312+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3360+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3408+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3456+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3504+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3552+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3600+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3648+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[3696+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[3744+i] = simde_mm256_sign_epi8(min, sgn);
            }
}
