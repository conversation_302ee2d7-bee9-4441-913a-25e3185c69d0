#include <stdint.h>
#include "PHY/sse_intrin.h"
static inline void nrLDPC_cnProc_BG2_R15_AVX2(int8_t* cnProcBuf, int8_t* cnProcBufRes, uint16_t Z) {
//Process group with 3 BNs
                simde__m256i ymm0, min, sgn,ones,maxLLR;
                ones   = simde_mm256_set1_epi8((char)1);
                maxLLR = simde_mm256_set1_epi8((char)127);
                uint32_t M;
 M = (6*Z + 31)>>5;
            for (int i=0;i<M;i+=2) {
                ymm0 = ((simde__m256i*)cnProcBuf)[72+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[144+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[0+i] = simde_mm256_sign_epi8(min, sgn);
                ymm0 = ((simde__m256i*)cnProcBuf)[73+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
            }
            for (int i=0;i<M;i+=2) {
                ymm0 = ((simde__m256i*)cnProcBuf)[0+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[144+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[72+i] = simde_mm256_sign_epi8(min, sgn);
                ymm0 = ((simde__m256i*)cnProcBuf)[1+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
            }
            for (int i=0;i<M;i+=2) {
                ymm0 = ((simde__m256i*)cnProcBuf)[0+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[72+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[144+i] = simde_mm256_sign_epi8(min, sgn);
                ymm0 = ((simde__m256i*)cnProcBuf)[1+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
            }
//Process group with 4 BNs
 M = (20*Z + 31)>>5;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[456+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[696+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[936+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[216+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[216+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[696+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[936+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[456+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[216+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[456+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[936+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[696+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[216+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[456+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[696+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[936+i] = simde_mm256_sign_epi8(min, sgn);
            }
//Process group with 5 BNs
 M = (9*Z + 31)>>5;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1284+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1392+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1500+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1608+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[1176+i] = simde_mm256_sign_epi8(min, sgn);
           }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1176+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1392+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1500+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1608+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[1284+i] = simde_mm256_sign_epi8(min, sgn);
           }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1176+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1284+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1500+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1608+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[1392+i] = simde_mm256_sign_epi8(min, sgn);
           }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1176+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1284+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1392+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1608+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[1500+i] = simde_mm256_sign_epi8(min, sgn);
           }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1176+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1284+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1392+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1500+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[1608+i] = simde_mm256_sign_epi8(min, sgn);
           }
//Process group with 6 BNs
M = (3*Z + 31)>>5;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1752+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1788+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1824+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1860+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1896+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[1716+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1716+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1788+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1824+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1860+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1896+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[1752+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1716+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1752+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1824+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1860+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1896+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[1788+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1716+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1752+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1788+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1860+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1896+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[1824+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1716+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1752+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1788+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1824+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1896+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[1860+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1716+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1752+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1788+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1824+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1860+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[1896+i] = simde_mm256_sign_epi8(min, sgn);
            }
//Process group with 8 BNs
M = (2*Z + 31)>>5;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1956+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1980+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2004+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2028+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2052+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2076+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2100+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[1932+i] = simde_mm256_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1932+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1980+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2004+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2028+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2052+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2076+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2100+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[1956+i] = simde_mm256_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1932+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1956+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2004+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2028+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2052+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2076+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2100+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[1980+i] = simde_mm256_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1932+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1956+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1980+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2028+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2052+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2076+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2100+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2004+i] = simde_mm256_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1932+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1956+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1980+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2004+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2052+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2076+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2100+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2028+i] = simde_mm256_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1932+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1956+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1980+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2004+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2028+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2076+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2100+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2052+i] = simde_mm256_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1932+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1956+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1980+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2004+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2028+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2052+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2100+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2076+i] = simde_mm256_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[1932+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1956+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[1980+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2004+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2028+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2052+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2076+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2100+i] = simde_mm256_sign_epi8(min, sgn);
              }
//Process group with 10 BNs
M = (2*Z + 31)>>5;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2148+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2172+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2196+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2220+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2244+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2268+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2292+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2316+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2340+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2124+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2124+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2172+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2196+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2220+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2244+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2268+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2292+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2316+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2340+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2148+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2124+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2148+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2196+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2220+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2244+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2268+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2292+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2316+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2340+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2172+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2124+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2148+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2172+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2220+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2244+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2268+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2292+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2316+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2340+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2196+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2124+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2148+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2172+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2196+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2244+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2268+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2292+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2316+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2340+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2220+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2124+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2148+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2172+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2196+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2220+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2268+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2292+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2316+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2340+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2244+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2124+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2148+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2172+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2196+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2220+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2244+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2292+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2316+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2340+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2268+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2124+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2148+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2172+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2196+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2220+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2244+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2268+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2316+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2340+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2292+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2124+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2148+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2172+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2196+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2220+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2244+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2268+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2292+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2340+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2316+i] = simde_mm256_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m256i*)cnProcBuf)[2124+i];
                sgn  = simde_mm256_sign_epi8(ones, ymm0);
                min  = simde_mm256_abs_epi8(ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2148+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2172+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2196+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2220+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2244+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2268+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2292+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m256i*)cnProcBuf)[2316+i];
                min  = simde_mm256_min_epu8(min, simde_mm256_abs_epi8(ymm0));
                sgn  = simde_mm256_sign_epi8(sgn, ymm0);
                min = simde_mm256_min_epu8(min, maxLLR);
                ((simde__m256i*)cnProcBufRes)[2340+i] = simde_mm256_sign_epi8(min, sgn);
            }
}
