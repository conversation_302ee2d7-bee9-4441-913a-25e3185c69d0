#include <stdint.h>
#include "PHY/sse_intrin.h"
static inline void nrLDPC_cnProc_BG1_R13_128(int8_t* cnProcBuf, int8_t* cnProcBufRes, uint16_t Z) {
//Process group with 3 BNs
                simde__m128i ymm0, min, sgn,ones;
                ones   = simde_mm_set1_epi8((int8_t)1);
                uint32_t  M;
 M = (1*Z + 15)>>4;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[24+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[48+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[0+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[0+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[48+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[24+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[0+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[24+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[48+i] = simde_mm_sign_epi8(min, sgn);
            }
//Process group with 4 BNs
 M = (5*Z + 15)>>4;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[192+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[312+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[72+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[72+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[312+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[192+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[72+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[192+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[312+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[72+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[192+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[312+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[432+i] = simde_mm_sign_epi8(min, sgn);
            }
//Process group with 5 BNs
 M = (18*Z + 15)>>4;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[984+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[1416+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[1848+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[2280+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[552+i] = simde_mm_sign_epi8(min, sgn);
           }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[552+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[1416+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[1848+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[2280+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[984+i] = simde_mm_sign_epi8(min, sgn);
           }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[552+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[984+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[1848+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[2280+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[1416+i] = simde_mm_sign_epi8(min, sgn);
           }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[552+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[984+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[1416+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[2280+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[1848+i] = simde_mm_sign_epi8(min, sgn);
           }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[552+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[984+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[1416+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[1848+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[2280+i] = simde_mm_sign_epi8(min, sgn);
           }
//Process group with 6 BNs
M = (8*Z + 15)>>4;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[2904+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3096+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3288+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3480+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3672+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[2712+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[2712+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3096+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3288+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3480+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3672+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[2904+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[2712+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[2904+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3288+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3480+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3672+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[3096+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[2712+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[2904+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3096+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3480+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3672+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[3288+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[2712+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[2904+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3096+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3288+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3672+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[3480+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[2712+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[2904+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3096+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3288+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3480+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[3672+i] = simde_mm_sign_epi8(min, sgn);
            }
//Process group with 7 BNs
M = (5*Z + 15)>>4;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[3984+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4224+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4344+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4464+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4584+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[3864+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[3864+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4224+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4344+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4464+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4584+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[3984+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[3864+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3984+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4224+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4344+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4464+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4584+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[4104+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[3864+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3984+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4344+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4464+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4584+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[4224+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[3864+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3984+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4224+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4464+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4584+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[4344+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[3864+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3984+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4224+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4344+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4584+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[4464+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[3864+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3984+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4224+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4344+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4464+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[4584+i] = simde_mm_sign_epi8(min, sgn);
            }
//Process group with 8 BNs
M = (2*Z + 15)>>4;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[4752+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4800+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4848+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4896+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4944+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4992+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5040+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[4704+i] = simde_mm_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[4704+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4800+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4848+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4896+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4944+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4992+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5040+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[4752+i] = simde_mm_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[4704+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4752+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4848+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4896+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4944+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4992+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5040+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[4800+i] = simde_mm_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[4704+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4752+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4800+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4896+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4944+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4992+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5040+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[4848+i] = simde_mm_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[4704+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4752+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4800+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4848+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4944+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4992+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5040+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[4896+i] = simde_mm_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[4704+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4752+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4800+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4848+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4896+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4992+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5040+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[4944+i] = simde_mm_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[4704+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4752+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4800+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4848+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4896+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4944+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5040+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[4992+i] = simde_mm_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[4704+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4752+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4800+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4848+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4896+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4944+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4992+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5040+i] = simde_mm_sign_epi8(min, sgn);
              }
//Process group with 9 BNs
M = (2*Z + 15)>>4;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5136+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5184+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5232+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5280+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5328+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5376+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5424+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5472+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5088+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5088+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5184+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5232+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5280+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5328+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5376+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5424+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5472+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5136+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5088+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5136+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5232+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5280+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5328+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5376+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5424+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5472+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5184+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5088+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5136+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5184+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5280+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5328+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5376+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5424+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5472+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5232+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5088+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5136+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5184+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5232+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5328+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5376+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5424+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5472+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5280+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5088+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5136+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5184+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5232+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5280+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5376+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5424+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5472+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5328+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5088+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5136+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5184+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5232+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5280+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5328+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5424+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5472+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5376+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5088+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5136+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5184+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5232+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5280+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5328+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5376+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5472+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5424+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5088+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5136+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5184+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5232+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5280+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5328+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5376+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5424+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5472+i] = simde_mm_sign_epi8(min, sgn);
            }
//Process group with 10 BNs
 M = (1*Z + 15)>>4;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5544+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5568+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5592+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5616+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5640+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5664+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5688+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5712+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5736+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5520+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5520+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5568+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5592+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5616+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5640+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5664+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5688+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5712+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5736+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5544+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5520+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5544+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5592+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5616+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5640+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5664+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5688+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5712+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5736+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5568+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5520+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5544+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5568+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5616+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5640+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5664+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5688+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5712+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5736+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5592+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5520+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5544+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5568+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5592+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5640+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5664+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5688+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5712+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5736+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5616+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5520+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5544+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5568+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5592+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5616+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5664+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5688+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5712+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5736+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5640+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5520+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5544+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5568+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5592+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5616+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5640+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5688+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5712+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5736+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5664+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5520+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5544+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5568+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5592+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5616+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5640+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5664+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5712+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5736+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5688+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5520+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5544+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5568+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5592+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5616+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5640+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5664+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5688+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5736+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5712+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5520+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5544+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5568+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5592+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5616+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5640+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5664+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5688+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5712+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5736+i] = simde_mm_sign_epi8(min, sgn);
            }
//Process group with 19 BNs
 M = (4*Z + 15)>>4;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5760+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5856+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5952+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[6048+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[6144+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[6240+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[6336+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[6432+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[6528+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[6624+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[6720+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[6816+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[6912+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[7008+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[7104+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[7200+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[7296+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[7392+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[7488+i] = simde_mm_sign_epi8(min, sgn);
            }
}
