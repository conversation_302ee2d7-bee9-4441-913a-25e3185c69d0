#include <stdint.h>
#include "PHY/sse_intrin.h"
static inline void nrLDPC_cnProc_BG1_R89_128(int8_t* cnProcBuf, int8_t* cnProcBufRes, uint16_t Z) {
//Process group with 3 BNs
                simde__m128i ymm0, min, sgn,ones;
                ones   = simde_mm_set1_epi8((int8_t)1);
                uint32_t  M;
 M = (1*Z + 15)>>4;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[24+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[48+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[0+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[0+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[48+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[24+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[0+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[24+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[48+i] = simde_mm_sign_epi8(min, sgn);
            }
//Process group with 4 BNs
//Process group with 5 BNs
//Process group with 6 BNs
//Process group with 7 BNs
//Process group with 8 BNs
//Process group with 9 BNs
//Process group with 10 BNs
//Process group with 19 BNs
 M = (4*Z + 15)>>4;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5760+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5856+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[5952+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[6048+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[6144+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[6240+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[6336+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[6432+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[6528+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[6624+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[6720+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[6816+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[6912+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[7008+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[7104+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[7200+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[7296+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[7392+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[5760+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5856+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[5952+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6048+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6240+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6336+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6432+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6528+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6624+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6816+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[6912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[7392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ((simde__m128i*)cnProcBufRes)[7488+i] = simde_mm_sign_epi8(min, sgn);
            }
}
