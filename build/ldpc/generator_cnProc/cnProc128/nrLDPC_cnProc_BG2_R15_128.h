#include <stdint.h>
#include "PHY/sse_intrin.h"
static inline void nrLDPC_cnProc_BG2_R15_128(int8_t* cnProcBuf, int8_t* cnProcBufRes, uint16_t Z) {
//Process group with 3 BNs
                simde__m128i ymm0, min, sgn,ones,maxLLR;
                ones   = simde_mm_set1_epi8((char)1);
                maxLLR = simde_mm_set1_epi8((char)127);
                uint32_t M;
 M = (6*Z + 15)>>4;
            for (int i=0;i<M;i+=2) {
                ymm0 = ((simde__m128i*)cnProcBuf)[144+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[288+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[0+i] = simde_mm_sign_epi8(min, sgn);
                ymm0 = ((simde__m128i*)cnProcBuf)[145+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
            }
            for (int i=0;i<M;i+=2) {
                ymm0 = ((simde__m128i*)cnProcBuf)[0+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[288+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[144+i] = simde_mm_sign_epi8(min, sgn);
                ymm0 = ((simde__m128i*)cnProcBuf)[1+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
            }
            for (int i=0;i<M;i+=2) {
                ymm0 = ((simde__m128i*)cnProcBuf)[0+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[144+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[288+i] = simde_mm_sign_epi8(min, sgn);
                ymm0 = ((simde__m128i*)cnProcBuf)[1+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
            }
//Process group with 4 BNs
 M = (20*Z + 15)>>4;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[912+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[1392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[1872+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[432+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[432+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[1392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[1872+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[912+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[432+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[1872+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[1392+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[432+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[1392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[1872+i] = simde_mm_sign_epi8(min, sgn);
            }
//Process group with 5 BNs
 M = (9*Z + 15)>>4;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[2568+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[2784+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3000+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3216+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[2352+i] = simde_mm_sign_epi8(min, sgn);
           }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[2352+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[2784+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3000+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3216+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[2568+i] = simde_mm_sign_epi8(min, sgn);
           }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[2352+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[2568+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3000+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3216+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[2784+i] = simde_mm_sign_epi8(min, sgn);
           }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[2352+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[2568+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[2784+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3216+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[3000+i] = simde_mm_sign_epi8(min, sgn);
           }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[2352+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[2568+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[2784+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3000+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[3216+i] = simde_mm_sign_epi8(min, sgn);
           }
//Process group with 6 BNs
M = (3*Z + 15)>>4;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[3504+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3576+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3648+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3792+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[3432+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[3432+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3576+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3648+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3792+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[3504+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[3432+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3504+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3648+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3792+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[3576+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[3432+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3504+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3576+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3792+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[3648+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[3432+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3504+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3576+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3648+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3792+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[3720+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[3432+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3504+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3576+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3648+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3720+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[3792+i] = simde_mm_sign_epi8(min, sgn);
            }
//Process group with 8 BNs
M = (2*Z + 15)>>4;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[3912+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3960+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4056+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4152+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[3864+i] = simde_mm_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[3864+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3960+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4056+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4152+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[3912+i] = simde_mm_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[3864+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4056+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4152+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[3960+i] = simde_mm_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[3864+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3960+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4056+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4152+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[4008+i] = simde_mm_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[3864+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3960+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4152+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[4056+i] = simde_mm_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[3864+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3960+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4056+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4152+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[4104+i] = simde_mm_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[3864+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3960+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4056+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4200+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[4152+i] = simde_mm_sign_epi8(min, sgn);
              }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[3864+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3912+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[3960+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4008+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4056+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4104+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4152+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[4200+i] = simde_mm_sign_epi8(min, sgn);
              }
//Process group with 10 BNs
M = (2*Z + 15)>>4;
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[4296+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4344+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4440+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4536+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4584+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4632+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4680+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[4248+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[4248+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4344+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4440+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4536+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4584+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4632+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4680+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[4296+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[4248+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4440+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4536+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4584+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4632+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4680+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[4344+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[4248+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4344+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4440+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4536+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4584+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4632+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4680+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[4392+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[4248+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4344+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4536+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4584+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4632+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4680+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[4440+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[4248+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4344+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4440+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4536+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4584+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4632+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4680+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[4488+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[4248+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4344+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4440+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4584+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4632+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4680+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[4536+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[4248+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4344+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4440+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4536+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4632+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4680+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[4584+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[4248+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4344+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4440+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4536+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4584+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4680+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[4632+i] = simde_mm_sign_epi8(min, sgn);
            }
            for (int i=0;i<M;i++) {
                ymm0 = ((simde__m128i*)cnProcBuf)[4248+i];
                sgn  = simde_mm_sign_epi8(ones, ymm0);
                min  = simde_mm_abs_epi8(ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4296+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4344+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4392+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4440+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4488+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4536+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4584+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                ymm0 = ((simde__m128i*)cnProcBuf)[4632+i];
                min  = simde_mm_min_epu8(min, simde_mm_abs_epi8(ymm0));
                sgn  = simde_mm_sign_epi8(sgn, ymm0);
                min = simde_mm_min_epu8(min, maxLLR);
                ((simde__m128i*)cnProcBufRes)[4680+i] = simde_mm_sign_epi8(min, sgn);
            }
}
