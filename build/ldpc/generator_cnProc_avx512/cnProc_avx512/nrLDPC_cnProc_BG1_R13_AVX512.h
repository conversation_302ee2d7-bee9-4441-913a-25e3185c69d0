static inline void nrLDPC_cnProc_BG1_R13_AVX512(int8_t* cnProcBuf, int8_t* cnProcBufRes, uint16_t Z) {
                uint32_t M, i;
                simde__m512i zmm0, min, sgn,zeros,maxLLR, ones;
                  zeros  = simde_mm512_setzero_si512();
                  maxLLR = simde_mm512_set1_epi8((char)127);
                 ones = simde_mm512_set1_epi8((char)1);
//Process group with 3 BNs
 M = (1*Z + 63)>>6;
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[6+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[12+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[0+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[0+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[12+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[6+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[0+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[6+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[12+i] = conditional_negate(min, sgn,zeros);
            }
//Process group with 4 BNs
 M = (5*Z + 63)>>6;
            for (i=0;i<M;i++) {
              zmm0 = ((simde__m512i*)cnProcBuf)[48+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[78+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[108+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[18+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
              zmm0 = ((simde__m512i*)cnProcBuf)[18+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[78+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[108+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[48+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
              zmm0 = ((simde__m512i*)cnProcBuf)[18+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[48+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[108+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[78+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
              zmm0 = ((simde__m512i*)cnProcBuf)[18+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[48+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[78+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[108+i] = conditional_negate(min, sgn,zeros);
            }
//Process group with 5 BNs
 M = (18*Z + 63)>>6;
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[246+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[354+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[462+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[570+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[138+i] = conditional_negate(min, sgn,zeros);
           }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[138+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[354+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[462+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[570+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[246+i] = conditional_negate(min, sgn,zeros);
           }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[138+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[246+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[462+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[570+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[354+i] = conditional_negate(min, sgn,zeros);
           }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[138+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[246+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[354+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[570+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[462+i] = conditional_negate(min, sgn,zeros);
           }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[138+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[246+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[354+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[462+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[570+i] = conditional_negate(min, sgn,zeros);
           }
//Process group with 6 BNs
M = (8*Z + 63)>>6;
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[726+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[774+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[822+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[870+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[918+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[678+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[678+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[774+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[822+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[870+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[918+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[726+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[678+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[726+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[822+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[870+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[918+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[774+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[678+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[726+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[774+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[870+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[918+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[822+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[678+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[726+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[774+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[822+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[918+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[870+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[678+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[726+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[774+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[822+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[870+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[918+i] = conditional_negate(min, sgn,zeros);
            }
//Process group with 7 BNs
M = (5*Z + 63)>>6;
            for (i=0;i<M;i++) {
                zmm0= ((simde__m512i*)cnProcBuf)[996+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1026+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1056+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1086+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1116+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1146+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[966+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0= ((simde__m512i*)cnProcBuf)[966+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1026+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1056+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1086+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1116+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1146+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[996+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0= ((simde__m512i*)cnProcBuf)[966+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[996+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1056+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1086+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1116+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1146+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1026+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0= ((simde__m512i*)cnProcBuf)[966+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[996+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1026+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1086+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1116+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1146+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1056+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0= ((simde__m512i*)cnProcBuf)[966+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[996+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1026+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1056+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1116+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1146+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1086+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0= ((simde__m512i*)cnProcBuf)[966+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[996+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1026+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1056+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1086+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1146+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1116+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0= ((simde__m512i*)cnProcBuf)[966+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[996+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1026+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1056+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1086+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1116+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1146+i] = conditional_negate(min, sgn,zeros);
            }
//Process group with 8 BNs
M = (2*Z + 63)>>6;
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1188+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1200+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1212+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1224+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1236+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1248+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1260+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1176+i] = conditional_negate(min, sgn,zeros);
              }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1176+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1200+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1212+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1224+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1236+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1248+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1260+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1188+i] = conditional_negate(min, sgn,zeros);
              }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1176+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1188+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1212+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1224+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1236+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1248+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1260+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1200+i] = conditional_negate(min, sgn,zeros);
              }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1176+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1188+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1200+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1224+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1236+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1248+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1260+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1212+i] = conditional_negate(min, sgn,zeros);
              }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1176+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1188+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1200+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1212+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1236+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1248+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1260+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1224+i] = conditional_negate(min, sgn,zeros);
              }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1176+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1188+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1200+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1212+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1224+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1248+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1260+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1236+i] = conditional_negate(min, sgn,zeros);
              }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1176+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1188+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1200+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1212+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1224+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1236+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1260+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1248+i] = conditional_negate(min, sgn,zeros);
              }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1176+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1188+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1200+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1212+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1224+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1236+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1248+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1260+i] = conditional_negate(min, sgn,zeros);
              }
//Process group with 9 BNs
M = (2*Z + 63)>>6;
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1284+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1296+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1308+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1320+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1332+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1344+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1356+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1368+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1272+i] = conditional_negate(min, sgn,zeros);
              }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1272+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1296+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1308+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1320+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1332+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1344+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1356+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1368+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1284+i] = conditional_negate(min, sgn,zeros);
              }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1272+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1284+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1308+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1320+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1332+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1344+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1356+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1368+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1296+i] = conditional_negate(min, sgn,zeros);
              }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1272+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1284+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1296+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1320+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1332+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1344+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1356+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1368+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1308+i] = conditional_negate(min, sgn,zeros);
              }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1272+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1284+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1296+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1308+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1332+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1344+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1356+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1368+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1320+i] = conditional_negate(min, sgn,zeros);
              }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1272+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1284+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1296+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1308+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1320+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1344+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1356+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1368+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1332+i] = conditional_negate(min, sgn,zeros);
              }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1272+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1284+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1296+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1308+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1320+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1332+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1356+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1368+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1344+i] = conditional_negate(min, sgn,zeros);
              }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1272+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1284+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1296+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1308+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1320+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1332+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1344+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1368+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1356+i] = conditional_negate(min, sgn,zeros);
              }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1272+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1284+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1296+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1308+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1320+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1332+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1344+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1356+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1368+i] = conditional_negate(min, sgn,zeros);
              }
//Process group with 10 BNs
 M = (1*Z + 63)>>6;
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1386+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1392+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1398+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1404+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1410+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1416+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1422+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1428+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1434+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1380+i] = conditional_negate(min,sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1380+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1392+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1398+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1404+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1410+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1416+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1422+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1428+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1434+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1386+i] = conditional_negate(min,sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1380+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1386+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1398+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1404+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1410+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1416+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1422+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1428+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1434+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1392+i] = conditional_negate(min,sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1380+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1386+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1392+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1404+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1410+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1416+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1422+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1428+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1434+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1398+i] = conditional_negate(min,sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1380+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1386+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1392+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1398+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1410+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1416+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1422+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1428+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1434+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1404+i] = conditional_negate(min,sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1380+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1386+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1392+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1398+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1404+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1416+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1422+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1428+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1434+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1410+i] = conditional_negate(min,sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1380+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1386+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1392+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1398+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1404+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1410+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1422+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1428+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1434+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1416+i] = conditional_negate(min,sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1380+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1386+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1392+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1398+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1404+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1410+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1416+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1428+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1434+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1422+i] = conditional_negate(min,sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1380+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1386+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1392+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1398+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1404+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1410+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1416+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1422+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1434+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1428+i] = conditional_negate(min,sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1380+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1386+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1392+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1398+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1404+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1410+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1416+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1422+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1428+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1434+i] = conditional_negate(min,sgn,zeros);
            }
//Process group with 19 BNs
 M = (4*Z + 63)>>6;
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1464+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1488+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1512+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1536+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1560+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1584+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1608+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1632+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1656+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1680+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1704+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1728+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1752+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1776+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1800+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1824+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1848+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1872+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1440+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1440+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1488+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1512+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1536+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1560+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1584+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1608+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1632+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1656+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1680+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1704+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1728+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1752+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1776+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1800+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1824+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1848+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1872+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1464+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1440+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1464+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1512+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1536+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1560+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1584+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1608+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1632+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1656+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1680+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1704+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1728+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1752+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1776+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1800+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1824+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1848+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1872+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1488+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1440+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1464+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1488+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1536+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1560+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1584+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1608+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1632+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1656+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1680+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1704+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1728+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1752+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1776+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1800+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1824+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1848+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1872+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1512+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1440+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1464+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1488+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1512+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1560+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1584+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1608+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1632+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1656+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1680+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1704+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1728+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1752+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1776+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1800+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1824+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1848+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1872+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1536+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1440+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1464+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1488+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1512+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1536+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1584+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1608+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1632+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1656+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1680+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1704+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1728+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1752+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1776+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1800+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1824+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1848+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1872+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1560+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1440+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1464+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1488+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1512+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1536+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1560+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1608+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1632+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1656+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1680+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1704+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1728+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1752+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1776+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1800+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1824+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1848+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1872+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1584+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1440+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1464+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1488+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1512+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1536+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1560+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1584+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1632+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1656+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1680+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1704+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1728+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1752+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1776+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1800+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1824+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1848+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1872+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1608+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1440+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1464+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1488+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1512+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1536+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1560+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1584+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1608+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1656+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1680+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1704+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1728+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1752+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1776+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1800+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1824+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1848+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1872+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1632+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1440+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1464+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1488+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1512+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1536+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1560+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1584+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1608+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1632+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1680+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1704+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1728+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1752+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1776+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1800+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1824+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1848+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1872+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1656+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1440+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1464+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1488+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1512+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1536+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1560+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1584+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1608+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1632+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1656+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1704+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1728+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1752+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1776+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1800+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1824+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1848+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1872+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1680+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1440+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1464+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1488+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1512+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1536+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1560+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1584+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1608+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1632+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1656+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1680+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1728+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1752+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1776+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1800+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1824+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1848+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1872+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1704+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1440+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1464+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1488+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1512+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1536+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1560+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1584+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1608+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1632+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1656+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1680+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1704+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1752+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1776+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1800+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1824+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1848+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1872+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1728+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1440+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1464+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1488+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1512+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1536+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1560+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1584+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1608+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1632+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1656+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1680+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1704+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1728+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1776+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1800+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1824+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1848+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1872+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1752+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1440+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1464+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1488+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1512+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1536+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1560+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1584+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1608+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1632+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1656+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1680+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1704+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1728+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1752+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1800+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1824+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1848+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1872+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1776+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1440+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1464+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1488+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1512+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1536+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1560+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1584+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1608+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1632+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1656+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1680+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1704+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1728+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1752+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1776+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1824+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1848+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1872+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1800+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1440+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1464+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1488+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1512+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1536+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1560+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1584+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1608+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1632+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1656+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1680+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1704+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1728+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1752+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1776+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1800+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1848+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1872+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1824+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1440+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1464+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1488+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1512+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1536+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1560+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1584+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1608+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1632+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1656+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1680+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1704+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1728+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1752+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1776+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1800+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1824+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1872+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1848+i] = conditional_negate(min, sgn,zeros);
            }
            for (i=0;i<M;i++) {
                zmm0 = ((simde__m512i*)cnProcBuf)[1440+i];
                sgn  = simde_mm512_xor_si512(ones, zmm0);
                min  = simde_mm512_abs_epi8(zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1464+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1488+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1512+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1536+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1560+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1584+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1608+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1632+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1656+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1680+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1704+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1728+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1752+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1776+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1800+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1824+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                zmm0 = ((simde__m512i*)cnProcBuf)[1848+i];
                min  = simde_mm512_min_epu8(min, simde_mm512_abs_epi8(zmm0));
                sgn  = simde_mm512_xor_si512(sgn, zmm0);
                min = simde_mm512_min_epu8(min, maxLLR);
                ((simde__m512i*)cnProcBufRes)[1872+i] = conditional_negate(min, sgn,zeros);
            }
}
