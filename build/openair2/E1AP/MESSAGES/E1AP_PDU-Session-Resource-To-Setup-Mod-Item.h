/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-IEs"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#ifndef	_E1AP_PDU_Session_Resource_To_Setup_Mod_Item_H_
#define	_E1AP_PDU_Session_Resource_To_Setup_Mod_Item_H_


#include <asn_application.h>

/* Including external dependencies */
#include "E1AP_PDU-Session-ID.h"
#include "E1AP_PDU-Session-Type.h"
#include "E1AP_SNSSAI.h"
#include "E1AP_SecurityIndication.h"
#include "E1AP_BitRate.h"
#include "E1AP_UP-TNL-Information.h"
#include "E1AP_Inactivity-Timer.h"
#include "E1AP_DRB-To-Setup-Mod-List-NG-RAN.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct E1AP_Data_Forwarding_Information_Request;
struct E1AP_ProtocolExtensionContainer;

/* E1AP_PDU-Session-Resource-To-Setup-Mod-Item */
typedef struct E1AP_PDU_Session_Resource_To_Setup_Mod_Item {
	E1AP_PDU_Session_ID_t	 pDU_Session_ID;
	E1AP_PDU_Session_Type_t	 pDU_Session_Type;
	E1AP_SNSSAI_t	 sNSSAI;
	E1AP_SecurityIndication_t	 securityIndication;
	E1AP_BitRate_t	*pDU_Session_Resource_AMBR;	/* OPTIONAL */
	E1AP_UP_TNL_Information_t	 nG_UL_UP_TNL_Information;
	struct E1AP_Data_Forwarding_Information_Request	*pDU_Session_Data_Forwarding_Information_Request;	/* OPTIONAL */
	E1AP_Inactivity_Timer_t	*pDU_Session_Inactivity_Timer;	/* OPTIONAL */
	E1AP_DRB_To_Setup_Mod_List_NG_RAN_t	 dRB_To_Setup_Mod_List_NG_RAN;
	struct E1AP_ProtocolExtensionContainer	*iE_Extensions;	/* OPTIONAL */
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_PDU_Session_Resource_To_Setup_Mod_Item_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_E1AP_PDU_Session_Resource_To_Setup_Mod_Item;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_PDU_Session_Resource_To_Setup_Mod_Item_specs_1;
extern asn_TYPE_member_t asn_MBR_E1AP_PDU_Session_Resource_To_Setup_Mod_Item_1[10];

#ifdef __cplusplus
}
#endif

#endif	/* _E1AP_PDU_Session_Resource_To_Setup_Mod_Item_H_ */
#include <asn_internal.h>
