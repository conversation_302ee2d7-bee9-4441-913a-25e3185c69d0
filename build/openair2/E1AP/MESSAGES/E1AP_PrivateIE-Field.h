/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-Containers"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#ifndef	_E1AP_PrivateIE_Field_H_
#define	_E1AP_PrivateIE_Field_H_


#include <asn_application.h>

/* Including external dependencies */
#include "E1AP_PrivateIE-ID.h"
#include "E1AP_Criticality.h"
#include <ANY.h>
#include <asn_ioc.h>
#include <OPEN_TYPE.h>
#include <constr_CHOICE.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum E1AP_PrivateMessage_IEs__value_PR {
	E1AP_PrivateMessage_IEs__value_PR_NOTHING	/* No components present */
	
} E1AP_PrivateMessage_IEs__value_PR;

/* E1AP_PrivateIE-Field */
typedef struct E1AP_PrivateMessage_IEs {
	E1AP_PrivateIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_PrivateMessage_IEs__value {
		E1AP_PrivateMessage_IEs__value_PR present;
		union E1AP_PrivateMessage_IEs__E1AP_value_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_PrivateMessage_IEs_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_E1AP_PrivateMessage_IEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_PrivateMessage_IEs_specs_1;
extern asn_TYPE_member_t asn_MBR_E1AP_PrivateMessage_IEs_1[3];

#ifdef __cplusplus
}
#endif

#endif	/* _E1AP_PrivateIE_Field_H_ */
#include <asn_internal.h>
