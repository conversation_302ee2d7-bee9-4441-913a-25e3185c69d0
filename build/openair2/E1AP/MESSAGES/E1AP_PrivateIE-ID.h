/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-CommonDataTypes"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#ifndef	_E1AP_PrivateIE_ID_H_
#define	_E1AP_PrivateIE_ID_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include <OBJECT_IDENTIFIER.h>
#include <constr_CHOICE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum E1AP_PrivateIE_ID_PR {
	E1AP_PrivateIE_ID_PR_NOTHING,	/* No components present */
	E1AP_PrivateIE_ID_PR_local,
	E1AP_PrivateIE_ID_PR_global
} E1AP_PrivateIE_ID_PR;

/* E1AP_PrivateIE-ID */
typedef struct E1AP_PrivateIE_ID {
	E1AP_PrivateIE_ID_PR present;
	union E1AP_PrivateIE_ID_u {
		long	 local;
		OBJECT_IDENTIFIER_t	 global;
	} choice;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_PrivateIE_ID_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_E1AP_PrivateIE_ID;
extern asn_CHOICE_specifics_t asn_SPC_E1AP_PrivateIE_ID_specs_1;
extern asn_TYPE_member_t asn_MBR_E1AP_PrivateIE_ID_1[2];
extern asn_per_constraints_t asn_PER_type_E1AP_PrivateIE_ID_constr_1;

#ifdef __cplusplus
}
#endif

#endif	/* _E1AP_PrivateIE_ID_H_ */
#include <asn_internal.h>
