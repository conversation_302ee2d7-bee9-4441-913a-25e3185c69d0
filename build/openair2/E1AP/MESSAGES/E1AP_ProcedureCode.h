/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-CommonDataTypes"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#ifndef	_E1AP_ProcedureCode_H_
#define	_E1AP_ProcedureCode_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>

#ifdef __cplusplus
extern "C" {
#endif

/* E1AP_ProcedureCode */
typedef long	 E1AP_ProcedureCode_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_E1AP_ProcedureCode_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProcedureCode;
asn_struct_free_f E1AP_ProcedureCode_free;
asn_struct_print_f E1AP_ProcedureCode_print;
asn_constr_check_f E1AP_ProcedureCode_constraint;
xer_type_decoder_f E1AP_ProcedureCode_decode_xer;
xer_type_encoder_f E1AP_ProcedureCode_encode_xer;
per_type_decoder_f E1AP_ProcedureCode_decode_uper;
per_type_encoder_f E1AP_ProcedureCode_encode_uper;
per_type_decoder_f E1AP_ProcedureCode_decode_aper;
per_type_encoder_f E1AP_ProcedureCode_encode_aper;
#define E1AP_ProcedureCode_id_reset	((E1AP_ProcedureCode_t)0)
#define E1AP_ProcedureCode_id_errorIndication	((E1AP_ProcedureCode_t)1)
#define E1AP_ProcedureCode_id_privateMessage	((E1AP_ProcedureCode_t)2)
#define E1AP_ProcedureCode_id_gNB_CU_UP_E1Setup	((E1AP_ProcedureCode_t)3)
#define E1AP_ProcedureCode_id_gNB_CU_CP_E1Setup	((E1AP_ProcedureCode_t)4)
#define E1AP_ProcedureCode_id_gNB_CU_UP_ConfigurationUpdate	((E1AP_ProcedureCode_t)5)
#define E1AP_ProcedureCode_id_gNB_CU_CP_ConfigurationUpdate	((E1AP_ProcedureCode_t)6)
#define E1AP_ProcedureCode_id_e1Release	((E1AP_ProcedureCode_t)7)
#define E1AP_ProcedureCode_id_bearerContextSetup	((E1AP_ProcedureCode_t)8)
#define E1AP_ProcedureCode_id_bearerContextModification	((E1AP_ProcedureCode_t)9)
#define E1AP_ProcedureCode_id_bearerContextModificationRequired	((E1AP_ProcedureCode_t)10)
#define E1AP_ProcedureCode_id_bearerContextRelease	((E1AP_ProcedureCode_t)11)
#define E1AP_ProcedureCode_id_bearerContextReleaseRequest	((E1AP_ProcedureCode_t)12)
#define E1AP_ProcedureCode_id_bearerContextInactivityNotification	((E1AP_ProcedureCode_t)13)
#define E1AP_ProcedureCode_id_dLDataNotification	((E1AP_ProcedureCode_t)14)
#define E1AP_ProcedureCode_id_dataUsageReport	((E1AP_ProcedureCode_t)15)
#define E1AP_ProcedureCode_id_gNB_CU_UP_CounterCheck	((E1AP_ProcedureCode_t)16)
#define E1AP_ProcedureCode_id_gNB_CU_UP_StatusIndication	((E1AP_ProcedureCode_t)17)
#define E1AP_ProcedureCode_id_uLDataNotification	((E1AP_ProcedureCode_t)18)
#define E1AP_ProcedureCode_id_mRDC_DataUsageReport	((E1AP_ProcedureCode_t)19)
#define E1AP_ProcedureCode_id_TraceStart	((E1AP_ProcedureCode_t)20)
#define E1AP_ProcedureCode_id_DeactivateTrace	((E1AP_ProcedureCode_t)21)
#define E1AP_ProcedureCode_id_resourceStatusReportingInitiation	((E1AP_ProcedureCode_t)22)
#define E1AP_ProcedureCode_id_resourceStatusReporting	((E1AP_ProcedureCode_t)23)
#define E1AP_ProcedureCode_id_iAB_UPTNLAddressUpdate	((E1AP_ProcedureCode_t)24)
#define E1AP_ProcedureCode_id_CellTrafficTrace	((E1AP_ProcedureCode_t)25)
#define E1AP_ProcedureCode_id_earlyForwardingSNTransfer	((E1AP_ProcedureCode_t)26)
#define E1AP_ProcedureCode_id_gNB_CU_CPMeasurementResultsInformation	((E1AP_ProcedureCode_t)27)

#ifdef __cplusplus
}
#endif

#endif	/* _E1AP_ProcedureCode_H_ */
#include <asn_internal.h>
