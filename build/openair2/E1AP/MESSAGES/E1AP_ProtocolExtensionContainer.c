/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-Containers"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#include "E1AP_ProtocolExtensionContainer.h"

#include "E1AP_ProtocolExtensionField.h"
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P0_constr_1 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P1_constr_3 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P2_constr_5 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P3_constr_7 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P4_constr_9 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P5_constr_11 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P6_constr_13 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P7_constr_15 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P8_constr_17 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P9_constr_19 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P10_constr_21 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P11_constr_23 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P12_constr_25 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P13_constr_27 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P14_constr_29 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P15_constr_31 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P16_constr_33 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P17_constr_35 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P18_constr_37 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P19_constr_39 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P20_constr_41 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P21_constr_43 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P22_constr_45 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P23_constr_47 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P24_constr_49 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P25_constr_51 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P26_constr_53 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P27_constr_55 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P28_constr_57 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P29_constr_59 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P30_constr_61 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P31_constr_63 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P32_constr_65 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P33_constr_67 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P34_constr_69 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P35_constr_71 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P36_constr_73 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P37_constr_75 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P38_constr_77 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P39_constr_79 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P40_constr_81 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P41_constr_83 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P42_constr_85 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P43_constr_87 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P44_constr_89 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P45_constr_91 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P46_constr_93 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P47_constr_95 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P48_constr_97 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P49_constr_99 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P50_constr_101 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P51_constr_103 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P52_constr_105 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P53_constr_107 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P54_constr_109 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P55_constr_111 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P56_constr_113 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P57_constr_115 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P58_constr_117 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P59_constr_119 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P60_constr_121 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P61_constr_123 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P62_constr_125 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P63_constr_127 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P64_constr_129 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P65_constr_131 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P66_constr_133 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P67_constr_135 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P68_constr_137 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P69_constr_139 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P70_constr_141 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P71_constr_143 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P72_constr_145 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P73_constr_147 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P74_constr_149 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P75_constr_151 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P76_constr_153 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P77_constr_155 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P78_constr_157 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P79_constr_159 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P80_constr_161 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P81_constr_163 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P82_constr_165 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P83_constr_167 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P84_constr_169 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P85_constr_171 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P86_constr_173 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P87_constr_175 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P88_constr_177 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P89_constr_179 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P90_constr_181 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P91_constr_183 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P92_constr_185 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P93_constr_187 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P94_constr_189 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P95_constr_191 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P96_constr_193 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P97_constr_195 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P98_constr_197 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P99_constr_199 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P100_constr_201 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P101_constr_203 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P102_constr_205 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P103_constr_207 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P104_constr_209 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P105_constr_211 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P106_constr_213 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P107_constr_215 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P108_constr_217 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P109_constr_219 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P110_constr_221 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P111_constr_223 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P112_constr_225 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P113_constr_227 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P114_constr_229 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P115_constr_231 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P116_constr_233 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P117_constr_235 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P118_constr_237 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P119_constr_239 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P120_constr_241 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P121_constr_243 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P122_constr_245 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P123_constr_247 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P124_constr_249 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P125_constr_251 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P126_constr_253 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P127_constr_255 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P128_constr_257 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P129_constr_259 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P130_constr_261 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P131_constr_263 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P132_constr_265 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P133_constr_267 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P134_constr_269 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P135_constr_271 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P136_constr_273 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P137_constr_275 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P138_constr_277 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  1,  65535 }	/* (SIZE(1..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P0_1[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_SupportedPLMNs_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P0_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P0_specs_1 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P0),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P0, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P0 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P0_tags_1,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P0_tags_1)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P0_tags_1[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P0_tags_1,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P0_tags_1)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P0_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P0_constr_1,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P0_1,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P0_specs_1	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P1_3[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_AlternativeQoSParaSetItem_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P1_tags_3[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P1_specs_3 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P1),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P1, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P1 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P1_tags_3,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P1_tags_3)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P1_tags_3[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P1_tags_3,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P1_tags_3)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P1_tags_3[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P1_constr_3,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P1_3,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P1_specs_3	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P2_5[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_Cell_Group_Information_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P2_tags_5[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P2_specs_5 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P2),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P2, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P2 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P2_tags_5,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P2_tags_5)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P2_tags_5[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P2_tags_5,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P2_tags_5)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P2_tags_5[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P2_constr_5,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P2_5,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P2_specs_5	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P3_7[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_CriticalityDiagnostics_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P3_tags_7[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P3_specs_7 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P3),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P3, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P3 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P3_tags_7,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P3_tags_7)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P3_tags_7[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P3_tags_7,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P3_tags_7)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P3_tags_7[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P3_constr_7,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P3_7,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P3_specs_7	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P4_9[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_CriticalityDiagnostics_IE_List_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P4_tags_9[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P4_specs_9 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P4),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P4, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P4 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P4_tags_9,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P4_tags_9)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P4_tags_9[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P4_tags_9,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P4_tags_9)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P4_tags_9[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P4_constr_9,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P4_9,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P4_specs_9	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P5_11[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DAPSRequestInfo_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P5_tags_11[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P5_specs_11 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P5),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P5, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P5 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P5_tags_11,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P5_tags_11)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P5_tags_11[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P5_tags_11,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P5_tags_11)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P5_tags_11[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P5_constr_11,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P5_11,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P5_specs_11	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P6_13[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_Data_Forwarding_Information_Request_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P6_tags_13[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P6_specs_13 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P6),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P6, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P6 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P6_tags_13,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P6_tags_13)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P6_tags_13[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P6_tags_13,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P6_tags_13)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P6_tags_13[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P6_constr_13,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P6_13,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P6_specs_13	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P7_15[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_Data_Forwarding_Information_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P7_tags_15[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P7_specs_15 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P7),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P7, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P7 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P7_tags_15,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P7_tags_15)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P7_tags_15[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P7_tags_15,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P7_tags_15)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P7_tags_15[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P7_constr_15,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P7_15,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P7_specs_15	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P8_17[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DataForwardingtoE_UTRANInformationListItem_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P8_tags_17[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P8_specs_17 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P8),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P8, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P8 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P8_tags_17,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P8_tags_17)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P8_tags_17[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P8_tags_17,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P8_tags_17)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P8_tags_17[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P8_constr_17,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P8_17,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P8_specs_17	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P9_19[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_Data_Usage_per_PDU_Session_Report_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P9_tags_19[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P9_specs_19 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P9),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P9, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P9 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P9_tags_19,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P9_tags_19)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P9_tags_19[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P9_tags_19,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P9_tags_19)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P9_tags_19[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P9_constr_19,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P9_19,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P9_specs_19	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P10_21[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_Data_Usage_per_QoS_Flow_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P10_tags_21[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P10_specs_21 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P10),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P10, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P10 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P10_tags_21,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P10_tags_21)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P10_tags_21[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P10_tags_21,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P10_tags_21)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P10_tags_21[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P10_constr_21,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P10_21,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P10_specs_21	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P11_23[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_Data_Usage_Report_ItemExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P11_tags_23[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P11_specs_23 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P11),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P11, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P11 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P11_tags_23,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P11_tags_23)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P11_tags_23[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P11_tags_23,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P11_tags_23)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P11_tags_23[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P11_constr_23,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P11_23,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P11_specs_23	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P12_25[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DLDiscarding_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P12_tags_25[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P12_specs_25 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P12),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P12, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P12 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P12_tags_25,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P12_tags_25)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P12_tags_25[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P12_tags_25,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P12_tags_25)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P12_tags_25[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P12_constr_25,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P12_25,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P12_specs_25	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P13_27[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DLUPTNLAddressToUpdateItemExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P13_tags_27[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P13_specs_27 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P13),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P13, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P13 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P13_tags_27,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P13_tags_27)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P13_tags_27[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P13_tags_27,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P13_tags_27)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P13_tags_27[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P13_constr_27,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P13_27,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P13_specs_27	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P14_29[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_Activity_ItemExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P14_tags_29[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P14_specs_29 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P14),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P14, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P14 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P14_tags_29,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P14_tags_29)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P14_tags_29[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P14_tags_29,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P14_tags_29)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P14_tags_29[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P14_constr_29,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P14_29,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P14_specs_29	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P15_31[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_Confirm_Modified_Item_EUTRAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P15_tags_31[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P15_specs_31 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P15),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P15, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P15 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P15_tags_31,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P15_tags_31)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P15_tags_31[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P15_tags_31,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P15_tags_31)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P15_tags_31[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P15_constr_31,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P15_31,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P15_specs_31	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P16_33[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_Confirm_Modified_Item_NG_RAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P16_tags_33[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P16_specs_33 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P16),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P16, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P16 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P16_tags_33,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P16_tags_33)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P16_tags_33[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P16_tags_33,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P16_tags_33)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P16_tags_33[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P16_constr_33,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P16_33,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P16_specs_33	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P17_35[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_Failed_Item_EUTRAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P17_tags_35[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P17_specs_35 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P17),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P17, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P17 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P17_tags_35,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P17_tags_35)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P17_tags_35[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P17_tags_35,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P17_tags_35)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P17_tags_35[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P17_constr_35,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P17_35,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P17_specs_35	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P18_37[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_Failed_Mod_Item_EUTRAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P18_tags_37[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P18_specs_37 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P18),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P18, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P18 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P18_tags_37,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P18_tags_37)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P18_tags_37[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P18_tags_37,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P18_tags_37)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P18_tags_37[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P18_constr_37,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P18_37,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P18_specs_37	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P19_39[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_Failed_Item_NG_RAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P19_tags_39[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P19_specs_39 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P19),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P19, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P19 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P19_tags_39,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P19_tags_39)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P19_tags_39[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P19_tags_39,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P19_tags_39)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P19_tags_39[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P19_constr_39,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P19_39,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P19_specs_39	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P20_41[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_Failed_Mod_Item_NG_RAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P20_tags_41[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P20_specs_41 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P20),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P20, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P20 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P20_tags_41,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P20_tags_41)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P20_tags_41[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P20_tags_41,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P20_tags_41)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P20_tags_41[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P20_constr_41,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P20_41,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P20_specs_41	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P21_43[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_Failed_To_Modify_Item_EUTRAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P21_tags_43[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P21_specs_43 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P21),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P21, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P21 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P21_tags_43,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P21_tags_43)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P21_tags_43[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P21_tags_43,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P21_tags_43)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P21_tags_43[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P21_constr_43,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P21_43,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P21_specs_43	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P22_45[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_Failed_To_Modify_Item_NG_RAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P22_tags_45[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P22_specs_45 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P22),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P22, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P22 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P22_tags_45,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P22_tags_45)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P22_tags_45[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P22_tags_45,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P22_tags_45)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P22_tags_45[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P22_constr_45,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P22_45,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P22_specs_45	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P23_47[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_Measurement_Results_Information_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P23_tags_47[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P23_specs_47 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P23),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P23, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P23 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P23_tags_47,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P23_tags_47)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P23_tags_47[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P23_tags_47,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P23_tags_47)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P23_tags_47[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P23_constr_47,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P23_47,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P23_specs_47	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P24_49[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_Modified_Item_EUTRAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P24_tags_49[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P24_specs_49 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P24),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P24, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P24 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P24_tags_49,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P24_tags_49)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P24_tags_49[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P24_tags_49,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P24_tags_49)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P24_tags_49[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P24_constr_49,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P24_49,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P24_specs_49	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P25_51[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_Modified_Item_NG_RAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P25_tags_51[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P25_specs_51 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P25),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P25, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P25 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P25_tags_51,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P25_tags_51)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P25_tags_51[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P25_tags_51,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P25_tags_51)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P25_tags_51[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P25_constr_51,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P25_51,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P25_specs_51	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P26_53[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_Removed_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P26_tags_53[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P26_specs_53 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P26),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P26, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P26 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P26_tags_53,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P26_tags_53)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P26_tags_53[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P26_tags_53,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P26_tags_53)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P26_tags_53[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P26_constr_53,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P26_53,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P26_specs_53	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P27_55[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_Required_To_Modify_Item_EUTRAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P27_tags_55[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P27_specs_55 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P27),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P27, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P27 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P27_tags_55,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P27_tags_55)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P27_tags_55[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P27_tags_55,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P27_tags_55)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P27_tags_55[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P27_constr_55,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P27_55,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P27_specs_55	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P28_57[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_Required_To_Modify_Item_NG_RAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P28_tags_57[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P28_specs_57 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P28),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P28, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P28 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P28_tags_57,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P28_tags_57)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P28_tags_57[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P28_tags_57,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P28_tags_57)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P28_tags_57[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P28_constr_57,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P28_57,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P28_specs_57	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P29_59[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_Setup_Item_EUTRAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P29_tags_59[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P29_specs_59 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P29),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P29, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P29 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P29_tags_59,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P29_tags_59)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P29_tags_59[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P29_tags_59,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P29_tags_59)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P29_tags_59[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P29_constr_59,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P29_59,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P29_specs_59	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P30_61[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_Setup_Mod_Item_EUTRAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P30_tags_61[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P30_specs_61 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P30),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P30, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P30 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P30_tags_61,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P30_tags_61)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P30_tags_61[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P30_tags_61,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P30_tags_61)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P30_tags_61[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P30_constr_61,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P30_61,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P30_specs_61	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P31_63[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_Setup_Item_NG_RAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P31_tags_63[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P31_specs_63 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P31),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P31, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P31 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P31_tags_63,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P31_tags_63)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P31_tags_63[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P31_tags_63,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P31_tags_63)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P31_tags_63[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P31_constr_63,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P31_63,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P31_specs_63	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P32_65[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_Setup_Mod_Item_NG_RAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P32_tags_65[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P32_specs_65 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P32),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P32, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P32 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P32_tags_65,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P32_tags_65)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P32_tags_65[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P32_tags_65,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P32_tags_65)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P32_tags_65[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P32_constr_65,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P32_65,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P32_specs_65	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P33_67[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_Status_ItemExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P33_tags_67[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P33_specs_67 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P33),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P33, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P33 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P33_tags_67,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P33_tags_67)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P33_tags_67[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P33_tags_67,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P33_tags_67)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P33_tags_67[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P33_constr_67,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P33_67,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P33_specs_67	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P34_69[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRBs_Subject_To_Counter_Check_Item_EUTRAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P34_tags_69[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P34_specs_69 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P34),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P34, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P34 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P34_tags_69,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P34_tags_69)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P34_tags_69[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P34_tags_69,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P34_tags_69)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P34_tags_69[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P34_constr_69,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P34_69,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P34_specs_69	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P35_71[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRBs_Subject_To_Counter_Check_Item_NG_RAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P35_tags_71[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P35_specs_71 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P35),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P35, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P35 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P35_tags_71,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P35_tags_71)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P35_tags_71[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P35_tags_71,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P35_tags_71)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P35_tags_71[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P35_constr_71,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P35_71,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P35_specs_71	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P36_73[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRBs_Subject_To_Early_Forwarding_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P36_tags_73[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P36_specs_73 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P36),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P36, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P36 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P36_tags_73,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P36_tags_73)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P36_tags_73[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P36_tags_73,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P36_tags_73)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P36_tags_73[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P36_constr_73,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P36_73,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P36_specs_73	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P37_75[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_To_Modify_Item_EUTRAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P37_tags_75[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P37_specs_75 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P37),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P37, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P37 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P37_tags_75,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P37_tags_75)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P37_tags_75[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P37_tags_75,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P37_tags_75)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P37_tags_75[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P37_constr_75,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P37_75,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P37_specs_75	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P38_77[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_To_Modify_Item_NG_RAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P38_tags_77[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P38_specs_77 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P38),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P38, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P38 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P38_tags_77,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P38_tags_77)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P38_tags_77[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P38_tags_77,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P38_tags_77)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P38_tags_77[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P38_constr_77,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P38_77,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P38_specs_77	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P39_79[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_To_Remove_Item_EUTRAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P39_tags_79[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P39_specs_79 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P39),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P39, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P39 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P39_tags_79,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P39_tags_79)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P39_tags_79[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P39_tags_79,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P39_tags_79)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P39_tags_79[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P39_constr_79,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P39_79,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P39_specs_79	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P40_81[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_Required_To_Remove_Item_EUTRAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P40_tags_81[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P40_specs_81 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P40),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P40, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P40 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P40_tags_81,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P40_tags_81)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P40_tags_81[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P40_tags_81,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P40_tags_81)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P40_tags_81[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P40_constr_81,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P40_81,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P40_specs_81	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P41_83[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_To_Remove_Item_NG_RAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P41_tags_83[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P41_specs_83 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P41),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P41, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P41 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P41_tags_83,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P41_tags_83)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P41_tags_83[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P41_tags_83,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P41_tags_83)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P41_tags_83[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P41_constr_83,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P41_83,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P41_specs_83	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P42_85[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_Required_To_Remove_Item_NG_RAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P42_tags_85[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P42_specs_85 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P42),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P42, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P42 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P42_tags_85,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P42_tags_85)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P42_tags_85[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P42_tags_85,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P42_tags_85)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P42_tags_85[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P42_constr_85,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P42_85,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P42_specs_85	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P43_87[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_To_Setup_Item_EUTRAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P43_tags_87[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P43_specs_87 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P43),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P43, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P43 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P43_tags_87,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P43_tags_87)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P43_tags_87[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P43_tags_87,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P43_tags_87)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P43_tags_87[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P43_constr_87,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P43_87,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P43_specs_87	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P44_89[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_To_Setup_Mod_Item_EUTRAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P44_tags_89[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P44_specs_89 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P44),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P44, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P44 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P44_tags_89,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P44_tags_89)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P44_tags_89[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P44_tags_89,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P44_tags_89)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P44_tags_89[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P44_constr_89,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P44_89,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P44_specs_89	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P45_91[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_To_Setup_Item_NG_RAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P45_tags_91[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P45_specs_91 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P45),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P45, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P45 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P45_tags_91,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P45_tags_91)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P45_tags_91[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P45_tags_91,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P45_tags_91)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P45_tags_91[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P45_constr_91,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P45_91,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P45_specs_91	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P46_93[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_To_Setup_Mod_Item_NG_RAN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P46_tags_93[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P46_specs_93 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P46),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P46, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P46 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P46_tags_93,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P46_tags_93)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P46_tags_93[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P46_tags_93,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P46_tags_93)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P46_tags_93[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P46_constr_93,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P46_93,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P46_specs_93	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P47_95[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRB_Usage_Report_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P47_tags_95[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P47_specs_95 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P47),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P47, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P47 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P47_tags_95,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P47_tags_95)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P47_tags_95[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P47_tags_95,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P47_tags_95)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P47_tags_95[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P47_constr_95,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P47_95,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P47_specs_95	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P48_97[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_Dynamic5QIDescriptor_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P48_tags_97[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P48_specs_97 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P48),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P48, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P48 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P48_tags_97,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P48_tags_97)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P48_tags_97[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P48_tags_97,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P48_tags_97)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P48_tags_97[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P48_constr_97,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P48_97,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P48_specs_97	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P49_99[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_EHC_Common_Parameters_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P49_tags_99[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P49_specs_99 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P49),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P49, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P49 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P49_tags_99,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P49_tags_99)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P49_tags_99[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P49_tags_99,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P49_tags_99)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P49_tags_99[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P49_constr_99,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P49_99,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P49_specs_99	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P50_101[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_EHC_Downlink_Parameters_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P50_tags_101[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P50_specs_101 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P50),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P50, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P50 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P50_tags_101,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P50_tags_101)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P50_tags_101[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P50_tags_101,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P50_tags_101)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P50_tags_101[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P50_constr_101,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P50_101,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P50_specs_101	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P51_103[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_EHC_Uplink_Parameters_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P51_tags_103[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P51_specs_103 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P51),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P51, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P51 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P51_tags_103,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P51_tags_103)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P51_tags_103[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P51_tags_103,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P51_tags_103)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P51_tags_103[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P51_constr_103,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P51_103,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P51_specs_103	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P52_105[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_EHC_Parameters_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P52_tags_105[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P52_specs_105 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P52),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P52, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P52 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P52_tags_105,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P52_tags_105)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P52_tags_105[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P52_tags_105,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P52_tags_105)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P52_tags_105[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P52_constr_105,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P52_105,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P52_specs_105	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P53_107[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_Endpoint_IP_address_and_port_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P53_tags_107[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P53_specs_107 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P53),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P53, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P53 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P53_tags_107,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P53_tags_107)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P53_tags_107[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P53_tags_107,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P53_tags_107)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P53_tags_107[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P53_constr_107,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P53_107,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P53_specs_107	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P54_109[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_EUTRANAllocationAndRetentionPriority_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P54_tags_109[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P54_specs_109 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P54),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P54, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P54 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P54_tags_109,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P54_tags_109)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P54_tags_109[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P54_tags_109,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P54_tags_109)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P54_tags_109[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P54_constr_109,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P54_109,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P54_specs_109	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P55_111[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_EUTRAN_QoS_Support_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P55_tags_111[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P55_specs_111 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P55),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P55, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P55 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P55_tags_111,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P55_tags_111)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P55_tags_111[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P55_tags_111,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P55_tags_111)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P55_tags_111[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P55_constr_111,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P55_111,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P55_specs_111	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P56_113[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_EUTRAN_QoS_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P56_tags_113[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P56_specs_113 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P56),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P56, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P56 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P56_tags_113,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P56_tags_113)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P56_tags_113[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P56_tags_113,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P56_tags_113)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P56_tags_113[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P56_constr_113,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P56_113,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P56_specs_113	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P57_115[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_FirstDLCount_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P57_tags_115[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P57_specs_115 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P57),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P57, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P57 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P57_tags_115,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P57_tags_115)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P57_tags_115[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P57_tags_115,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P57_tags_115)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P57_tags_115[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P57_constr_115,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P57_115,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P57_specs_115	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P58_117[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_Extended_GNB_CU_CP_Name_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P58_tags_117[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P58_specs_117 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P58),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P58, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P58 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P58_tags_117,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P58_tags_117)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P58_tags_117[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P58_tags_117,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P58_tags_117)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P58_tags_117[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P58_constr_117,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P58_117,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P58_specs_117	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P59_119[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_UP_CellGroupRelatedConfiguration_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P59_tags_119[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P59_specs_119 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P59),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P59, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P59 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P59_tags_119,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P59_tags_119)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P59_tags_119[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P59_tags_119,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P59_tags_119)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P59_tags_119[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P59_constr_119,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P59_119,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P59_specs_119	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P60_121[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_Extended_GNB_CU_UP_Name_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P60_tags_121[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P60_specs_121 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P60),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P60, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P60 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P60_tags_121,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P60_tags_121)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P60_tags_121[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P60_tags_121,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P60_tags_121)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P60_tags_121[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P60_constr_121,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P60_121,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P60_specs_121	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P61_123[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_CP_TNLA_Setup_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P61_tags_123[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P61_specs_123 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P61),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P61, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P61 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P61_tags_123,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P61_tags_123)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P61_tags_123[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P61_tags_123,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P61_tags_123)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P61_tags_123[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P61_constr_123,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P61_123,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P61_specs_123	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P62_125[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_CP_TNLA_Failed_To_Setup_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P62_tags_125[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P62_specs_125 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P62),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P62, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P62 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P62_tags_125,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P62_tags_125)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P62_tags_125[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P62_tags_125,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P62_tags_125)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P62_tags_125[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P62_constr_125,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P62_125,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P62_specs_125	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P63_127[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_CP_TNLA_To_Add_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P63_tags_127[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P63_specs_127 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P63),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P63, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P63 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P63_tags_127,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P63_tags_127)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P63_tags_127[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P63_tags_127,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P63_tags_127)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P63_tags_127[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P63_constr_127,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P63_127,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P63_specs_127	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P64_129[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_CP_TNLA_To_Remove_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P64_tags_129[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P64_specs_129 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P64),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P64, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P64 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P64_tags_129,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P64_tags_129)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P64_tags_129[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P64_tags_129,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P64_tags_129)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P64_tags_129[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P64_constr_129,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P64_129,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P64_specs_129	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P65_131[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_CP_TNLA_To_Update_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P65_tags_131[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P65_specs_131 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P65),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P65, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P65 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P65_tags_131,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P65_tags_131)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P65_tags_131[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P65_tags_131,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P65_tags_131)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P65_tags_131[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P65_constr_131,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P65_131,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P65_specs_131	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P66_133[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_UP_TNLA_To_Remove_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P66_tags_133[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P66_specs_133 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P66),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P66, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P66 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P66_tags_133,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P66_tags_133)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P66_tags_133[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P66_tags_133,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P66_tags_133)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P66_tags_133[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P66_constr_133,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P66_133,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P66_specs_133	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P67_135[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GBR_QosInformation_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P67_tags_135[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P67_specs_135 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P67),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P67, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P67 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P67_tags_135,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P67_tags_135)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P67_tags_135[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P67_tags_135,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P67_tags_135)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P67_tags_135[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P67_constr_135,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P67_135,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P67_specs_135	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P68_137[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GBR_QosFlowInformation_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P68_tags_137[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P68_specs_137 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P68),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P68, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P68 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P68_tags_137,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P68_tags_137)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P68_tags_137[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P68_tags_137,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P68_tags_137)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P68_tags_137[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P68_constr_137,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P68_137,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P68_specs_137	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P69_139[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GTPTLA_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P69_tags_139[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P69_specs_139 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P69),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P69, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P69 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P69_tags_139,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P69_tags_139)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P69_tags_139[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P69_tags_139,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P69_tags_139)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P69_tags_139[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P69_constr_139,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P69_139,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P69_specs_139	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P70_141[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GTPTunnel_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P70_tags_141[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P70_specs_141 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P70),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P70, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P70 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P70_tags_141,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P70_tags_141)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P70_tags_141[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P70_tags_141,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P70_tags_141)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P70_tags_141[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P70_constr_141,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P70_141,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P70_specs_141	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P71_143[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_HW_CapacityIndicator_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P71_tags_143[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P71_specs_143 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P71),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P71, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P71 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P71_tags_143,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P71_tags_143)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P71_tags_143[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P71_tags_143,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P71_tags_143)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P71_tags_143[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P71_constr_143,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P71_143,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P71_specs_143	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P72_145[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_ImmediateMDT_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P72_tags_145[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P72_specs_145 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P72),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P72, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P72 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P72_tags_145,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P72_tags_145)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P72_tags_145[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P72_tags_145,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P72_tags_145)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P72_tags_145[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P72_constr_145,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P72_145,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P72_specs_145	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P73_147[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_MaximumIPdatarate_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P73_tags_147[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P73_specs_147 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P73),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P73, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P73 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P73_tags_147,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P73_tags_147)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P73_tags_147[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P73_tags_147,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P73_tags_147)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P73_tags_147[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P73_constr_147,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P73_147,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P73_specs_147	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P74_149[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_MRDC_Data_Usage_Report_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P74_tags_149[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P74_specs_149 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P74),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P74, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P74 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P74_tags_149,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P74_tags_149)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P74_tags_149[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P74_tags_149,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P74_tags_149)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P74_tags_149[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P74_constr_149,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P74_149,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P74_specs_149	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P75_151[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_MRDC_Usage_Information_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P75_tags_151[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P75_specs_151 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P75),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P75, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P75 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P75_tags_151,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P75_tags_151)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P75_tags_151[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P75_tags_151,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P75_tags_151)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P75_tags_151[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P75_constr_151,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P75_151,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P75_specs_151	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P76_153[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_M4Configuration_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P76_tags_153[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P76_specs_153 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P76),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P76, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P76 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P76_tags_153,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P76_tags_153)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P76_tags_153[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P76_tags_153,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P76_tags_153)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P76_tags_153[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P76_constr_153,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P76_153,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P76_specs_153	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P77_155[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_M6Configuration_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P77_tags_155[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P77_specs_155 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P77),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P77, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P77 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P77_tags_155,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P77_tags_155)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P77_tags_155[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P77_tags_155,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P77_tags_155)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P77_tags_155[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P77_constr_155,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P77_155,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P77_specs_155	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P78_157[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_M7Configuration_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P78_tags_157[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P78_specs_157 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P78),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P78, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P78 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P78_tags_157,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P78_tags_157)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P78_tags_157[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P78_tags_157,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P78_tags_157)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P78_tags_157[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P78_constr_157,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P78_157,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P78_specs_157	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P79_159[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_MDT_Configuration_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P79_tags_159[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P79_specs_159 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P79),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P79, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P79 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P79_tags_159,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P79_tags_159)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P79_tags_159[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P79_tags_159,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P79_tags_159)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P79_tags_159[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P79_constr_159,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P79_159,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P79_specs_159	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P80_161[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_NGRANAllocationAndRetentionPriority_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P80_tags_161[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P80_specs_161 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P80),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P80, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P80 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P80_tags_161,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P80_tags_161)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P80_tags_161[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P80_tags_161,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P80_tags_161)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P80_tags_161[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P80_constr_161,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P80_161,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P80_specs_161	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P81_163[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_NG_RAN_QoS_Support_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P81_tags_163[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P81_specs_163 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P81),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P81, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P81 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P81_tags_163,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P81_tags_163)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P81_tags_163[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P81_tags_163,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P81_tags_163)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P81_tags_163[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P81_constr_163,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P81_163,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P81_specs_163	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P82_165[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_Non_Dynamic5QIDescriptor_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P82_tags_165[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P82_specs_165 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P82),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P82, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P82 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P82_tags_165,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P82_tags_165)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P82_tags_165[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P82_tags_165,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P82_tags_165)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P82_tags_165[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P82_constr_165,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P82_165,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P82_specs_165	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P83_167[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_NPNSupportInfo_SNPN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P83_tags_167[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P83_specs_167 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P83),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P83, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P83 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P83_tags_167,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P83_tags_167)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P83_tags_167[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P83_tags_167,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P83_tags_167)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P83_tags_167[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P83_constr_167,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P83_167,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P83_specs_167	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P84_169[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_NPNContextInfo_SNPN_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P84_tags_169[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P84_specs_169 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P84),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P84, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P84 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P84_tags_169,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P84_tags_169)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P84_tags_169[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P84_tags_169,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P84_tags_169)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P84_tags_169[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P84_constr_169,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P84_169,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P84_specs_169	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P85_171[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_NR_CGI_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P85_tags_171[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P85_specs_171 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P85),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P85, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P85 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P85_tags_171,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P85_tags_171)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P85_tags_171[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P85_tags_171,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P85_tags_171)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P85_tags_171[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P85_constr_171,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P85_171,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P85_specs_171	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P86_173[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_NR_CGI_Support_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P86_tags_173[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P86_specs_173 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P86),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P86, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P86 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P86_tags_173,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P86_tags_173)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P86_tags_173[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P86_tags_173,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P86_tags_173)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P86_tags_173[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P86_constr_173,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P86_173,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P86_specs_173	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P87_175[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_Extended_NR_CGI_Support_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P87_tags_175[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P87_specs_175 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P87),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P87, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P87 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P87_tags_175,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P87_tags_175)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P87_tags_175[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P87_tags_175,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P87_tags_175)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P87_tags_175[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P87_constr_175,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P87_175,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P87_specs_175	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P88_177[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_PacketErrorRate_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P88_tags_177[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P88_specs_177 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P88),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P88, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P88 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P88_tags_177,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P88_tags_177)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P88_tags_177[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P88_tags_177,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P88_tags_177)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P88_tags_177[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P88_constr_177,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P88_177,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P88_specs_177	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P89_179[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_PDCP_Configuration_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P89_tags_179[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P89_specs_179 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P89),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P89, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P89 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P89_tags_179,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P89_tags_179)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P89_tags_179[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P89_tags_179,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P89_tags_179)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P89_tags_179[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P89_constr_179,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P89_179,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P89_specs_179	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P90_181[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_PDCP_Count_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P90_tags_181[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P90_specs_181 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P90),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P90, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P90 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P90_tags_181,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P90_tags_181)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P90_tags_181[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P90_tags_181,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P90_tags_181)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P90_tags_181[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P90_constr_181,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P90_181,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P90_specs_181	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P91_183[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_PDU_Session_Resource_Data_Usage_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P91_tags_183[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P91_specs_183 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P91),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P91, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P91 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P91_tags_183,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P91_tags_183)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P91_tags_183[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P91_tags_183,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P91_tags_183)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P91_tags_183[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P91_constr_183,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P91_183,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P91_specs_183	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P92_185[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_PDCP_SN_Status_Information_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P92_tags_185[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P92_specs_185 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P92),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P92, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P92 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P92_tags_185,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P92_tags_185)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P92_tags_185[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P92_tags_185,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P92_tags_185)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P92_tags_185[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P92_constr_185,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P92_185,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P92_specs_185	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P93_187[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DRBBStatusTransfer_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P93_tags_187[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P93_specs_187 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P93),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P93, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P93 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P93_tags_187,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P93_tags_187)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P93_tags_187[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P93_tags_187,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P93_tags_187)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P93_tags_187[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P93_constr_187,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P93_187,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P93_specs_187	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P94_189[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_PDU_Session_Resource_Activity_ItemExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P94_tags_189[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P94_specs_189 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P94),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P94, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P94 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P94_tags_189,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P94_tags_189)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P94_tags_189[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P94_tags_189,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P94_tags_189)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P94_tags_189[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P94_constr_189,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P94_189,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P94_specs_189	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P95_191[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_PDU_Session_Resource_Confirm_Modified_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P95_tags_191[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P95_specs_191 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P95),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P95, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P95 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P95_tags_191,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P95_tags_191)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P95_tags_191[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P95_tags_191,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P95_tags_191)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P95_tags_191[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P95_constr_191,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P95_191,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P95_specs_191	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P96_193[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_PDU_Session_Resource_Failed_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P96_tags_193[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P96_specs_193 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P96),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P96, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P96 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P96_tags_193,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P96_tags_193)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P96_tags_193[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P96_tags_193,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P96_tags_193)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P96_tags_193[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P96_constr_193,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P96_193,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P96_specs_193	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P97_195[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_PDU_Session_Resource_Failed_Mod_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P97_tags_195[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P97_specs_195 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P97),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P97, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P97 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P97_tags_195,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P97_tags_195)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P97_tags_195[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P97_tags_195,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P97_tags_195)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P97_tags_195[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P97_constr_195,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P97_195,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P97_specs_195	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P98_197[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_PDU_Session_Resource_Failed_To_Modify_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P98_tags_197[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P98_specs_197 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P98),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P98, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P98 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P98_tags_197,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P98_tags_197)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P98_tags_197[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P98_tags_197,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P98_tags_197)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P98_tags_197[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P98_constr_197,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P98_197,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P98_specs_197	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P99_199[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_PDU_Session_Resource_Modified_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P99_tags_199[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P99_specs_199 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P99),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P99, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P99 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P99_tags_199,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P99_tags_199)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P99_tags_199[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P99_tags_199,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P99_tags_199)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P99_tags_199[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P99_constr_199,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P99_199,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P99_specs_199	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P100_201[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_PDU_Session_Resource_Required_To_Modify_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P100_tags_201[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P100_specs_201 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P100),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P100, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P100 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P100_tags_201,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P100_tags_201)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P100_tags_201[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P100_tags_201,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P100_tags_201)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P100_tags_201[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P100_constr_201,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P100_201,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P100_specs_201	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P101_203[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_PDU_Session_Resource_Setup_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P101_tags_203[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P101_specs_203 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P101),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P101, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P101 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P101_tags_203,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P101_tags_203)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P101_tags_203[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P101_tags_203,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P101_tags_203)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P101_tags_203[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P101_constr_203,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P101_203,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P101_specs_203	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P102_205[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_PDU_Session_Resource_Setup_Mod_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P102_tags_205[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P102_specs_205 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P102),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P102, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P102 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P102_tags_205,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P102_tags_205)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P102_tags_205[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P102_tags_205,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P102_tags_205)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P102_tags_205[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P102_constr_205,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P102_205,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P102_specs_205	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P103_207[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_PDU_Session_Resource_To_Modify_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P103_tags_207[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P103_specs_207 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P103),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P103, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P103 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P103_tags_207,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P103_tags_207)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P103_tags_207[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P103_tags_207,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P103_tags_207)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P103_tags_207[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P103_constr_207,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P103_207,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P103_specs_207	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P104_209[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_PDU_Session_Resource_To_Remove_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P104_tags_209[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P104_specs_209 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P104),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P104, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P104 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P104_tags_209,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P104_tags_209)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P104_tags_209[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P104_tags_209,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P104_tags_209)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P104_tags_209[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P104_constr_209,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P104_209,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P104_specs_209	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P105_211[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_PDU_Session_Resource_To_Setup_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P105_tags_211[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P105_specs_211 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P105),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P105, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P105 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P105_tags_211,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P105_tags_211)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P105_tags_211[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P105_tags_211,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P105_tags_211)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P105_tags_211[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P105_constr_211,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P105_211,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P105_specs_211	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P106_213[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_PDU_Session_Resource_To_Setup_Mod_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P106_tags_213[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P106_specs_213 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P106),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P106, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P106 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P106_tags_213,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P106_tags_213)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P106_tags_213[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P106_tags_213,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P106_tags_213)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P106_tags_213[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P106_constr_213,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P106_213,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P106_specs_213	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P107_215[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_PDU_Session_To_Notify_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P107_tags_215[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P107_specs_215 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P107),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P107, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P107 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P107_tags_215,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P107_tags_215)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P107_tags_215[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P107_tags_215,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P107_tags_215)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P107_tags_215[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P107_constr_215,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P107_215,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P107_specs_215	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P108_217[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_QoS_Flow_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P108_tags_217[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P108_specs_217 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P108),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P108, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P108 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P108_tags_217,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P108_tags_217)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P108_tags_217[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P108_tags_217,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P108_tags_217)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P108_tags_217[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P108_constr_217,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P108_217,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P108_specs_217	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P109_219[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_QoS_Flow_Failed_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P109_tags_219[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P109_specs_219 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P109),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P109, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P109 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P109_tags_219,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P109_tags_219)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P109_tags_219[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P109_tags_219,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P109_tags_219)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P109_tags_219[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P109_constr_219,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P109_219,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P109_specs_219	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P110_221[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_QoS_Flow_Mapping_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P110_tags_221[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P110_specs_221 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P110),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P110, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P110 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P110_tags_221,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P110_tags_221)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P110_tags_221[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P110_tags_221,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P110_tags_221)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P110_tags_221[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P110_constr_221,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P110_221,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P110_specs_221	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P111_223[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_QoS_Parameters_Support_List_ItemExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P111_tags_223[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P111_specs_223 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P111),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P111, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P111 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P111_tags_223,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P111_tags_223)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P111_tags_223[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P111_tags_223,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P111_tags_223)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P111_tags_223[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P111_constr_223,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P111_223,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P111_specs_223	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P112_225[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_QoS_Flow_QoS_Parameter_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P112_tags_225[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P112_specs_225 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P112),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P112, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P112 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P112_tags_225,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P112_tags_225)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P112_tags_225[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P112_tags_225,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P112_tags_225)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P112_tags_225[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P112_constr_225,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P112_225,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P112_specs_225	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P113_227[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_QoSFlowLevelQoSParameters_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P113_tags_227[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P113_specs_227 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P113),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P113, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P113 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P113_tags_227,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P113_tags_227)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P113_tags_227[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P113_tags_227,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P113_tags_227)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P113_tags_227[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P113_constr_227,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P113_227,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P113_specs_227	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P114_229[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_QoS_Flow_Removed_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P114_tags_229[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P114_specs_229 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P114),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P114, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P114 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P114_tags_229,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P114_tags_229)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P114_tags_229[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P114_tags_229,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P114_tags_229)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P114_tags_229[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P114_constr_229,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P114_229,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P114_specs_229	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P115_231[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_QoS_Flows_to_be_forwarded_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P115_tags_231[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P115_specs_231 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P115),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P115, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P115 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P115_tags_231,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P115_tags_231)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P115_tags_231[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P115_tags_231,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P115_tags_231)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P115_tags_231[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P115_constr_231,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P115_231,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P115_specs_231	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P116_233[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DataForwardingtoNG_RANQoSFlowInformationList_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P116_tags_233[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P116_specs_233 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P116),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P116, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P116 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P116_tags_233,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P116_tags_233)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P116_tags_233[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P116_tags_233,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P116_tags_233)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P116_tags_233[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P116_constr_233,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P116_233,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P116_specs_233	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P117_235[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_RedundantPDUSessionInformation_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P117_tags_235[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P117_specs_235 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P117),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P117, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P117 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P117_tags_235,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P117_tags_235)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P117_tags_235[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P117_tags_235,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P117_tags_235)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P117_tags_235[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P117_constr_235,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P117_235,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P117_specs_235	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P118_237[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_ROHC_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P118_tags_237[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P118_specs_237 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P118),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P118, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P118 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P118_tags_237,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P118_tags_237)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P118_tags_237[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P118_tags_237,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P118_tags_237)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P118_tags_237[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P118_constr_237,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P118_237,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P118_specs_237	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P119_239[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_SecurityAlgorithm_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P119_tags_239[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P119_specs_239 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P119),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P119, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P119 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P119_tags_239,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P119_tags_239)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P119_tags_239[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P119_tags_239,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P119_tags_239)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P119_tags_239[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P119_constr_239,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P119_239,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P119_specs_239	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P120_241[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_SecurityIndication_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P120_tags_241[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P120_specs_241 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P120),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P120, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P120 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P120_tags_241,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P120_tags_241)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P120_tags_241[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P120_tags_241,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P120_tags_241)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P120_tags_241[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P120_constr_241,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P120_241,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P120_specs_241	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P121_243[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_SecurityInformation_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P121_tags_243[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P121_specs_243 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P121),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P121, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P121 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P121_tags_243,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P121_tags_243)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P121_tags_243[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P121_tags_243,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P121_tags_243)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P121_tags_243[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P121_constr_243,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P121_243,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P121_specs_243	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P122_245[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_SecurityResult_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P122_tags_245[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P122_specs_245 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P122),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P122, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P122 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P122_tags_245,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P122_tags_245)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P122_tags_245[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P122_tags_245,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P122_tags_245)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P122_tags_245[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P122_constr_245,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P122_245,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P122_specs_245	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P123_247[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_Slice_Support_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P123_tags_247[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P123_specs_247 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P123),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P123, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P123 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P123_tags_247,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P123_tags_247)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P123_tags_247[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P123_tags_247,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P123_tags_247)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P123_tags_247[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P123_constr_247,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P123_247,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P123_specs_247	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P124_249[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_SNSSAI_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P124_tags_249[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P124_specs_249 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P124),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P124, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P124 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P124_tags_249,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P124_tags_249)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P124_tags_249[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P124_tags_249,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P124_tags_249)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P124_tags_249[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P124_constr_249,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P124_249,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P124_specs_249	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P125_251[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_SDAP_Configuration_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P125_tags_251[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P125_specs_251 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P125),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P125, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P125 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P125_tags_251,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P125_tags_251)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P125_tags_251[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P125_tags_251,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P125_tags_251)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P125_tags_251[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P125_constr_251,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P125_251,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P125_specs_251	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P126_253[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_TNL_AvailableCapacityIndicator_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P126_tags_253[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P126_specs_253 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P126),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P126, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P126 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P126_tags_253,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P126_tags_253)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P126_tags_253[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P126_tags_253,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P126_tags_253)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P126_tags_253[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P126_constr_253,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P126_253,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P126_specs_253	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P127_255[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_TSCTrafficCharacteristics_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P127_tags_255[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P127_specs_255 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P127),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P127, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P127 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P127_tags_255,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P127_tags_255)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P127_tags_255[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P127_tags_255,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P127_tags_255)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P127_tags_255[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P127_constr_255,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P127_255,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P127_specs_255	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P128_257[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_TSCTrafficInformation_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P128_tags_257[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P128_specs_257 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P128),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P128, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P128 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P128_tags_257,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P128_tags_257)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P128_tags_257[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P128_tags_257,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P128_tags_257)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P128_tags_257[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P128_constr_257,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P128_257,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P128_specs_257	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P129_259[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_TraceActivation_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P129_tags_259[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P129_specs_259 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P129),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P129, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P129 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P129_tags_259,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P129_tags_259)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P129_tags_259[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P129_tags_259,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P129_tags_259)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P129_tags_259[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P129_constr_259,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P129_259,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P129_specs_259	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P130_261[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_T_ReorderingTimer_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P130_tags_261[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P130_specs_261 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P130),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P130, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P130 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P130_tags_261,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P130_tags_261)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P130_tags_261[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P130_tags_261,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P130_tags_261)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P130_tags_261[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P130_constr_261,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P130_261,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P130_specs_261	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P131_263[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_Transport_Layer_Address_Info_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P131_tags_263[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P131_specs_263 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P131),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P131, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P131 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P131_tags_263,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P131_tags_263)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P131_tags_263[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P131_tags_263,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P131_tags_263)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P131_tags_263[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P131_constr_263,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P131_263,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P131_specs_263	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P132_265[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_Transport_UP_Layer_Addresses_Info_To_Add_ItemExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P132_tags_265[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P132_specs_265 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P132),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P132, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P132 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P132_tags_265,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P132_tags_265)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P132_tags_265[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P132_tags_265,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P132_tags_265)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P132_tags_265[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P132_constr_265,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P132_265,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P132_specs_265	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P133_267[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_Transport_UP_Layer_Addresses_Info_To_Remove_ItemExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P133_tags_267[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P133_specs_267 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P133),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P133, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P133 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P133_tags_267,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P133_tags_267)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P133_tags_267[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P133_tags_267,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P133_tags_267)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P133_tags_267[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P133_constr_267,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P133_267,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P133_specs_267	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P134_269[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_UE_associatedLogicalE1_ConnectionItemExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P134_tags_269[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P134_specs_269 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P134),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P134, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P134 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P134_tags_269,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P134_tags_269)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P134_tags_269[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P134_tags_269,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P134_tags_269)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P134_tags_269[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P134_constr_269,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P134_269,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P134_specs_269	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P135_271[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_ULUPTNLAddressToUpdateItemExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P135_tags_271[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P135_specs_271 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P135),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P135, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P135 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P135_tags_271,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P135_tags_271)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P135_tags_271[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P135_tags_271,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P135_tags_271)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P135_tags_271[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P135_constr_271,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P135_271,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P135_specs_271	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P136_273[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_UP_Parameters_Item_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P136_tags_273[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P136_specs_273 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P136),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P136, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P136 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P136_tags_273,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P136_tags_273)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P136_tags_273[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P136_tags_273,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P136_tags_273)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P136_tags_273[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P136_constr_273,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P136_273,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P136_specs_273	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P137_275[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_UPSecuritykey_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P137_tags_275[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P137_specs_275 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P137),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P137, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P137 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P137_tags_275,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P137_tags_275)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P137_tags_275[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P137_tags_275,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P137_tags_275)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P137_tags_275[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P137_constr_275,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P137_275,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P137_specs_275	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P138_277[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_UplinkOnlyROHC_ExtIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P138_tags_277[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P138_specs_277 = {
	sizeof(struct E1AP_ProtocolExtensionContainer_4961P138),
	offsetof(struct E1AP_ProtocolExtensionContainer_4961P138, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P138 = {
	"ProtocolExtensionContainer",
	"ProtocolExtensionContainer",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P138_tags_277,
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P138_tags_277)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P138_tags_277[0]), /* 1 */
	asn_DEF_E1AP_ProtocolExtensionContainer_4961P138_tags_277,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P138_tags_277)
		/sizeof(asn_DEF_E1AP_ProtocolExtensionContainer_4961P138_tags_277[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolExtensionContainer_4961P138_constr_277,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolExtensionContainer_4961P138_277,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolExtensionContainer_4961P138_specs_277	/* Additional specs */
};

