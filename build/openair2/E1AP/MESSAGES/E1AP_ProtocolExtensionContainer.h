/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-Containers"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#ifndef	_E1AP_ProtocolExtensionContainer_H_
#define	_E1AP_ProtocolExtensionContainer_H_


#include <asn_application.h>

/* Including external dependencies */
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct E1AP_SupportedPLMNs_ExtIEs;
struct E1AP_AlternativeQoSParaSetItem_ExtIEs;
struct E1AP_Cell_Group_Information_Item_ExtIEs;
struct E1AP_CriticalityDiagnostics_ExtIEs;
struct E1AP_CriticalityDiagnostics_IE_List_ExtIEs;
struct E1AP_DAPSRequestInfo_ExtIEs;
struct E1AP_Data_Forwarding_Information_Request_ExtIEs;
struct E1AP_Data_Forwarding_Information_ExtIEs;
struct E1AP_DataForwardingtoE_UTRANInformationListItem_ExtIEs;
struct E1AP_Data_Usage_per_PDU_Session_Report_ExtIEs;
struct E1AP_Data_Usage_per_QoS_Flow_Item_ExtIEs;
struct E1AP_Data_Usage_Report_ItemExtIEs;
struct E1AP_DLDiscarding_ExtIEs;
struct E1AP_DLUPTNLAddressToUpdateItemExtIEs;
struct E1AP_DRB_Activity_ItemExtIEs;
struct E1AP_DRB_Confirm_Modified_Item_EUTRAN_ExtIEs;
struct E1AP_DRB_Confirm_Modified_Item_NG_RAN_ExtIEs;
struct E1AP_DRB_Failed_Item_EUTRAN_ExtIEs;
struct E1AP_DRB_Failed_Mod_Item_EUTRAN_ExtIEs;
struct E1AP_DRB_Failed_Item_NG_RAN_ExtIEs;
struct E1AP_DRB_Failed_Mod_Item_NG_RAN_ExtIEs;
struct E1AP_DRB_Failed_To_Modify_Item_EUTRAN_ExtIEs;
struct E1AP_DRB_Failed_To_Modify_Item_NG_RAN_ExtIEs;
struct E1AP_DRB_Measurement_Results_Information_Item_ExtIEs;
struct E1AP_DRB_Modified_Item_EUTRAN_ExtIEs;
struct E1AP_DRB_Modified_Item_NG_RAN_ExtIEs;
struct E1AP_DRB_Removed_Item_ExtIEs;
struct E1AP_DRB_Required_To_Modify_Item_EUTRAN_ExtIEs;
struct E1AP_DRB_Required_To_Modify_Item_NG_RAN_ExtIEs;
struct E1AP_DRB_Setup_Item_EUTRAN_ExtIEs;
struct E1AP_DRB_Setup_Mod_Item_EUTRAN_ExtIEs;
struct E1AP_DRB_Setup_Item_NG_RAN_ExtIEs;
struct E1AP_DRB_Setup_Mod_Item_NG_RAN_ExtIEs;
struct E1AP_DRB_Status_ItemExtIEs;
struct E1AP_DRBs_Subject_To_Counter_Check_Item_EUTRAN_ExtIEs;
struct E1AP_DRBs_Subject_To_Counter_Check_Item_NG_RAN_ExtIEs;
struct E1AP_DRBs_Subject_To_Early_Forwarding_Item_ExtIEs;
struct E1AP_DRB_To_Modify_Item_EUTRAN_ExtIEs;
struct E1AP_DRB_To_Modify_Item_NG_RAN_ExtIEs;
struct E1AP_DRB_To_Remove_Item_EUTRAN_ExtIEs;
struct E1AP_DRB_Required_To_Remove_Item_EUTRAN_ExtIEs;
struct E1AP_DRB_To_Remove_Item_NG_RAN_ExtIEs;
struct E1AP_DRB_Required_To_Remove_Item_NG_RAN_ExtIEs;
struct E1AP_DRB_To_Setup_Item_EUTRAN_ExtIEs;
struct E1AP_DRB_To_Setup_Mod_Item_EUTRAN_ExtIEs;
struct E1AP_DRB_To_Setup_Item_NG_RAN_ExtIEs;
struct E1AP_DRB_To_Setup_Mod_Item_NG_RAN_ExtIEs;
struct E1AP_DRB_Usage_Report_Item_ExtIEs;
struct E1AP_Dynamic5QIDescriptor_ExtIEs;
struct E1AP_EHC_Common_Parameters_ExtIEs;
struct E1AP_EHC_Downlink_Parameters_ExtIEs;
struct E1AP_EHC_Uplink_Parameters_ExtIEs;
struct E1AP_EHC_Parameters_ExtIEs;
struct E1AP_Endpoint_IP_address_and_port_ExtIEs;
struct E1AP_EUTRANAllocationAndRetentionPriority_ExtIEs;
struct E1AP_EUTRAN_QoS_Support_Item_ExtIEs;
struct E1AP_EUTRAN_QoS_ExtIEs;
struct E1AP_FirstDLCount_ExtIEs;
struct E1AP_Extended_GNB_CU_CP_Name_ExtIEs;
struct E1AP_GNB_CU_UP_CellGroupRelatedConfiguration_Item_ExtIEs;
struct E1AP_Extended_GNB_CU_UP_Name_ExtIEs;
struct E1AP_GNB_CU_CP_TNLA_Setup_Item_ExtIEs;
struct E1AP_GNB_CU_CP_TNLA_Failed_To_Setup_Item_ExtIEs;
struct E1AP_GNB_CU_CP_TNLA_To_Add_Item_ExtIEs;
struct E1AP_GNB_CU_CP_TNLA_To_Remove_Item_ExtIEs;
struct E1AP_GNB_CU_CP_TNLA_To_Update_Item_ExtIEs;
struct E1AP_GNB_CU_UP_TNLA_To_Remove_Item_ExtIEs;
struct E1AP_GBR_QosInformation_ExtIEs;
struct E1AP_GBR_QosFlowInformation_ExtIEs;
struct E1AP_GTPTLA_Item_ExtIEs;
struct E1AP_GTPTunnel_ExtIEs;
struct E1AP_HW_CapacityIndicator_ExtIEs;
struct E1AP_ImmediateMDT_ExtIEs;
struct E1AP_MaximumIPdatarate_ExtIEs;
struct E1AP_MRDC_Data_Usage_Report_Item_ExtIEs;
struct E1AP_MRDC_Usage_Information_ExtIEs;
struct E1AP_M4Configuration_ExtIEs;
struct E1AP_M6Configuration_ExtIEs;
struct E1AP_M7Configuration_ExtIEs;
struct E1AP_MDT_Configuration_ExtIEs;
struct E1AP_NGRANAllocationAndRetentionPriority_ExtIEs;
struct E1AP_NG_RAN_QoS_Support_Item_ExtIEs;
struct E1AP_Non_Dynamic5QIDescriptor_ExtIEs;
struct E1AP_NPNSupportInfo_SNPN_ExtIEs;
struct E1AP_NPNContextInfo_SNPN_ExtIEs;
struct E1AP_NR_CGI_ExtIEs;
struct E1AP_NR_CGI_Support_Item_ExtIEs;
struct E1AP_Extended_NR_CGI_Support_Item_ExtIEs;
struct E1AP_PacketErrorRate_ExtIEs;
struct E1AP_PDCP_Configuration_ExtIEs;
struct E1AP_PDCP_Count_ExtIEs;
struct E1AP_PDU_Session_Resource_Data_Usage_Item_ExtIEs;
struct E1AP_PDCP_SN_Status_Information_ExtIEs;
struct E1AP_DRBBStatusTransfer_ExtIEs;
struct E1AP_PDU_Session_Resource_Activity_ItemExtIEs;
struct E1AP_PDU_Session_Resource_Confirm_Modified_Item_ExtIEs;
struct E1AP_PDU_Session_Resource_Failed_Item_ExtIEs;
struct E1AP_PDU_Session_Resource_Failed_Mod_Item_ExtIEs;
struct E1AP_PDU_Session_Resource_Failed_To_Modify_Item_ExtIEs;
struct E1AP_PDU_Session_Resource_Modified_Item_ExtIEs;
struct E1AP_PDU_Session_Resource_Required_To_Modify_Item_ExtIEs;
struct E1AP_PDU_Session_Resource_Setup_Item_ExtIEs;
struct E1AP_PDU_Session_Resource_Setup_Mod_Item_ExtIEs;
struct E1AP_PDU_Session_Resource_To_Modify_Item_ExtIEs;
struct E1AP_PDU_Session_Resource_To_Remove_Item_ExtIEs;
struct E1AP_PDU_Session_Resource_To_Setup_Item_ExtIEs;
struct E1AP_PDU_Session_Resource_To_Setup_Mod_Item_ExtIEs;
struct E1AP_PDU_Session_To_Notify_Item_ExtIEs;
struct E1AP_QoS_Flow_Item_ExtIEs;
struct E1AP_QoS_Flow_Failed_Item_ExtIEs;
struct E1AP_QoS_Flow_Mapping_Item_ExtIEs;
struct E1AP_QoS_Parameters_Support_List_ItemExtIEs;
struct E1AP_QoS_Flow_QoS_Parameter_Item_ExtIEs;
struct E1AP_QoSFlowLevelQoSParameters_ExtIEs;
struct E1AP_QoS_Flow_Removed_Item_ExtIEs;
struct E1AP_QoS_Flows_to_be_forwarded_Item_ExtIEs;
struct E1AP_DataForwardingtoNG_RANQoSFlowInformationList_Item_ExtIEs;
struct E1AP_RedundantPDUSessionInformation_ExtIEs;
struct E1AP_ROHC_ExtIEs;
struct E1AP_SecurityAlgorithm_ExtIEs;
struct E1AP_SecurityIndication_ExtIEs;
struct E1AP_SecurityInformation_ExtIEs;
struct E1AP_SecurityResult_ExtIEs;
struct E1AP_Slice_Support_Item_ExtIEs;
struct E1AP_SNSSAI_ExtIEs;
struct E1AP_SDAP_Configuration_ExtIEs;
struct E1AP_TNL_AvailableCapacityIndicator_ExtIEs;
struct E1AP_TSCTrafficCharacteristics_ExtIEs;
struct E1AP_TSCTrafficInformation_ExtIEs;
struct E1AP_TraceActivation_ExtIEs;
struct E1AP_T_ReorderingTimer_ExtIEs;
struct E1AP_Transport_Layer_Address_Info_ExtIEs;
struct E1AP_Transport_UP_Layer_Addresses_Info_To_Add_ItemExtIEs;
struct E1AP_Transport_UP_Layer_Addresses_Info_To_Remove_ItemExtIEs;
struct E1AP_UE_associatedLogicalE1_ConnectionItemExtIEs;
struct E1AP_ULUPTNLAddressToUpdateItemExtIEs;
struct E1AP_UP_Parameters_Item_ExtIEs;
struct E1AP_UPSecuritykey_ExtIEs;
struct E1AP_UplinkOnlyROHC_ExtIEs;

/* E1AP_ProtocolExtensionContainer */
typedef struct E1AP_ProtocolExtensionContainer_4961P0 {
	A_SEQUENCE_OF(struct E1AP_SupportedPLMNs_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P0_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P1 {
	A_SEQUENCE_OF(struct E1AP_AlternativeQoSParaSetItem_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P1_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P2 {
	A_SEQUENCE_OF(struct E1AP_Cell_Group_Information_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P2_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P3 {
	A_SEQUENCE_OF(struct E1AP_CriticalityDiagnostics_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P3_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P4 {
	A_SEQUENCE_OF(struct E1AP_CriticalityDiagnostics_IE_List_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P4_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P5 {
	A_SEQUENCE_OF(struct E1AP_DAPSRequestInfo_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P5_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P6 {
	A_SEQUENCE_OF(struct E1AP_Data_Forwarding_Information_Request_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P6_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P7 {
	A_SEQUENCE_OF(struct E1AP_Data_Forwarding_Information_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P7_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P8 {
	A_SEQUENCE_OF(struct E1AP_DataForwardingtoE_UTRANInformationListItem_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P8_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P9 {
	A_SEQUENCE_OF(struct E1AP_Data_Usage_per_PDU_Session_Report_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P9_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P10 {
	A_SEQUENCE_OF(struct E1AP_Data_Usage_per_QoS_Flow_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P10_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P11 {
	A_SEQUENCE_OF(struct E1AP_Data_Usage_Report_ItemExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P11_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P12 {
	A_SEQUENCE_OF(struct E1AP_DLDiscarding_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P12_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P13 {
	A_SEQUENCE_OF(struct E1AP_DLUPTNLAddressToUpdateItemExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P13_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P14 {
	A_SEQUENCE_OF(struct E1AP_DRB_Activity_ItemExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P14_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P15 {
	A_SEQUENCE_OF(struct E1AP_DRB_Confirm_Modified_Item_EUTRAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P15_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P16 {
	A_SEQUENCE_OF(struct E1AP_DRB_Confirm_Modified_Item_NG_RAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P16_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P17 {
	A_SEQUENCE_OF(struct E1AP_DRB_Failed_Item_EUTRAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P17_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P18 {
	A_SEQUENCE_OF(struct E1AP_DRB_Failed_Mod_Item_EUTRAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P18_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P19 {
	A_SEQUENCE_OF(struct E1AP_DRB_Failed_Item_NG_RAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P19_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P20 {
	A_SEQUENCE_OF(struct E1AP_DRB_Failed_Mod_Item_NG_RAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P20_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P21 {
	A_SEQUENCE_OF(struct E1AP_DRB_Failed_To_Modify_Item_EUTRAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P21_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P22 {
	A_SEQUENCE_OF(struct E1AP_DRB_Failed_To_Modify_Item_NG_RAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P22_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P23 {
	A_SEQUENCE_OF(struct E1AP_DRB_Measurement_Results_Information_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P23_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P24 {
	A_SEQUENCE_OF(struct E1AP_DRB_Modified_Item_EUTRAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P24_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P25 {
	A_SEQUENCE_OF(struct E1AP_DRB_Modified_Item_NG_RAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P25_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P26 {
	A_SEQUENCE_OF(struct E1AP_DRB_Removed_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P26_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P27 {
	A_SEQUENCE_OF(struct E1AP_DRB_Required_To_Modify_Item_EUTRAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P27_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P28 {
	A_SEQUENCE_OF(struct E1AP_DRB_Required_To_Modify_Item_NG_RAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P28_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P29 {
	A_SEQUENCE_OF(struct E1AP_DRB_Setup_Item_EUTRAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P29_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P30 {
	A_SEQUENCE_OF(struct E1AP_DRB_Setup_Mod_Item_EUTRAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P30_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P31 {
	A_SEQUENCE_OF(struct E1AP_DRB_Setup_Item_NG_RAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P31_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P32 {
	A_SEQUENCE_OF(struct E1AP_DRB_Setup_Mod_Item_NG_RAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P32_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P33 {
	A_SEQUENCE_OF(struct E1AP_DRB_Status_ItemExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P33_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P34 {
	A_SEQUENCE_OF(struct E1AP_DRBs_Subject_To_Counter_Check_Item_EUTRAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P34_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P35 {
	A_SEQUENCE_OF(struct E1AP_DRBs_Subject_To_Counter_Check_Item_NG_RAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P35_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P36 {
	A_SEQUENCE_OF(struct E1AP_DRBs_Subject_To_Early_Forwarding_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P36_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P37 {
	A_SEQUENCE_OF(struct E1AP_DRB_To_Modify_Item_EUTRAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P37_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P38 {
	A_SEQUENCE_OF(struct E1AP_DRB_To_Modify_Item_NG_RAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P38_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P39 {
	A_SEQUENCE_OF(struct E1AP_DRB_To_Remove_Item_EUTRAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P39_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P40 {
	A_SEQUENCE_OF(struct E1AP_DRB_Required_To_Remove_Item_EUTRAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P40_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P41 {
	A_SEQUENCE_OF(struct E1AP_DRB_To_Remove_Item_NG_RAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P41_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P42 {
	A_SEQUENCE_OF(struct E1AP_DRB_Required_To_Remove_Item_NG_RAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P42_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P43 {
	A_SEQUENCE_OF(struct E1AP_DRB_To_Setup_Item_EUTRAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P43_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P44 {
	A_SEQUENCE_OF(struct E1AP_DRB_To_Setup_Mod_Item_EUTRAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P44_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P45 {
	A_SEQUENCE_OF(struct E1AP_DRB_To_Setup_Item_NG_RAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P45_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P46 {
	A_SEQUENCE_OF(struct E1AP_DRB_To_Setup_Mod_Item_NG_RAN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P46_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P47 {
	A_SEQUENCE_OF(struct E1AP_DRB_Usage_Report_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P47_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P48 {
	A_SEQUENCE_OF(struct E1AP_Dynamic5QIDescriptor_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P48_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P49 {
	A_SEQUENCE_OF(struct E1AP_EHC_Common_Parameters_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P49_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P50 {
	A_SEQUENCE_OF(struct E1AP_EHC_Downlink_Parameters_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P50_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P51 {
	A_SEQUENCE_OF(struct E1AP_EHC_Uplink_Parameters_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P51_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P52 {
	A_SEQUENCE_OF(struct E1AP_EHC_Parameters_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P52_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P53 {
	A_SEQUENCE_OF(struct E1AP_Endpoint_IP_address_and_port_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P53_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P54 {
	A_SEQUENCE_OF(struct E1AP_EUTRANAllocationAndRetentionPriority_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P54_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P55 {
	A_SEQUENCE_OF(struct E1AP_EUTRAN_QoS_Support_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P55_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P56 {
	A_SEQUENCE_OF(struct E1AP_EUTRAN_QoS_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P56_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P57 {
	A_SEQUENCE_OF(struct E1AP_FirstDLCount_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P57_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P58 {
	A_SEQUENCE_OF(struct E1AP_Extended_GNB_CU_CP_Name_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P58_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P59 {
	A_SEQUENCE_OF(struct E1AP_GNB_CU_UP_CellGroupRelatedConfiguration_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P59_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P60 {
	A_SEQUENCE_OF(struct E1AP_Extended_GNB_CU_UP_Name_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P60_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P61 {
	A_SEQUENCE_OF(struct E1AP_GNB_CU_CP_TNLA_Setup_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P61_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P62 {
	A_SEQUENCE_OF(struct E1AP_GNB_CU_CP_TNLA_Failed_To_Setup_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P62_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P63 {
	A_SEQUENCE_OF(struct E1AP_GNB_CU_CP_TNLA_To_Add_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P63_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P64 {
	A_SEQUENCE_OF(struct E1AP_GNB_CU_CP_TNLA_To_Remove_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P64_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P65 {
	A_SEQUENCE_OF(struct E1AP_GNB_CU_CP_TNLA_To_Update_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P65_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P66 {
	A_SEQUENCE_OF(struct E1AP_GNB_CU_UP_TNLA_To_Remove_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P66_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P67 {
	A_SEQUENCE_OF(struct E1AP_GBR_QosInformation_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P67_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P68 {
	A_SEQUENCE_OF(struct E1AP_GBR_QosFlowInformation_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P68_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P69 {
	A_SEQUENCE_OF(struct E1AP_GTPTLA_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P69_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P70 {
	A_SEQUENCE_OF(struct E1AP_GTPTunnel_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P70_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P71 {
	A_SEQUENCE_OF(struct E1AP_HW_CapacityIndicator_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P71_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P72 {
	A_SEQUENCE_OF(struct E1AP_ImmediateMDT_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P72_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P73 {
	A_SEQUENCE_OF(struct E1AP_MaximumIPdatarate_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P73_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P74 {
	A_SEQUENCE_OF(struct E1AP_MRDC_Data_Usage_Report_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P74_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P75 {
	A_SEQUENCE_OF(struct E1AP_MRDC_Usage_Information_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P75_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P76 {
	A_SEQUENCE_OF(struct E1AP_M4Configuration_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P76_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P77 {
	A_SEQUENCE_OF(struct E1AP_M6Configuration_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P77_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P78 {
	A_SEQUENCE_OF(struct E1AP_M7Configuration_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P78_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P79 {
	A_SEQUENCE_OF(struct E1AP_MDT_Configuration_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P79_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P80 {
	A_SEQUENCE_OF(struct E1AP_NGRANAllocationAndRetentionPriority_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P80_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P81 {
	A_SEQUENCE_OF(struct E1AP_NG_RAN_QoS_Support_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P81_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P82 {
	A_SEQUENCE_OF(struct E1AP_Non_Dynamic5QIDescriptor_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P82_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P83 {
	A_SEQUENCE_OF(struct E1AP_NPNSupportInfo_SNPN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P83_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P84 {
	A_SEQUENCE_OF(struct E1AP_NPNContextInfo_SNPN_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P84_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P85 {
	A_SEQUENCE_OF(struct E1AP_NR_CGI_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P85_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P86 {
	A_SEQUENCE_OF(struct E1AP_NR_CGI_Support_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P86_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P87 {
	A_SEQUENCE_OF(struct E1AP_Extended_NR_CGI_Support_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P87_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P88 {
	A_SEQUENCE_OF(struct E1AP_PacketErrorRate_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P88_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P89 {
	A_SEQUENCE_OF(struct E1AP_PDCP_Configuration_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P89_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P90 {
	A_SEQUENCE_OF(struct E1AP_PDCP_Count_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P90_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P91 {
	A_SEQUENCE_OF(struct E1AP_PDU_Session_Resource_Data_Usage_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P91_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P92 {
	A_SEQUENCE_OF(struct E1AP_PDCP_SN_Status_Information_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P92_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P93 {
	A_SEQUENCE_OF(struct E1AP_DRBBStatusTransfer_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P93_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P94 {
	A_SEQUENCE_OF(struct E1AP_PDU_Session_Resource_Activity_ItemExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P94_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P95 {
	A_SEQUENCE_OF(struct E1AP_PDU_Session_Resource_Confirm_Modified_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P95_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P96 {
	A_SEQUENCE_OF(struct E1AP_PDU_Session_Resource_Failed_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P96_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P97 {
	A_SEQUENCE_OF(struct E1AP_PDU_Session_Resource_Failed_Mod_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P97_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P98 {
	A_SEQUENCE_OF(struct E1AP_PDU_Session_Resource_Failed_To_Modify_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P98_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P99 {
	A_SEQUENCE_OF(struct E1AP_PDU_Session_Resource_Modified_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P99_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P100 {
	A_SEQUENCE_OF(struct E1AP_PDU_Session_Resource_Required_To_Modify_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P100_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P101 {
	A_SEQUENCE_OF(struct E1AP_PDU_Session_Resource_Setup_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P101_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P102 {
	A_SEQUENCE_OF(struct E1AP_PDU_Session_Resource_Setup_Mod_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P102_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P103 {
	A_SEQUENCE_OF(struct E1AP_PDU_Session_Resource_To_Modify_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P103_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P104 {
	A_SEQUENCE_OF(struct E1AP_PDU_Session_Resource_To_Remove_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P104_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P105 {
	A_SEQUENCE_OF(struct E1AP_PDU_Session_Resource_To_Setup_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P105_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P106 {
	A_SEQUENCE_OF(struct E1AP_PDU_Session_Resource_To_Setup_Mod_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P106_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P107 {
	A_SEQUENCE_OF(struct E1AP_PDU_Session_To_Notify_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P107_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P108 {
	A_SEQUENCE_OF(struct E1AP_QoS_Flow_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P108_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P109 {
	A_SEQUENCE_OF(struct E1AP_QoS_Flow_Failed_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P109_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P110 {
	A_SEQUENCE_OF(struct E1AP_QoS_Flow_Mapping_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P110_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P111 {
	A_SEQUENCE_OF(struct E1AP_QoS_Parameters_Support_List_ItemExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P111_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P112 {
	A_SEQUENCE_OF(struct E1AP_QoS_Flow_QoS_Parameter_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P112_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P113 {
	A_SEQUENCE_OF(struct E1AP_QoSFlowLevelQoSParameters_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P113_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P114 {
	A_SEQUENCE_OF(struct E1AP_QoS_Flow_Removed_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P114_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P115 {
	A_SEQUENCE_OF(struct E1AP_QoS_Flows_to_be_forwarded_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P115_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P116 {
	A_SEQUENCE_OF(struct E1AP_DataForwardingtoNG_RANQoSFlowInformationList_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P116_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P117 {
	A_SEQUENCE_OF(struct E1AP_RedundantPDUSessionInformation_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P117_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P118 {
	A_SEQUENCE_OF(struct E1AP_ROHC_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P118_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P119 {
	A_SEQUENCE_OF(struct E1AP_SecurityAlgorithm_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P119_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P120 {
	A_SEQUENCE_OF(struct E1AP_SecurityIndication_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P120_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P121 {
	A_SEQUENCE_OF(struct E1AP_SecurityInformation_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P121_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P122 {
	A_SEQUENCE_OF(struct E1AP_SecurityResult_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P122_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P123 {
	A_SEQUENCE_OF(struct E1AP_Slice_Support_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P123_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P124 {
	A_SEQUENCE_OF(struct E1AP_SNSSAI_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P124_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P125 {
	A_SEQUENCE_OF(struct E1AP_SDAP_Configuration_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P125_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P126 {
	A_SEQUENCE_OF(struct E1AP_TNL_AvailableCapacityIndicator_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P126_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P127 {
	A_SEQUENCE_OF(struct E1AP_TSCTrafficCharacteristics_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P127_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P128 {
	A_SEQUENCE_OF(struct E1AP_TSCTrafficInformation_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P128_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P129 {
	A_SEQUENCE_OF(struct E1AP_TraceActivation_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P129_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P130 {
	A_SEQUENCE_OF(struct E1AP_T_ReorderingTimer_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P130_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P131 {
	A_SEQUENCE_OF(struct E1AP_Transport_Layer_Address_Info_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P131_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P132 {
	A_SEQUENCE_OF(struct E1AP_Transport_UP_Layer_Addresses_Info_To_Add_ItemExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P132_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P133 {
	A_SEQUENCE_OF(struct E1AP_Transport_UP_Layer_Addresses_Info_To_Remove_ItemExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P133_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P134 {
	A_SEQUENCE_OF(struct E1AP_UE_associatedLogicalE1_ConnectionItemExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P134_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P135 {
	A_SEQUENCE_OF(struct E1AP_ULUPTNLAddressToUpdateItemExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P135_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P136 {
	A_SEQUENCE_OF(struct E1AP_UP_Parameters_Item_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P136_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P137 {
	A_SEQUENCE_OF(struct E1AP_UPSecuritykey_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P137_t;
typedef struct E1AP_ProtocolExtensionContainer_4961P138 {
	A_SEQUENCE_OF(struct E1AP_UplinkOnlyROHC_ExtIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolExtensionContainer_4961P138_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P0;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P0_specs_1;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P0_1[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P0_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P1;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P1_specs_3;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P1_3[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P1_constr_3;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P2;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P2_specs_5;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P2_5[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P2_constr_5;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P3;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P3_specs_7;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P3_7[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P3_constr_7;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P4;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P4_specs_9;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P4_9[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P4_constr_9;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P5;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P5_specs_11;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P5_11[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P5_constr_11;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P6;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P6_specs_13;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P6_13[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P6_constr_13;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P7;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P7_specs_15;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P7_15[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P7_constr_15;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P8;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P8_specs_17;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P8_17[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P8_constr_17;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P9;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P9_specs_19;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P9_19[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P9_constr_19;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P10;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P10_specs_21;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P10_21[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P10_constr_21;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P11;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P11_specs_23;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P11_23[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P11_constr_23;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P12;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P12_specs_25;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P12_25[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P12_constr_25;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P13;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P13_specs_27;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P13_27[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P13_constr_27;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P14;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P14_specs_29;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P14_29[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P14_constr_29;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P15;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P15_specs_31;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P15_31[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P15_constr_31;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P16;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P16_specs_33;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P16_33[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P16_constr_33;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P17;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P17_specs_35;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P17_35[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P17_constr_35;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P18;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P18_specs_37;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P18_37[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P18_constr_37;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P19;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P19_specs_39;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P19_39[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P19_constr_39;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P20;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P20_specs_41;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P20_41[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P20_constr_41;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P21;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P21_specs_43;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P21_43[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P21_constr_43;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P22;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P22_specs_45;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P22_45[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P22_constr_45;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P23;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P23_specs_47;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P23_47[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P23_constr_47;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P24;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P24_specs_49;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P24_49[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P24_constr_49;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P25;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P25_specs_51;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P25_51[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P25_constr_51;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P26;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P26_specs_53;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P26_53[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P26_constr_53;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P27;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P27_specs_55;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P27_55[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P27_constr_55;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P28;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P28_specs_57;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P28_57[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P28_constr_57;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P29;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P29_specs_59;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P29_59[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P29_constr_59;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P30;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P30_specs_61;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P30_61[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P30_constr_61;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P31;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P31_specs_63;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P31_63[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P31_constr_63;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P32;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P32_specs_65;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P32_65[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P32_constr_65;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P33;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P33_specs_67;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P33_67[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P33_constr_67;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P34;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P34_specs_69;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P34_69[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P34_constr_69;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P35;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P35_specs_71;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P35_71[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P35_constr_71;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P36;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P36_specs_73;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P36_73[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P36_constr_73;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P37;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P37_specs_75;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P37_75[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P37_constr_75;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P38;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P38_specs_77;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P38_77[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P38_constr_77;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P39;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P39_specs_79;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P39_79[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P39_constr_79;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P40;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P40_specs_81;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P40_81[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P40_constr_81;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P41;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P41_specs_83;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P41_83[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P41_constr_83;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P42;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P42_specs_85;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P42_85[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P42_constr_85;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P43;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P43_specs_87;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P43_87[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P43_constr_87;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P44;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P44_specs_89;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P44_89[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P44_constr_89;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P45;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P45_specs_91;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P45_91[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P45_constr_91;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P46;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P46_specs_93;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P46_93[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P46_constr_93;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P47;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P47_specs_95;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P47_95[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P47_constr_95;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P48;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P48_specs_97;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P48_97[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P48_constr_97;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P49;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P49_specs_99;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P49_99[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P49_constr_99;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P50;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P50_specs_101;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P50_101[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P50_constr_101;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P51;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P51_specs_103;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P51_103[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P51_constr_103;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P52;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P52_specs_105;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P52_105[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P52_constr_105;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P53;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P53_specs_107;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P53_107[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P53_constr_107;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P54;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P54_specs_109;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P54_109[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P54_constr_109;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P55;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P55_specs_111;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P55_111[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P55_constr_111;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P56;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P56_specs_113;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P56_113[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P56_constr_113;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P57;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P57_specs_115;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P57_115[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P57_constr_115;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P58;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P58_specs_117;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P58_117[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P58_constr_117;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P59;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P59_specs_119;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P59_119[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P59_constr_119;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P60;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P60_specs_121;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P60_121[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P60_constr_121;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P61;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P61_specs_123;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P61_123[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P61_constr_123;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P62;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P62_specs_125;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P62_125[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P62_constr_125;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P63;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P63_specs_127;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P63_127[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P63_constr_127;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P64;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P64_specs_129;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P64_129[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P64_constr_129;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P65;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P65_specs_131;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P65_131[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P65_constr_131;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P66;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P66_specs_133;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P66_133[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P66_constr_133;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P67;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P67_specs_135;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P67_135[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P67_constr_135;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P68;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P68_specs_137;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P68_137[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P68_constr_137;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P69;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P69_specs_139;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P69_139[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P69_constr_139;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P70;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P70_specs_141;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P70_141[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P70_constr_141;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P71;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P71_specs_143;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P71_143[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P71_constr_143;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P72;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P72_specs_145;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P72_145[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P72_constr_145;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P73;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P73_specs_147;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P73_147[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P73_constr_147;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P74;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P74_specs_149;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P74_149[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P74_constr_149;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P75;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P75_specs_151;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P75_151[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P75_constr_151;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P76;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P76_specs_153;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P76_153[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P76_constr_153;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P77;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P77_specs_155;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P77_155[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P77_constr_155;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P78;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P78_specs_157;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P78_157[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P78_constr_157;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P79;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P79_specs_159;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P79_159[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P79_constr_159;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P80;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P80_specs_161;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P80_161[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P80_constr_161;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P81;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P81_specs_163;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P81_163[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P81_constr_163;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P82;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P82_specs_165;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P82_165[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P82_constr_165;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P83;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P83_specs_167;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P83_167[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P83_constr_167;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P84;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P84_specs_169;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P84_169[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P84_constr_169;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P85;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P85_specs_171;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P85_171[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P85_constr_171;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P86;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P86_specs_173;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P86_173[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P86_constr_173;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P87;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P87_specs_175;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P87_175[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P87_constr_175;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P88;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P88_specs_177;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P88_177[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P88_constr_177;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P89;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P89_specs_179;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P89_179[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P89_constr_179;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P90;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P90_specs_181;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P90_181[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P90_constr_181;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P91;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P91_specs_183;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P91_183[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P91_constr_183;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P92;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P92_specs_185;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P92_185[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P92_constr_185;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P93;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P93_specs_187;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P93_187[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P93_constr_187;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P94;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P94_specs_189;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P94_189[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P94_constr_189;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P95;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P95_specs_191;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P95_191[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P95_constr_191;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P96;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P96_specs_193;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P96_193[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P96_constr_193;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P97;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P97_specs_195;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P97_195[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P97_constr_195;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P98;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P98_specs_197;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P98_197[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P98_constr_197;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P99;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P99_specs_199;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P99_199[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P99_constr_199;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P100;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P100_specs_201;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P100_201[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P100_constr_201;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P101;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P101_specs_203;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P101_203[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P101_constr_203;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P102;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P102_specs_205;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P102_205[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P102_constr_205;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P103;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P103_specs_207;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P103_207[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P103_constr_207;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P104;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P104_specs_209;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P104_209[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P104_constr_209;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P105;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P105_specs_211;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P105_211[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P105_constr_211;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P106;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P106_specs_213;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P106_213[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P106_constr_213;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P107;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P107_specs_215;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P107_215[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P107_constr_215;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P108;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P108_specs_217;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P108_217[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P108_constr_217;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P109;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P109_specs_219;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P109_219[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P109_constr_219;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P110;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P110_specs_221;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P110_221[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P110_constr_221;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P111;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P111_specs_223;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P111_223[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P111_constr_223;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P112;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P112_specs_225;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P112_225[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P112_constr_225;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P113;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P113_specs_227;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P113_227[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P113_constr_227;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P114;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P114_specs_229;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P114_229[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P114_constr_229;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P115;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P115_specs_231;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P115_231[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P115_constr_231;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P116;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P116_specs_233;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P116_233[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P116_constr_233;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P117;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P117_specs_235;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P117_235[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P117_constr_235;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P118;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P118_specs_237;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P118_237[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P118_constr_237;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P119;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P119_specs_239;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P119_239[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P119_constr_239;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P120;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P120_specs_241;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P120_241[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P120_constr_241;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P121;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P121_specs_243;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P121_243[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P121_constr_243;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P122;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P122_specs_245;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P122_245[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P122_constr_245;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P123;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P123_specs_247;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P123_247[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P123_constr_247;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P124;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P124_specs_249;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P124_249[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P124_constr_249;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P125;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P125_specs_251;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P125_251[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P125_constr_251;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P126;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P126_specs_253;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P126_253[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P126_constr_253;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P127;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P127_specs_255;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P127_255[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P127_constr_255;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P128;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P128_specs_257;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P128_257[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P128_constr_257;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P129;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P129_specs_259;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P129_259[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P129_constr_259;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P130;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P130_specs_261;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P130_261[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P130_constr_261;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P131;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P131_specs_263;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P131_263[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P131_constr_263;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P132;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P132_specs_265;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P132_265[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P132_constr_265;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P133;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P133_specs_267;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P133_267[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P133_constr_267;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P134;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P134_specs_269;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P134_269[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P134_constr_269;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P135;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P135_specs_271;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P135_271[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P135_constr_271;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P136;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P136_specs_273;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P136_273[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P136_constr_273;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P137;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P137_specs_275;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P137_275[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P137_constr_275;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolExtensionContainer_4961P138;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolExtensionContainer_4961P138_specs_277;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolExtensionContainer_4961P138_277[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolExtensionContainer_4961P138_constr_277;

#ifdef __cplusplus
}
#endif

#endif	/* _E1AP_ProtocolExtensionContainer_H_ */
#include <asn_internal.h>
