/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-Containers"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#ifndef	_E1AP_ProtocolExtensionField_H_
#define	_E1AP_ProtocolExtensionField_H_


#include <asn_application.h>

/* Including external dependencies */
#include "E1AP_ProtocolIE-ID.h"
#include "E1AP_Criticality.h"
#include <ANY.h>
#include <asn_ioc.h>
#include "E1AP_NPNSupportInfo.h"
#include "E1AP_Presence.h"
#include "E1AP_ExtendedSliceSupportList.h"
#include "E1AP_Extended-NR-CGI-Support-List.h"
#include <OPEN_TYPE.h>
#include <constr_CHOICE.h>
#include <constr_SEQUENCE.h>
#include "E1AP_Number-of-tunnels.h"
#include "E1AP_DataForwardingtoNG-RANQoSFlowInformationList.h"
#include "E1AP_EarlyForwardingCOUNTInfo.h"
#include "E1AP_QoS-Flow-List.h"
#include "E1AP_QoSFlowLevelQoSParameters.h"
#include "E1AP_EarlyForwardingCOUNTReq.h"
#include "E1AP_DAPSRequestInfo.h"
#include "E1AP_EarlyDataForwardingIndicator.h"
#include "E1AP_IgnoreMappingRuleIndication.h"
#include "E1AP_QoS-Flows-DRB-Remapping.h"
#include "E1AP_ExtendedPacketDelayBudget.h"
#include "E1AP_MaxCIDEHCDL.h"
#include "E1AP_CP-TNL-Information.h"
#include "E1AP_AlternativeQoSParaSetList.h"
#include "E1AP_PDCP-StatusReportIndication.h"
#include "E1AP_AdditionalPDCPduplicationInformation.h"
#include "E1AP_EHC-Parameters.h"
#include "E1AP_UP-TNL-Information.h"
#include "E1AP_RedundantPDUSessionInformation.h"
#include "E1AP_SNSSAI.h"
#include "E1AP_CommonNetworkInstance.h"
#include "E1AP_DataForwardingtoE-UTRANInformationList.h"
#include "E1AP_Cause.h"
#include "E1AP_NetworkInstance.h"
#include "E1AP_QoS-Flow-Mapping-Indication.h"
#include "E1AP_RedundantQoSFlowIndicator.h"
#include "E1AP_TSCTrafficCharacteristics.h"
#include "E1AP_QosMonitoringRequest.h"
#include "E1AP_GBR-QoSFlowInformation.h"
#include "E1AP_QosMonitoringReportingFrequency.h"
#include "E1AP_QosMonitoringDisabled.h"
#include "E1AP_MDT-Configuration.h"
#include "E1AP_URIaddress.h"
#include "E1AP_QoS-Mapping-Information.h"

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum E1AP_SupportedPLMNs_ExtIEs__extensionValue_PR {
	E1AP_SupportedPLMNs_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_SupportedPLMNs_ExtIEs__extensionValue_PR_NPNSupportInfo,
	E1AP_SupportedPLMNs_ExtIEs__extensionValue_PR_ExtendedSliceSupportList,
	E1AP_SupportedPLMNs_ExtIEs__extensionValue_PR_Extended_NR_CGI_Support_List
} E1AP_SupportedPLMNs_ExtIEs__extensionValue_PR;
typedef enum E1AP_AlternativeQoSParaSetItem_ExtIEs__extensionValue_PR {
	E1AP_AlternativeQoSParaSetItem_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_AlternativeQoSParaSetItem_ExtIEs__extensionValue_PR;
typedef enum E1AP_Cell_Group_Information_Item_ExtIEs__extensionValue_PR {
	E1AP_Cell_Group_Information_Item_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_Cell_Group_Information_Item_ExtIEs__extensionValue_PR_Number_of_tunnels
} E1AP_Cell_Group_Information_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_CriticalityDiagnostics_ExtIEs__extensionValue_PR {
	E1AP_CriticalityDiagnostics_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_CriticalityDiagnostics_ExtIEs__extensionValue_PR;
typedef enum E1AP_CriticalityDiagnostics_IE_List_ExtIEs__extensionValue_PR {
	E1AP_CriticalityDiagnostics_IE_List_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_CriticalityDiagnostics_IE_List_ExtIEs__extensionValue_PR;
typedef enum E1AP_DAPSRequestInfo_ExtIEs__extensionValue_PR {
	E1AP_DAPSRequestInfo_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DAPSRequestInfo_ExtIEs__extensionValue_PR;
typedef enum E1AP_Data_Forwarding_Information_Request_ExtIEs__extensionValue_PR {
	E1AP_Data_Forwarding_Information_Request_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_Data_Forwarding_Information_Request_ExtIEs__extensionValue_PR;
typedef enum E1AP_Data_Forwarding_Information_ExtIEs__extensionValue_PR {
	E1AP_Data_Forwarding_Information_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_Data_Forwarding_Information_ExtIEs__extensionValue_PR_DataForwardingtoNG_RANQoSFlowInformationList
} E1AP_Data_Forwarding_Information_ExtIEs__extensionValue_PR;
typedef enum E1AP_DataForwardingtoE_UTRANInformationListItem_ExtIEs__extensionValue_PR {
	E1AP_DataForwardingtoE_UTRANInformationListItem_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DataForwardingtoE_UTRANInformationListItem_ExtIEs__extensionValue_PR;
typedef enum E1AP_Data_Usage_per_PDU_Session_Report_ExtIEs__extensionValue_PR {
	E1AP_Data_Usage_per_PDU_Session_Report_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_Data_Usage_per_PDU_Session_Report_ExtIEs__extensionValue_PR;
typedef enum E1AP_Data_Usage_per_QoS_Flow_Item_ExtIEs__extensionValue_PR {
	E1AP_Data_Usage_per_QoS_Flow_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_Data_Usage_per_QoS_Flow_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_Data_Usage_Report_ItemExtIEs__extensionValue_PR {
	E1AP_Data_Usage_Report_ItemExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_Data_Usage_Report_ItemExtIEs__extensionValue_PR;
typedef enum E1AP_DLDiscarding_ExtIEs__extensionValue_PR {
	E1AP_DLDiscarding_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DLDiscarding_ExtIEs__extensionValue_PR;
typedef enum E1AP_DLUPTNLAddressToUpdateItemExtIEs__extensionValue_PR {
	E1AP_DLUPTNLAddressToUpdateItemExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DLUPTNLAddressToUpdateItemExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_Activity_ItemExtIEs__extensionValue_PR {
	E1AP_DRB_Activity_ItemExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_Activity_ItemExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_Confirm_Modified_Item_EUTRAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_Confirm_Modified_Item_EUTRAN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_Confirm_Modified_Item_EUTRAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_Confirm_Modified_Item_NG_RAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_Confirm_Modified_Item_NG_RAN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_Confirm_Modified_Item_NG_RAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_Failed_Item_EUTRAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_Failed_Item_EUTRAN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_Failed_Item_EUTRAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_Failed_Mod_Item_EUTRAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_Failed_Mod_Item_EUTRAN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_Failed_Mod_Item_EUTRAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_Failed_Item_NG_RAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_Failed_Item_NG_RAN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_Failed_Item_NG_RAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_Failed_Mod_Item_NG_RAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_Failed_Mod_Item_NG_RAN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_Failed_Mod_Item_NG_RAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_Failed_To_Modify_Item_EUTRAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_Failed_To_Modify_Item_EUTRAN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_Failed_To_Modify_Item_EUTRAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_Failed_To_Modify_Item_NG_RAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_Failed_To_Modify_Item_NG_RAN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_Failed_To_Modify_Item_NG_RAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_Measurement_Results_Information_Item_ExtIEs__extensionValue_PR {
	E1AP_DRB_Measurement_Results_Information_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_Measurement_Results_Information_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_Modified_Item_EUTRAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_Modified_Item_EUTRAN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_Modified_Item_EUTRAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_Modified_Item_NG_RAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_Modified_Item_NG_RAN_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_DRB_Modified_Item_NG_RAN_ExtIEs__extensionValue_PR_EarlyForwardingCOUNTInfo,
	E1AP_DRB_Modified_Item_NG_RAN_ExtIEs__extensionValue_PR_QoS_Flow_List
} E1AP_DRB_Modified_Item_NG_RAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_Removed_Item_ExtIEs__extensionValue_PR {
	E1AP_DRB_Removed_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_Removed_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_Required_To_Modify_Item_EUTRAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_Required_To_Modify_Item_EUTRAN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_Required_To_Modify_Item_EUTRAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_Required_To_Modify_Item_NG_RAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_Required_To_Modify_Item_NG_RAN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_Required_To_Modify_Item_NG_RAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_Setup_Item_EUTRAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_Setup_Item_EUTRAN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_Setup_Item_EUTRAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_Setup_Mod_Item_EUTRAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_Setup_Mod_Item_EUTRAN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_Setup_Mod_Item_EUTRAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_Setup_Item_NG_RAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_Setup_Item_NG_RAN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_Setup_Item_NG_RAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_Setup_Mod_Item_NG_RAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_Setup_Mod_Item_NG_RAN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_Setup_Mod_Item_NG_RAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_Status_ItemExtIEs__extensionValue_PR {
	E1AP_DRB_Status_ItemExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_Status_ItemExtIEs__extensionValue_PR;
typedef enum E1AP_DRBs_Subject_To_Counter_Check_Item_EUTRAN_ExtIEs__extensionValue_PR {
	E1AP_DRBs_Subject_To_Counter_Check_Item_EUTRAN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRBs_Subject_To_Counter_Check_Item_EUTRAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRBs_Subject_To_Counter_Check_Item_NG_RAN_ExtIEs__extensionValue_PR {
	E1AP_DRBs_Subject_To_Counter_Check_Item_NG_RAN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRBs_Subject_To_Counter_Check_Item_NG_RAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRBs_Subject_To_Early_Forwarding_Item_ExtIEs__extensionValue_PR {
	E1AP_DRBs_Subject_To_Early_Forwarding_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRBs_Subject_To_Early_Forwarding_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_To_Modify_Item_EUTRAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_To_Modify_Item_EUTRAN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_To_Modify_Item_EUTRAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_To_Modify_Item_NG_RAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_To_Modify_Item_NG_RAN_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_DRB_To_Modify_Item_NG_RAN_ExtIEs__extensionValue_PR_QoS_Flow_List,
	E1AP_DRB_To_Modify_Item_NG_RAN_ExtIEs__extensionValue_PR_QoSFlowLevelQoSParameters,
	E1AP_DRB_To_Modify_Item_NG_RAN_ExtIEs__extensionValue_PR_EarlyForwardingCOUNTReq,
	E1AP_DRB_To_Modify_Item_NG_RAN_ExtIEs__extensionValue_PR_EarlyForwardingCOUNTInfo,
	E1AP_DRB_To_Modify_Item_NG_RAN_ExtIEs__extensionValue_PR_DAPSRequestInfo,
	E1AP_DRB_To_Modify_Item_NG_RAN_ExtIEs__extensionValue_PR_EarlyDataForwardingIndicator
} E1AP_DRB_To_Modify_Item_NG_RAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_To_Remove_Item_EUTRAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_To_Remove_Item_EUTRAN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_To_Remove_Item_EUTRAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_Required_To_Remove_Item_EUTRAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_Required_To_Remove_Item_EUTRAN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_Required_To_Remove_Item_EUTRAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_To_Remove_Item_NG_RAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_To_Remove_Item_NG_RAN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_To_Remove_Item_NG_RAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_Required_To_Remove_Item_NG_RAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_Required_To_Remove_Item_NG_RAN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_Required_To_Remove_Item_NG_RAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_To_Setup_Item_EUTRAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_To_Setup_Item_EUTRAN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_To_Setup_Item_EUTRAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_To_Setup_Mod_Item_EUTRAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_To_Setup_Mod_Item_EUTRAN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_To_Setup_Mod_Item_EUTRAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_To_Setup_Item_NG_RAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_To_Setup_Item_NG_RAN_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_DRB_To_Setup_Item_NG_RAN_ExtIEs__extensionValue_PR_QoSFlowLevelQoSParameters,
	E1AP_DRB_To_Setup_Item_NG_RAN_ExtIEs__extensionValue_PR_DAPSRequestInfo,
	E1AP_DRB_To_Setup_Item_NG_RAN_ExtIEs__extensionValue_PR_IgnoreMappingRuleIndication,
	E1AP_DRB_To_Setup_Item_NG_RAN_ExtIEs__extensionValue_PR_QoS_Flows_DRB_Remapping
} E1AP_DRB_To_Setup_Item_NG_RAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_To_Setup_Mod_Item_NG_RAN_ExtIEs__extensionValue_PR {
	E1AP_DRB_To_Setup_Mod_Item_NG_RAN_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_DRB_To_Setup_Mod_Item_NG_RAN_ExtIEs__extensionValue_PR_QoSFlowLevelQoSParameters,
	E1AP_DRB_To_Setup_Mod_Item_NG_RAN_ExtIEs__extensionValue_PR_IgnoreMappingRuleIndication,
	E1AP_DRB_To_Setup_Mod_Item_NG_RAN_ExtIEs__extensionValue_PR_DAPSRequestInfo
} E1AP_DRB_To_Setup_Mod_Item_NG_RAN_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRB_Usage_Report_Item_ExtIEs__extensionValue_PR {
	E1AP_DRB_Usage_Report_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRB_Usage_Report_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_Dynamic5QIDescriptor_ExtIEs__extensionValue_PR {
	E1AP_Dynamic5QIDescriptor_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_Dynamic5QIDescriptor_ExtIEs__extensionValue_PR_ExtendedPacketDelayBudget,
	E1AP_Dynamic5QIDescriptor_ExtIEs__extensionValue_PR_ExtendedPacketDelayBudget_1,
	E1AP_Dynamic5QIDescriptor_ExtIEs__extensionValue_PR_ExtendedPacketDelayBudget_2
} E1AP_Dynamic5QIDescriptor_ExtIEs__extensionValue_PR;
typedef enum E1AP_EHC_Common_Parameters_ExtIEs__extensionValue_PR {
	E1AP_EHC_Common_Parameters_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_EHC_Common_Parameters_ExtIEs__extensionValue_PR;
typedef enum E1AP_EHC_Downlink_Parameters_ExtIEs__extensionValue_PR {
	E1AP_EHC_Downlink_Parameters_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_EHC_Downlink_Parameters_ExtIEs__extensionValue_PR_MaxCIDEHCDL
} E1AP_EHC_Downlink_Parameters_ExtIEs__extensionValue_PR;
typedef enum E1AP_EHC_Uplink_Parameters_ExtIEs__extensionValue_PR {
	E1AP_EHC_Uplink_Parameters_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_EHC_Uplink_Parameters_ExtIEs__extensionValue_PR;
typedef enum E1AP_EHC_Parameters_ExtIEs__extensionValue_PR {
	E1AP_EHC_Parameters_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_EHC_Parameters_ExtIEs__extensionValue_PR;
typedef enum E1AP_Endpoint_IP_address_and_port_ExtIEs__extensionValue_PR {
	E1AP_Endpoint_IP_address_and_port_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_Endpoint_IP_address_and_port_ExtIEs__extensionValue_PR;
typedef enum E1AP_EUTRANAllocationAndRetentionPriority_ExtIEs__extensionValue_PR {
	E1AP_EUTRANAllocationAndRetentionPriority_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_EUTRANAllocationAndRetentionPriority_ExtIEs__extensionValue_PR;
typedef enum E1AP_EUTRAN_QoS_Support_Item_ExtIEs__extensionValue_PR {
	E1AP_EUTRAN_QoS_Support_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_EUTRAN_QoS_Support_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_EUTRAN_QoS_ExtIEs__extensionValue_PR {
	E1AP_EUTRAN_QoS_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_EUTRAN_QoS_ExtIEs__extensionValue_PR;
typedef enum E1AP_FirstDLCount_ExtIEs__extensionValue_PR {
	E1AP_FirstDLCount_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_FirstDLCount_ExtIEs__extensionValue_PR;
typedef enum E1AP_Extended_GNB_CU_CP_Name_ExtIEs__extensionValue_PR {
	E1AP_Extended_GNB_CU_CP_Name_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_Extended_GNB_CU_CP_Name_ExtIEs__extensionValue_PR;
typedef enum E1AP_GNB_CU_UP_CellGroupRelatedConfiguration_Item_ExtIEs__extensionValue_PR {
	E1AP_GNB_CU_UP_CellGroupRelatedConfiguration_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_GNB_CU_UP_CellGroupRelatedConfiguration_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_Extended_GNB_CU_UP_Name_ExtIEs__extensionValue_PR {
	E1AP_Extended_GNB_CU_UP_Name_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_Extended_GNB_CU_UP_Name_ExtIEs__extensionValue_PR;
typedef enum E1AP_GNB_CU_CP_TNLA_Setup_Item_ExtIEs__extensionValue_PR {
	E1AP_GNB_CU_CP_TNLA_Setup_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_GNB_CU_CP_TNLA_Setup_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_GNB_CU_CP_TNLA_Failed_To_Setup_Item_ExtIEs__extensionValue_PR {
	E1AP_GNB_CU_CP_TNLA_Failed_To_Setup_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_GNB_CU_CP_TNLA_Failed_To_Setup_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_GNB_CU_CP_TNLA_To_Add_Item_ExtIEs__extensionValue_PR {
	E1AP_GNB_CU_CP_TNLA_To_Add_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_GNB_CU_CP_TNLA_To_Add_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_GNB_CU_CP_TNLA_To_Remove_Item_ExtIEs__extensionValue_PR {
	E1AP_GNB_CU_CP_TNLA_To_Remove_Item_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_GNB_CU_CP_TNLA_To_Remove_Item_ExtIEs__extensionValue_PR_CP_TNL_Information
} E1AP_GNB_CU_CP_TNLA_To_Remove_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_GNB_CU_CP_TNLA_To_Update_Item_ExtIEs__extensionValue_PR {
	E1AP_GNB_CU_CP_TNLA_To_Update_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_GNB_CU_CP_TNLA_To_Update_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_GNB_CU_UP_TNLA_To_Remove_Item_ExtIEs__extensionValue_PR {
	E1AP_GNB_CU_UP_TNLA_To_Remove_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_GNB_CU_UP_TNLA_To_Remove_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_GBR_QosInformation_ExtIEs__extensionValue_PR {
	E1AP_GBR_QosInformation_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_GBR_QosInformation_ExtIEs__extensionValue_PR;
typedef enum E1AP_GBR_QosFlowInformation_ExtIEs__extensionValue_PR {
	E1AP_GBR_QosFlowInformation_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_GBR_QosFlowInformation_ExtIEs__extensionValue_PR_AlternativeQoSParaSetList
} E1AP_GBR_QosFlowInformation_ExtIEs__extensionValue_PR;
typedef enum E1AP_GTPTLA_Item_ExtIEs__extensionValue_PR {
	E1AP_GTPTLA_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_GTPTLA_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_GTPTunnel_ExtIEs__extensionValue_PR {
	E1AP_GTPTunnel_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_GTPTunnel_ExtIEs__extensionValue_PR;
typedef enum E1AP_HW_CapacityIndicator_ExtIEs__extensionValue_PR {
	E1AP_HW_CapacityIndicator_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_HW_CapacityIndicator_ExtIEs__extensionValue_PR;
typedef enum E1AP_ImmediateMDT_ExtIEs__extensionValue_PR {
	E1AP_ImmediateMDT_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_ImmediateMDT_ExtIEs__extensionValue_PR;
typedef enum E1AP_MaximumIPdatarate_ExtIEs__extensionValue_PR {
	E1AP_MaximumIPdatarate_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_MaximumIPdatarate_ExtIEs__extensionValue_PR;
typedef enum E1AP_MRDC_Data_Usage_Report_Item_ExtIEs__extensionValue_PR {
	E1AP_MRDC_Data_Usage_Report_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_MRDC_Data_Usage_Report_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_MRDC_Usage_Information_ExtIEs__extensionValue_PR {
	E1AP_MRDC_Usage_Information_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_MRDC_Usage_Information_ExtIEs__extensionValue_PR;
typedef enum E1AP_M4Configuration_ExtIEs__extensionValue_PR {
	E1AP_M4Configuration_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_M4Configuration_ExtIEs__extensionValue_PR;
typedef enum E1AP_M6Configuration_ExtIEs__extensionValue_PR {
	E1AP_M6Configuration_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_M6Configuration_ExtIEs__extensionValue_PR;
typedef enum E1AP_M7Configuration_ExtIEs__extensionValue_PR {
	E1AP_M7Configuration_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_M7Configuration_ExtIEs__extensionValue_PR;
typedef enum E1AP_MDT_Configuration_ExtIEs__extensionValue_PR {
	E1AP_MDT_Configuration_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_MDT_Configuration_ExtIEs__extensionValue_PR;
typedef enum E1AP_NGRANAllocationAndRetentionPriority_ExtIEs__extensionValue_PR {
	E1AP_NGRANAllocationAndRetentionPriority_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_NGRANAllocationAndRetentionPriority_ExtIEs__extensionValue_PR;
typedef enum E1AP_NG_RAN_QoS_Support_Item_ExtIEs__extensionValue_PR {
	E1AP_NG_RAN_QoS_Support_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_NG_RAN_QoS_Support_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_Non_Dynamic5QIDescriptor_ExtIEs__extensionValue_PR {
	E1AP_Non_Dynamic5QIDescriptor_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_Non_Dynamic5QIDescriptor_ExtIEs__extensionValue_PR_ExtendedPacketDelayBudget,
	E1AP_Non_Dynamic5QIDescriptor_ExtIEs__extensionValue_PR_ExtendedPacketDelayBudget_1
} E1AP_Non_Dynamic5QIDescriptor_ExtIEs__extensionValue_PR;
typedef enum E1AP_NPNSupportInfo_SNPN_ExtIEs__extensionValue_PR {
	E1AP_NPNSupportInfo_SNPN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_NPNSupportInfo_SNPN_ExtIEs__extensionValue_PR;
typedef enum E1AP_NPNContextInfo_SNPN_ExtIEs__extensionValue_PR {
	E1AP_NPNContextInfo_SNPN_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_NPNContextInfo_SNPN_ExtIEs__extensionValue_PR;
typedef enum E1AP_NR_CGI_ExtIEs__extensionValue_PR {
	E1AP_NR_CGI_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_NR_CGI_ExtIEs__extensionValue_PR;
typedef enum E1AP_NR_CGI_Support_Item_ExtIEs__extensionValue_PR {
	E1AP_NR_CGI_Support_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_NR_CGI_Support_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_Extended_NR_CGI_Support_Item_ExtIEs__extensionValue_PR {
	E1AP_Extended_NR_CGI_Support_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_Extended_NR_CGI_Support_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_PacketErrorRate_ExtIEs__extensionValue_PR {
	E1AP_PacketErrorRate_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_PacketErrorRate_ExtIEs__extensionValue_PR;
typedef enum E1AP_PDCP_Configuration_ExtIEs__extensionValue_PR {
	E1AP_PDCP_Configuration_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_PDCP_Configuration_ExtIEs__extensionValue_PR_PDCP_StatusReportIndication,
	E1AP_PDCP_Configuration_ExtIEs__extensionValue_PR_AdditionalPDCPduplicationInformation,
	E1AP_PDCP_Configuration_ExtIEs__extensionValue_PR_EHC_Parameters
} E1AP_PDCP_Configuration_ExtIEs__extensionValue_PR;
typedef enum E1AP_PDCP_Count_ExtIEs__extensionValue_PR {
	E1AP_PDCP_Count_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_PDCP_Count_ExtIEs__extensionValue_PR;
typedef enum E1AP_PDU_Session_Resource_Data_Usage_Item_ExtIEs__extensionValue_PR {
	E1AP_PDU_Session_Resource_Data_Usage_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_PDU_Session_Resource_Data_Usage_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_PDCP_SN_Status_Information_ExtIEs__extensionValue_PR {
	E1AP_PDCP_SN_Status_Information_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_PDCP_SN_Status_Information_ExtIEs__extensionValue_PR;
typedef enum E1AP_DRBBStatusTransfer_ExtIEs__extensionValue_PR {
	E1AP_DRBBStatusTransfer_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DRBBStatusTransfer_ExtIEs__extensionValue_PR;
typedef enum E1AP_PDU_Session_Resource_Activity_ItemExtIEs__extensionValue_PR {
	E1AP_PDU_Session_Resource_Activity_ItemExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_PDU_Session_Resource_Activity_ItemExtIEs__extensionValue_PR;
typedef enum E1AP_PDU_Session_Resource_Confirm_Modified_Item_ExtIEs__extensionValue_PR {
	E1AP_PDU_Session_Resource_Confirm_Modified_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_PDU_Session_Resource_Confirm_Modified_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_PDU_Session_Resource_Failed_Item_ExtIEs__extensionValue_PR {
	E1AP_PDU_Session_Resource_Failed_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_PDU_Session_Resource_Failed_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_PDU_Session_Resource_Failed_Mod_Item_ExtIEs__extensionValue_PR {
	E1AP_PDU_Session_Resource_Failed_Mod_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_PDU_Session_Resource_Failed_Mod_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_PDU_Session_Resource_Failed_To_Modify_Item_ExtIEs__extensionValue_PR {
	E1AP_PDU_Session_Resource_Failed_To_Modify_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_PDU_Session_Resource_Failed_To_Modify_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_PDU_Session_Resource_Modified_Item_ExtIEs__extensionValue_PR {
	E1AP_PDU_Session_Resource_Modified_Item_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_PDU_Session_Resource_Modified_Item_ExtIEs__extensionValue_PR_UP_TNL_Information
} E1AP_PDU_Session_Resource_Modified_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_PDU_Session_Resource_Required_To_Modify_Item_ExtIEs__extensionValue_PR {
	E1AP_PDU_Session_Resource_Required_To_Modify_Item_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_PDU_Session_Resource_Required_To_Modify_Item_ExtIEs__extensionValue_PR_UP_TNL_Information
} E1AP_PDU_Session_Resource_Required_To_Modify_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_PDU_Session_Resource_Setup_Item_ExtIEs__extensionValue_PR {
	E1AP_PDU_Session_Resource_Setup_Item_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_PDU_Session_Resource_Setup_Item_ExtIEs__extensionValue_PR_UP_TNL_Information,
	E1AP_PDU_Session_Resource_Setup_Item_ExtIEs__extensionValue_PR_RedundantPDUSessionInformation
} E1AP_PDU_Session_Resource_Setup_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_PDU_Session_Resource_Setup_Mod_Item_ExtIEs__extensionValue_PR {
	E1AP_PDU_Session_Resource_Setup_Mod_Item_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_PDU_Session_Resource_Setup_Mod_Item_ExtIEs__extensionValue_PR_UP_TNL_Information
} E1AP_PDU_Session_Resource_Setup_Mod_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_PDU_Session_Resource_To_Modify_Item_ExtIEs__extensionValue_PR {
	E1AP_PDU_Session_Resource_To_Modify_Item_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_PDU_Session_Resource_To_Modify_Item_ExtIEs__extensionValue_PR_SNSSAI,
	E1AP_PDU_Session_Resource_To_Modify_Item_ExtIEs__extensionValue_PR_CommonNetworkInstance,
	E1AP_PDU_Session_Resource_To_Modify_Item_ExtIEs__extensionValue_PR_UP_TNL_Information,
	E1AP_PDU_Session_Resource_To_Modify_Item_ExtIEs__extensionValue_PR_CommonNetworkInstance_1,
	E1AP_PDU_Session_Resource_To_Modify_Item_ExtIEs__extensionValue_PR_DataForwardingtoE_UTRANInformationList
} E1AP_PDU_Session_Resource_To_Modify_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_PDU_Session_Resource_To_Remove_Item_ExtIEs__extensionValue_PR {
	E1AP_PDU_Session_Resource_To_Remove_Item_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_PDU_Session_Resource_To_Remove_Item_ExtIEs__extensionValue_PR_Cause
} E1AP_PDU_Session_Resource_To_Remove_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_PDU_Session_Resource_To_Setup_Item_ExtIEs__extensionValue_PR {
	E1AP_PDU_Session_Resource_To_Setup_Item_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_PDU_Session_Resource_To_Setup_Item_ExtIEs__extensionValue_PR_CommonNetworkInstance,
	E1AP_PDU_Session_Resource_To_Setup_Item_ExtIEs__extensionValue_PR_UP_TNL_Information,
	E1AP_PDU_Session_Resource_To_Setup_Item_ExtIEs__extensionValue_PR_CommonNetworkInstance_1,
	E1AP_PDU_Session_Resource_To_Setup_Item_ExtIEs__extensionValue_PR_RedundantPDUSessionInformation
} E1AP_PDU_Session_Resource_To_Setup_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_PDU_Session_Resource_To_Setup_Mod_Item_ExtIEs__extensionValue_PR {
	E1AP_PDU_Session_Resource_To_Setup_Mod_Item_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_PDU_Session_Resource_To_Setup_Mod_Item_ExtIEs__extensionValue_PR_NetworkInstance,
	E1AP_PDU_Session_Resource_To_Setup_Mod_Item_ExtIEs__extensionValue_PR_CommonNetworkInstance,
	E1AP_PDU_Session_Resource_To_Setup_Mod_Item_ExtIEs__extensionValue_PR_UP_TNL_Information,
	E1AP_PDU_Session_Resource_To_Setup_Mod_Item_ExtIEs__extensionValue_PR_CommonNetworkInstance_1
} E1AP_PDU_Session_Resource_To_Setup_Mod_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_PDU_Session_To_Notify_Item_ExtIEs__extensionValue_PR {
	E1AP_PDU_Session_To_Notify_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_PDU_Session_To_Notify_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_QoS_Flow_Item_ExtIEs__extensionValue_PR {
	E1AP_QoS_Flow_Item_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_QoS_Flow_Item_ExtIEs__extensionValue_PR_QoS_Flow_Mapping_Indication
} E1AP_QoS_Flow_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_QoS_Flow_Failed_Item_ExtIEs__extensionValue_PR {
	E1AP_QoS_Flow_Failed_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_QoS_Flow_Failed_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_QoS_Flow_Mapping_Item_ExtIEs__extensionValue_PR {
	E1AP_QoS_Flow_Mapping_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_QoS_Flow_Mapping_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_QoS_Parameters_Support_List_ItemExtIEs__extensionValue_PR {
	E1AP_QoS_Parameters_Support_List_ItemExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_QoS_Parameters_Support_List_ItemExtIEs__extensionValue_PR;
typedef enum E1AP_QoS_Flow_QoS_Parameter_Item_ExtIEs__extensionValue_PR {
	E1AP_QoS_Flow_QoS_Parameter_Item_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_QoS_Flow_QoS_Parameter_Item_ExtIEs__extensionValue_PR_RedundantQoSFlowIndicator,
	E1AP_QoS_Flow_QoS_Parameter_Item_ExtIEs__extensionValue_PR_TSCTrafficCharacteristics
} E1AP_QoS_Flow_QoS_Parameter_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_QoSFlowLevelQoSParameters_ExtIEs__extensionValue_PR {
	E1AP_QoSFlowLevelQoSParameters_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_QoSFlowLevelQoSParameters_ExtIEs__extensionValue_PR_QosMonitoringRequest,
	E1AP_QoSFlowLevelQoSParameters_ExtIEs__extensionValue_PR_GBR_QoSFlowInformation,
	E1AP_QoSFlowLevelQoSParameters_ExtIEs__extensionValue_PR_QosMonitoringReportingFrequency,
	E1AP_QoSFlowLevelQoSParameters_ExtIEs__extensionValue_PR_QosMonitoringDisabled
} E1AP_QoSFlowLevelQoSParameters_ExtIEs__extensionValue_PR;
typedef enum E1AP_QoS_Flow_Removed_Item_ExtIEs__extensionValue_PR {
	E1AP_QoS_Flow_Removed_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_QoS_Flow_Removed_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_QoS_Flows_to_be_forwarded_Item_ExtIEs__extensionValue_PR {
	E1AP_QoS_Flows_to_be_forwarded_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_QoS_Flows_to_be_forwarded_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_DataForwardingtoNG_RANQoSFlowInformationList_Item_ExtIEs__extensionValue_PR {
	E1AP_DataForwardingtoNG_RANQoSFlowInformationList_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_DataForwardingtoNG_RANQoSFlowInformationList_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_RedundantPDUSessionInformation_ExtIEs__extensionValue_PR {
	E1AP_RedundantPDUSessionInformation_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_RedundantPDUSessionInformation_ExtIEs__extensionValue_PR;
typedef enum E1AP_ROHC_ExtIEs__extensionValue_PR {
	E1AP_ROHC_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_ROHC_ExtIEs__extensionValue_PR;
typedef enum E1AP_SecurityAlgorithm_ExtIEs__extensionValue_PR {
	E1AP_SecurityAlgorithm_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_SecurityAlgorithm_ExtIEs__extensionValue_PR;
typedef enum E1AP_SecurityIndication_ExtIEs__extensionValue_PR {
	E1AP_SecurityIndication_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_SecurityIndication_ExtIEs__extensionValue_PR;
typedef enum E1AP_SecurityInformation_ExtIEs__extensionValue_PR {
	E1AP_SecurityInformation_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_SecurityInformation_ExtIEs__extensionValue_PR;
typedef enum E1AP_SecurityResult_ExtIEs__extensionValue_PR {
	E1AP_SecurityResult_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_SecurityResult_ExtIEs__extensionValue_PR;
typedef enum E1AP_Slice_Support_Item_ExtIEs__extensionValue_PR {
	E1AP_Slice_Support_Item_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_Slice_Support_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_SNSSAI_ExtIEs__extensionValue_PR {
	E1AP_SNSSAI_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_SNSSAI_ExtIEs__extensionValue_PR;
typedef enum E1AP_SDAP_Configuration_ExtIEs__extensionValue_PR {
	E1AP_SDAP_Configuration_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_SDAP_Configuration_ExtIEs__extensionValue_PR;
typedef enum E1AP_TNL_AvailableCapacityIndicator_ExtIEs__extensionValue_PR {
	E1AP_TNL_AvailableCapacityIndicator_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_TNL_AvailableCapacityIndicator_ExtIEs__extensionValue_PR;
typedef enum E1AP_TSCTrafficCharacteristics_ExtIEs__extensionValue_PR {
	E1AP_TSCTrafficCharacteristics_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_TSCTrafficCharacteristics_ExtIEs__extensionValue_PR;
typedef enum E1AP_TSCTrafficInformation_ExtIEs__extensionValue_PR {
	E1AP_TSCTrafficInformation_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_TSCTrafficInformation_ExtIEs__extensionValue_PR;
typedef enum E1AP_TraceActivation_ExtIEs__extensionValue_PR {
	E1AP_TraceActivation_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_TraceActivation_ExtIEs__extensionValue_PR_MDT_Configuration,
	E1AP_TraceActivation_ExtIEs__extensionValue_PR_URIaddress
} E1AP_TraceActivation_ExtIEs__extensionValue_PR;
typedef enum E1AP_T_ReorderingTimer_ExtIEs__extensionValue_PR {
	E1AP_T_ReorderingTimer_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_T_ReorderingTimer_ExtIEs__extensionValue_PR;
typedef enum E1AP_Transport_Layer_Address_Info_ExtIEs__extensionValue_PR {
	E1AP_Transport_Layer_Address_Info_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_Transport_Layer_Address_Info_ExtIEs__extensionValue_PR;
typedef enum E1AP_Transport_UP_Layer_Addresses_Info_To_Add_ItemExtIEs__extensionValue_PR {
	E1AP_Transport_UP_Layer_Addresses_Info_To_Add_ItemExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_Transport_UP_Layer_Addresses_Info_To_Add_ItemExtIEs__extensionValue_PR;
typedef enum E1AP_Transport_UP_Layer_Addresses_Info_To_Remove_ItemExtIEs__extensionValue_PR {
	E1AP_Transport_UP_Layer_Addresses_Info_To_Remove_ItemExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_Transport_UP_Layer_Addresses_Info_To_Remove_ItemExtIEs__extensionValue_PR;
typedef enum E1AP_UE_associatedLogicalE1_ConnectionItemExtIEs__extensionValue_PR {
	E1AP_UE_associatedLogicalE1_ConnectionItemExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_UE_associatedLogicalE1_ConnectionItemExtIEs__extensionValue_PR;
typedef enum E1AP_ULUPTNLAddressToUpdateItemExtIEs__extensionValue_PR {
	E1AP_ULUPTNLAddressToUpdateItemExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_ULUPTNLAddressToUpdateItemExtIEs__extensionValue_PR;
typedef enum E1AP_UP_Parameters_Item_ExtIEs__extensionValue_PR {
	E1AP_UP_Parameters_Item_ExtIEs__extensionValue_PR_NOTHING,	/* No components present */
	E1AP_UP_Parameters_Item_ExtIEs__extensionValue_PR_QoS_Mapping_Information
} E1AP_UP_Parameters_Item_ExtIEs__extensionValue_PR;
typedef enum E1AP_UPSecuritykey_ExtIEs__extensionValue_PR {
	E1AP_UPSecuritykey_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_UPSecuritykey_ExtIEs__extensionValue_PR;
typedef enum E1AP_UplinkOnlyROHC_ExtIEs__extensionValue_PR {
	E1AP_UplinkOnlyROHC_ExtIEs__extensionValue_PR_NOTHING	/* No components present */
	
} E1AP_UplinkOnlyROHC_ExtIEs__extensionValue_PR;

/* E1AP_ProtocolExtensionField */
typedef struct E1AP_SupportedPLMNs_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_SupportedPLMNs_ExtIEs__extensionValue {
		E1AP_SupportedPLMNs_ExtIEs__extensionValue_PR present;
		union E1AP_SupportedPLMNs_ExtIEs__E1AP_extensionValue_u {
			E1AP_NPNSupportInfo_t	 NPNSupportInfo;
			E1AP_ExtendedSliceSupportList_t	 ExtendedSliceSupportList;
			E1AP_Extended_NR_CGI_Support_List_t	 Extended_NR_CGI_Support_List;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_SupportedPLMNs_ExtIEs_t;
typedef struct E1AP_AlternativeQoSParaSetItem_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_AlternativeQoSParaSetItem_ExtIEs__extensionValue {
		E1AP_AlternativeQoSParaSetItem_ExtIEs__extensionValue_PR present;
		union E1AP_AlternativeQoSParaSetItem_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_AlternativeQoSParaSetItem_ExtIEs_t;
typedef struct E1AP_Cell_Group_Information_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_Cell_Group_Information_Item_ExtIEs__extensionValue {
		E1AP_Cell_Group_Information_Item_ExtIEs__extensionValue_PR present;
		union E1AP_Cell_Group_Information_Item_ExtIEs__E1AP_extensionValue_u {
			E1AP_Number_of_tunnels_t	 Number_of_tunnels;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_Cell_Group_Information_Item_ExtIEs_t;
typedef struct E1AP_CriticalityDiagnostics_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_CriticalityDiagnostics_ExtIEs__extensionValue {
		E1AP_CriticalityDiagnostics_ExtIEs__extensionValue_PR present;
		union E1AP_CriticalityDiagnostics_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_CriticalityDiagnostics_ExtIEs_t;
typedef struct E1AP_CriticalityDiagnostics_IE_List_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_CriticalityDiagnostics_IE_List_ExtIEs__extensionValue {
		E1AP_CriticalityDiagnostics_IE_List_ExtIEs__extensionValue_PR present;
		union E1AP_CriticalityDiagnostics_IE_List_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_CriticalityDiagnostics_IE_List_ExtIEs_t;
typedef struct E1AP_DAPSRequestInfo_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DAPSRequestInfo_ExtIEs__extensionValue {
		E1AP_DAPSRequestInfo_ExtIEs__extensionValue_PR present;
		union E1AP_DAPSRequestInfo_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DAPSRequestInfo_ExtIEs_t;
typedef struct E1AP_Data_Forwarding_Information_Request_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_Data_Forwarding_Information_Request_ExtIEs__extensionValue {
		E1AP_Data_Forwarding_Information_Request_ExtIEs__extensionValue_PR present;
		union E1AP_Data_Forwarding_Information_Request_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_Data_Forwarding_Information_Request_ExtIEs_t;
typedef struct E1AP_Data_Forwarding_Information_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_Data_Forwarding_Information_ExtIEs__extensionValue {
		E1AP_Data_Forwarding_Information_ExtIEs__extensionValue_PR present;
		union E1AP_Data_Forwarding_Information_ExtIEs__E1AP_extensionValue_u {
			E1AP_DataForwardingtoNG_RANQoSFlowInformationList_t	 DataForwardingtoNG_RANQoSFlowInformationList;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_Data_Forwarding_Information_ExtIEs_t;
typedef struct E1AP_DataForwardingtoE_UTRANInformationListItem_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DataForwardingtoE_UTRANInformationListItem_ExtIEs__extensionValue {
		E1AP_DataForwardingtoE_UTRANInformationListItem_ExtIEs__extensionValue_PR present;
		union E1AP_DataForwardingtoE_UTRANInformationListItem_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DataForwardingtoE_UTRANInformationListItem_ExtIEs_t;
typedef struct E1AP_Data_Usage_per_PDU_Session_Report_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_Data_Usage_per_PDU_Session_Report_ExtIEs__extensionValue {
		E1AP_Data_Usage_per_PDU_Session_Report_ExtIEs__extensionValue_PR present;
		union E1AP_Data_Usage_per_PDU_Session_Report_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_Data_Usage_per_PDU_Session_Report_ExtIEs_t;
typedef struct E1AP_Data_Usage_per_QoS_Flow_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_Data_Usage_per_QoS_Flow_Item_ExtIEs__extensionValue {
		E1AP_Data_Usage_per_QoS_Flow_Item_ExtIEs__extensionValue_PR present;
		union E1AP_Data_Usage_per_QoS_Flow_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_Data_Usage_per_QoS_Flow_Item_ExtIEs_t;
typedef struct E1AP_Data_Usage_Report_ItemExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_Data_Usage_Report_ItemExtIEs__extensionValue {
		E1AP_Data_Usage_Report_ItemExtIEs__extensionValue_PR present;
		union E1AP_Data_Usage_Report_ItemExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_Data_Usage_Report_ItemExtIEs_t;
typedef struct E1AP_DLDiscarding_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DLDiscarding_ExtIEs__extensionValue {
		E1AP_DLDiscarding_ExtIEs__extensionValue_PR present;
		union E1AP_DLDiscarding_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DLDiscarding_ExtIEs_t;
typedef struct E1AP_DLUPTNLAddressToUpdateItemExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DLUPTNLAddressToUpdateItemExtIEs__extensionValue {
		E1AP_DLUPTNLAddressToUpdateItemExtIEs__extensionValue_PR present;
		union E1AP_DLUPTNLAddressToUpdateItemExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DLUPTNLAddressToUpdateItemExtIEs_t;
typedef struct E1AP_DRB_Activity_ItemExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_Activity_ItemExtIEs__extensionValue {
		E1AP_DRB_Activity_ItemExtIEs__extensionValue_PR present;
		union E1AP_DRB_Activity_ItemExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_Activity_ItemExtIEs_t;
typedef struct E1AP_DRB_Confirm_Modified_Item_EUTRAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_Confirm_Modified_Item_EUTRAN_ExtIEs__extensionValue {
		E1AP_DRB_Confirm_Modified_Item_EUTRAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_Confirm_Modified_Item_EUTRAN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_Confirm_Modified_Item_EUTRAN_ExtIEs_t;
typedef struct E1AP_DRB_Confirm_Modified_Item_NG_RAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_Confirm_Modified_Item_NG_RAN_ExtIEs__extensionValue {
		E1AP_DRB_Confirm_Modified_Item_NG_RAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_Confirm_Modified_Item_NG_RAN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_Confirm_Modified_Item_NG_RAN_ExtIEs_t;
typedef struct E1AP_DRB_Failed_Item_EUTRAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_Failed_Item_EUTRAN_ExtIEs__extensionValue {
		E1AP_DRB_Failed_Item_EUTRAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_Failed_Item_EUTRAN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_Failed_Item_EUTRAN_ExtIEs_t;
typedef struct E1AP_DRB_Failed_Mod_Item_EUTRAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_Failed_Mod_Item_EUTRAN_ExtIEs__extensionValue {
		E1AP_DRB_Failed_Mod_Item_EUTRAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_Failed_Mod_Item_EUTRAN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_Failed_Mod_Item_EUTRAN_ExtIEs_t;
typedef struct E1AP_DRB_Failed_Item_NG_RAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_Failed_Item_NG_RAN_ExtIEs__extensionValue {
		E1AP_DRB_Failed_Item_NG_RAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_Failed_Item_NG_RAN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_Failed_Item_NG_RAN_ExtIEs_t;
typedef struct E1AP_DRB_Failed_Mod_Item_NG_RAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_Failed_Mod_Item_NG_RAN_ExtIEs__extensionValue {
		E1AP_DRB_Failed_Mod_Item_NG_RAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_Failed_Mod_Item_NG_RAN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_Failed_Mod_Item_NG_RAN_ExtIEs_t;
typedef struct E1AP_DRB_Failed_To_Modify_Item_EUTRAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_Failed_To_Modify_Item_EUTRAN_ExtIEs__extensionValue {
		E1AP_DRB_Failed_To_Modify_Item_EUTRAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_Failed_To_Modify_Item_EUTRAN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_Failed_To_Modify_Item_EUTRAN_ExtIEs_t;
typedef struct E1AP_DRB_Failed_To_Modify_Item_NG_RAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_Failed_To_Modify_Item_NG_RAN_ExtIEs__extensionValue {
		E1AP_DRB_Failed_To_Modify_Item_NG_RAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_Failed_To_Modify_Item_NG_RAN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_Failed_To_Modify_Item_NG_RAN_ExtIEs_t;
typedef struct E1AP_DRB_Measurement_Results_Information_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_Measurement_Results_Information_Item_ExtIEs__extensionValue {
		E1AP_DRB_Measurement_Results_Information_Item_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_Measurement_Results_Information_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_Measurement_Results_Information_Item_ExtIEs_t;
typedef struct E1AP_DRB_Modified_Item_EUTRAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_Modified_Item_EUTRAN_ExtIEs__extensionValue {
		E1AP_DRB_Modified_Item_EUTRAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_Modified_Item_EUTRAN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_Modified_Item_EUTRAN_ExtIEs_t;
typedef struct E1AP_DRB_Modified_Item_NG_RAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_Modified_Item_NG_RAN_ExtIEs__extensionValue {
		E1AP_DRB_Modified_Item_NG_RAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_Modified_Item_NG_RAN_ExtIEs__E1AP_extensionValue_u {
			E1AP_EarlyForwardingCOUNTInfo_t	 EarlyForwardingCOUNTInfo;
			E1AP_QoS_Flow_List_t	 QoS_Flow_List;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_Modified_Item_NG_RAN_ExtIEs_t;
typedef struct E1AP_DRB_Removed_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_Removed_Item_ExtIEs__extensionValue {
		E1AP_DRB_Removed_Item_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_Removed_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_Removed_Item_ExtIEs_t;
typedef struct E1AP_DRB_Required_To_Modify_Item_EUTRAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_Required_To_Modify_Item_EUTRAN_ExtIEs__extensionValue {
		E1AP_DRB_Required_To_Modify_Item_EUTRAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_Required_To_Modify_Item_EUTRAN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_Required_To_Modify_Item_EUTRAN_ExtIEs_t;
typedef struct E1AP_DRB_Required_To_Modify_Item_NG_RAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_Required_To_Modify_Item_NG_RAN_ExtIEs__extensionValue {
		E1AP_DRB_Required_To_Modify_Item_NG_RAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_Required_To_Modify_Item_NG_RAN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_Required_To_Modify_Item_NG_RAN_ExtIEs_t;
typedef struct E1AP_DRB_Setup_Item_EUTRAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_Setup_Item_EUTRAN_ExtIEs__extensionValue {
		E1AP_DRB_Setup_Item_EUTRAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_Setup_Item_EUTRAN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_Setup_Item_EUTRAN_ExtIEs_t;
typedef struct E1AP_DRB_Setup_Mod_Item_EUTRAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_Setup_Mod_Item_EUTRAN_ExtIEs__extensionValue {
		E1AP_DRB_Setup_Mod_Item_EUTRAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_Setup_Mod_Item_EUTRAN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_Setup_Mod_Item_EUTRAN_ExtIEs_t;
typedef struct E1AP_DRB_Setup_Item_NG_RAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_Setup_Item_NG_RAN_ExtIEs__extensionValue {
		E1AP_DRB_Setup_Item_NG_RAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_Setup_Item_NG_RAN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_Setup_Item_NG_RAN_ExtIEs_t;
typedef struct E1AP_DRB_Setup_Mod_Item_NG_RAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_Setup_Mod_Item_NG_RAN_ExtIEs__extensionValue {
		E1AP_DRB_Setup_Mod_Item_NG_RAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_Setup_Mod_Item_NG_RAN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_Setup_Mod_Item_NG_RAN_ExtIEs_t;
typedef struct E1AP_DRB_Status_ItemExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_Status_ItemExtIEs__extensionValue {
		E1AP_DRB_Status_ItemExtIEs__extensionValue_PR present;
		union E1AP_DRB_Status_ItemExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_Status_ItemExtIEs_t;
typedef struct E1AP_DRBs_Subject_To_Counter_Check_Item_EUTRAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRBs_Subject_To_Counter_Check_Item_EUTRAN_ExtIEs__extensionValue {
		E1AP_DRBs_Subject_To_Counter_Check_Item_EUTRAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRBs_Subject_To_Counter_Check_Item_EUTRAN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRBs_Subject_To_Counter_Check_Item_EUTRAN_ExtIEs_t;
typedef struct E1AP_DRBs_Subject_To_Counter_Check_Item_NG_RAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRBs_Subject_To_Counter_Check_Item_NG_RAN_ExtIEs__extensionValue {
		E1AP_DRBs_Subject_To_Counter_Check_Item_NG_RAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRBs_Subject_To_Counter_Check_Item_NG_RAN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRBs_Subject_To_Counter_Check_Item_NG_RAN_ExtIEs_t;
typedef struct E1AP_DRBs_Subject_To_Early_Forwarding_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRBs_Subject_To_Early_Forwarding_Item_ExtIEs__extensionValue {
		E1AP_DRBs_Subject_To_Early_Forwarding_Item_ExtIEs__extensionValue_PR present;
		union E1AP_DRBs_Subject_To_Early_Forwarding_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRBs_Subject_To_Early_Forwarding_Item_ExtIEs_t;
typedef struct E1AP_DRB_To_Modify_Item_EUTRAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_To_Modify_Item_EUTRAN_ExtIEs__extensionValue {
		E1AP_DRB_To_Modify_Item_EUTRAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_To_Modify_Item_EUTRAN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_To_Modify_Item_EUTRAN_ExtIEs_t;
typedef struct E1AP_DRB_To_Modify_Item_NG_RAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_To_Modify_Item_NG_RAN_ExtIEs__extensionValue {
		E1AP_DRB_To_Modify_Item_NG_RAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_To_Modify_Item_NG_RAN_ExtIEs__E1AP_extensionValue_u {
			E1AP_QoS_Flow_List_t	 QoS_Flow_List;
			E1AP_QoSFlowLevelQoSParameters_t	 QoSFlowLevelQoSParameters;
			E1AP_EarlyForwardingCOUNTReq_t	 EarlyForwardingCOUNTReq;
			E1AP_EarlyForwardingCOUNTInfo_t	 EarlyForwardingCOUNTInfo;
			E1AP_DAPSRequestInfo_t	 DAPSRequestInfo;
			E1AP_EarlyDataForwardingIndicator_t	 EarlyDataForwardingIndicator;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_To_Modify_Item_NG_RAN_ExtIEs_t;
typedef struct E1AP_DRB_To_Remove_Item_EUTRAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_To_Remove_Item_EUTRAN_ExtIEs__extensionValue {
		E1AP_DRB_To_Remove_Item_EUTRAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_To_Remove_Item_EUTRAN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_To_Remove_Item_EUTRAN_ExtIEs_t;
typedef struct E1AP_DRB_Required_To_Remove_Item_EUTRAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_Required_To_Remove_Item_EUTRAN_ExtIEs__extensionValue {
		E1AP_DRB_Required_To_Remove_Item_EUTRAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_Required_To_Remove_Item_EUTRAN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_Required_To_Remove_Item_EUTRAN_ExtIEs_t;
typedef struct E1AP_DRB_To_Remove_Item_NG_RAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_To_Remove_Item_NG_RAN_ExtIEs__extensionValue {
		E1AP_DRB_To_Remove_Item_NG_RAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_To_Remove_Item_NG_RAN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_To_Remove_Item_NG_RAN_ExtIEs_t;
typedef struct E1AP_DRB_Required_To_Remove_Item_NG_RAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_Required_To_Remove_Item_NG_RAN_ExtIEs__extensionValue {
		E1AP_DRB_Required_To_Remove_Item_NG_RAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_Required_To_Remove_Item_NG_RAN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_Required_To_Remove_Item_NG_RAN_ExtIEs_t;
typedef struct E1AP_DRB_To_Setup_Item_EUTRAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_To_Setup_Item_EUTRAN_ExtIEs__extensionValue {
		E1AP_DRB_To_Setup_Item_EUTRAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_To_Setup_Item_EUTRAN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_To_Setup_Item_EUTRAN_ExtIEs_t;
typedef struct E1AP_DRB_To_Setup_Mod_Item_EUTRAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_To_Setup_Mod_Item_EUTRAN_ExtIEs__extensionValue {
		E1AP_DRB_To_Setup_Mod_Item_EUTRAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_To_Setup_Mod_Item_EUTRAN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_To_Setup_Mod_Item_EUTRAN_ExtIEs_t;
typedef struct E1AP_DRB_To_Setup_Item_NG_RAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_To_Setup_Item_NG_RAN_ExtIEs__extensionValue {
		E1AP_DRB_To_Setup_Item_NG_RAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_To_Setup_Item_NG_RAN_ExtIEs__E1AP_extensionValue_u {
			E1AP_QoSFlowLevelQoSParameters_t	 QoSFlowLevelQoSParameters;
			E1AP_DAPSRequestInfo_t	 DAPSRequestInfo;
			E1AP_IgnoreMappingRuleIndication_t	 IgnoreMappingRuleIndication;
			E1AP_QoS_Flows_DRB_Remapping_t	 QoS_Flows_DRB_Remapping;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_To_Setup_Item_NG_RAN_ExtIEs_t;
typedef struct E1AP_DRB_To_Setup_Mod_Item_NG_RAN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_To_Setup_Mod_Item_NG_RAN_ExtIEs__extensionValue {
		E1AP_DRB_To_Setup_Mod_Item_NG_RAN_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_To_Setup_Mod_Item_NG_RAN_ExtIEs__E1AP_extensionValue_u {
			E1AP_QoSFlowLevelQoSParameters_t	 QoSFlowLevelQoSParameters;
			E1AP_IgnoreMappingRuleIndication_t	 IgnoreMappingRuleIndication;
			E1AP_DAPSRequestInfo_t	 DAPSRequestInfo;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_To_Setup_Mod_Item_NG_RAN_ExtIEs_t;
typedef struct E1AP_DRB_Usage_Report_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRB_Usage_Report_Item_ExtIEs__extensionValue {
		E1AP_DRB_Usage_Report_Item_ExtIEs__extensionValue_PR present;
		union E1AP_DRB_Usage_Report_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRB_Usage_Report_Item_ExtIEs_t;
typedef struct E1AP_Dynamic5QIDescriptor_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_Dynamic5QIDescriptor_ExtIEs__extensionValue {
		E1AP_Dynamic5QIDescriptor_ExtIEs__extensionValue_PR present;
		union E1AP_Dynamic5QIDescriptor_ExtIEs__E1AP_extensionValue_u {
			E1AP_ExtendedPacketDelayBudget_t	 ExtendedPacketDelayBudget;
			E1AP_ExtendedPacketDelayBudget_t	 ExtendedPacketDelayBudget_1;
			E1AP_ExtendedPacketDelayBudget_t	 ExtendedPacketDelayBudget_2;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_Dynamic5QIDescriptor_ExtIEs_t;
typedef struct E1AP_EHC_Common_Parameters_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_EHC_Common_Parameters_ExtIEs__extensionValue {
		E1AP_EHC_Common_Parameters_ExtIEs__extensionValue_PR present;
		union E1AP_EHC_Common_Parameters_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_EHC_Common_Parameters_ExtIEs_t;
typedef struct E1AP_EHC_Downlink_Parameters_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_EHC_Downlink_Parameters_ExtIEs__extensionValue {
		E1AP_EHC_Downlink_Parameters_ExtIEs__extensionValue_PR present;
		union E1AP_EHC_Downlink_Parameters_ExtIEs__E1AP_extensionValue_u {
			E1AP_MaxCIDEHCDL_t	 MaxCIDEHCDL;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_EHC_Downlink_Parameters_ExtIEs_t;
typedef struct E1AP_EHC_Uplink_Parameters_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_EHC_Uplink_Parameters_ExtIEs__extensionValue {
		E1AP_EHC_Uplink_Parameters_ExtIEs__extensionValue_PR present;
		union E1AP_EHC_Uplink_Parameters_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_EHC_Uplink_Parameters_ExtIEs_t;
typedef struct E1AP_EHC_Parameters_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_EHC_Parameters_ExtIEs__extensionValue {
		E1AP_EHC_Parameters_ExtIEs__extensionValue_PR present;
		union E1AP_EHC_Parameters_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_EHC_Parameters_ExtIEs_t;
typedef struct E1AP_Endpoint_IP_address_and_port_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_Endpoint_IP_address_and_port_ExtIEs__extensionValue {
		E1AP_Endpoint_IP_address_and_port_ExtIEs__extensionValue_PR present;
		union E1AP_Endpoint_IP_address_and_port_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_Endpoint_IP_address_and_port_ExtIEs_t;
typedef struct E1AP_EUTRANAllocationAndRetentionPriority_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_EUTRANAllocationAndRetentionPriority_ExtIEs__extensionValue {
		E1AP_EUTRANAllocationAndRetentionPriority_ExtIEs__extensionValue_PR present;
		union E1AP_EUTRANAllocationAndRetentionPriority_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_EUTRANAllocationAndRetentionPriority_ExtIEs_t;
typedef struct E1AP_EUTRAN_QoS_Support_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_EUTRAN_QoS_Support_Item_ExtIEs__extensionValue {
		E1AP_EUTRAN_QoS_Support_Item_ExtIEs__extensionValue_PR present;
		union E1AP_EUTRAN_QoS_Support_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_EUTRAN_QoS_Support_Item_ExtIEs_t;
typedef struct E1AP_EUTRAN_QoS_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_EUTRAN_QoS_ExtIEs__extensionValue {
		E1AP_EUTRAN_QoS_ExtIEs__extensionValue_PR present;
		union E1AP_EUTRAN_QoS_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_EUTRAN_QoS_ExtIEs_t;
typedef struct E1AP_FirstDLCount_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_FirstDLCount_ExtIEs__extensionValue {
		E1AP_FirstDLCount_ExtIEs__extensionValue_PR present;
		union E1AP_FirstDLCount_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_FirstDLCount_ExtIEs_t;
typedef struct E1AP_Extended_GNB_CU_CP_Name_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_Extended_GNB_CU_CP_Name_ExtIEs__extensionValue {
		E1AP_Extended_GNB_CU_CP_Name_ExtIEs__extensionValue_PR present;
		union E1AP_Extended_GNB_CU_CP_Name_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_Extended_GNB_CU_CP_Name_ExtIEs_t;
typedef struct E1AP_GNB_CU_UP_CellGroupRelatedConfiguration_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GNB_CU_UP_CellGroupRelatedConfiguration_Item_ExtIEs__extensionValue {
		E1AP_GNB_CU_UP_CellGroupRelatedConfiguration_Item_ExtIEs__extensionValue_PR present;
		union E1AP_GNB_CU_UP_CellGroupRelatedConfiguration_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GNB_CU_UP_CellGroupRelatedConfiguration_Item_ExtIEs_t;
typedef struct E1AP_Extended_GNB_CU_UP_Name_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_Extended_GNB_CU_UP_Name_ExtIEs__extensionValue {
		E1AP_Extended_GNB_CU_UP_Name_ExtIEs__extensionValue_PR present;
		union E1AP_Extended_GNB_CU_UP_Name_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_Extended_GNB_CU_UP_Name_ExtIEs_t;
typedef struct E1AP_GNB_CU_CP_TNLA_Setup_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GNB_CU_CP_TNLA_Setup_Item_ExtIEs__extensionValue {
		E1AP_GNB_CU_CP_TNLA_Setup_Item_ExtIEs__extensionValue_PR present;
		union E1AP_GNB_CU_CP_TNLA_Setup_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GNB_CU_CP_TNLA_Setup_Item_ExtIEs_t;
typedef struct E1AP_GNB_CU_CP_TNLA_Failed_To_Setup_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GNB_CU_CP_TNLA_Failed_To_Setup_Item_ExtIEs__extensionValue {
		E1AP_GNB_CU_CP_TNLA_Failed_To_Setup_Item_ExtIEs__extensionValue_PR present;
		union E1AP_GNB_CU_CP_TNLA_Failed_To_Setup_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GNB_CU_CP_TNLA_Failed_To_Setup_Item_ExtIEs_t;
typedef struct E1AP_GNB_CU_CP_TNLA_To_Add_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GNB_CU_CP_TNLA_To_Add_Item_ExtIEs__extensionValue {
		E1AP_GNB_CU_CP_TNLA_To_Add_Item_ExtIEs__extensionValue_PR present;
		union E1AP_GNB_CU_CP_TNLA_To_Add_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GNB_CU_CP_TNLA_To_Add_Item_ExtIEs_t;
typedef struct E1AP_GNB_CU_CP_TNLA_To_Remove_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GNB_CU_CP_TNLA_To_Remove_Item_ExtIEs__extensionValue {
		E1AP_GNB_CU_CP_TNLA_To_Remove_Item_ExtIEs__extensionValue_PR present;
		union E1AP_GNB_CU_CP_TNLA_To_Remove_Item_ExtIEs__E1AP_extensionValue_u {
			E1AP_CP_TNL_Information_t	 CP_TNL_Information;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GNB_CU_CP_TNLA_To_Remove_Item_ExtIEs_t;
typedef struct E1AP_GNB_CU_CP_TNLA_To_Update_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GNB_CU_CP_TNLA_To_Update_Item_ExtIEs__extensionValue {
		E1AP_GNB_CU_CP_TNLA_To_Update_Item_ExtIEs__extensionValue_PR present;
		union E1AP_GNB_CU_CP_TNLA_To_Update_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GNB_CU_CP_TNLA_To_Update_Item_ExtIEs_t;
typedef struct E1AP_GNB_CU_UP_TNLA_To_Remove_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GNB_CU_UP_TNLA_To_Remove_Item_ExtIEs__extensionValue {
		E1AP_GNB_CU_UP_TNLA_To_Remove_Item_ExtIEs__extensionValue_PR present;
		union E1AP_GNB_CU_UP_TNLA_To_Remove_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GNB_CU_UP_TNLA_To_Remove_Item_ExtIEs_t;
typedef struct E1AP_GBR_QosInformation_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GBR_QosInformation_ExtIEs__extensionValue {
		E1AP_GBR_QosInformation_ExtIEs__extensionValue_PR present;
		union E1AP_GBR_QosInformation_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GBR_QosInformation_ExtIEs_t;
typedef struct E1AP_GBR_QosFlowInformation_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GBR_QosFlowInformation_ExtIEs__extensionValue {
		E1AP_GBR_QosFlowInformation_ExtIEs__extensionValue_PR present;
		union E1AP_GBR_QosFlowInformation_ExtIEs__E1AP_extensionValue_u {
			E1AP_AlternativeQoSParaSetList_t	 AlternativeQoSParaSetList;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GBR_QosFlowInformation_ExtIEs_t;
typedef struct E1AP_GTPTLA_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GTPTLA_Item_ExtIEs__extensionValue {
		E1AP_GTPTLA_Item_ExtIEs__extensionValue_PR present;
		union E1AP_GTPTLA_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GTPTLA_Item_ExtIEs_t;
typedef struct E1AP_GTPTunnel_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GTPTunnel_ExtIEs__extensionValue {
		E1AP_GTPTunnel_ExtIEs__extensionValue_PR present;
		union E1AP_GTPTunnel_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GTPTunnel_ExtIEs_t;
typedef struct E1AP_HW_CapacityIndicator_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_HW_CapacityIndicator_ExtIEs__extensionValue {
		E1AP_HW_CapacityIndicator_ExtIEs__extensionValue_PR present;
		union E1AP_HW_CapacityIndicator_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_HW_CapacityIndicator_ExtIEs_t;
typedef struct E1AP_ImmediateMDT_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_ImmediateMDT_ExtIEs__extensionValue {
		E1AP_ImmediateMDT_ExtIEs__extensionValue_PR present;
		union E1AP_ImmediateMDT_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ImmediateMDT_ExtIEs_t;
typedef struct E1AP_MaximumIPdatarate_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_MaximumIPdatarate_ExtIEs__extensionValue {
		E1AP_MaximumIPdatarate_ExtIEs__extensionValue_PR present;
		union E1AP_MaximumIPdatarate_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_MaximumIPdatarate_ExtIEs_t;
typedef struct E1AP_MRDC_Data_Usage_Report_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_MRDC_Data_Usage_Report_Item_ExtIEs__extensionValue {
		E1AP_MRDC_Data_Usage_Report_Item_ExtIEs__extensionValue_PR present;
		union E1AP_MRDC_Data_Usage_Report_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_MRDC_Data_Usage_Report_Item_ExtIEs_t;
typedef struct E1AP_MRDC_Usage_Information_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_MRDC_Usage_Information_ExtIEs__extensionValue {
		E1AP_MRDC_Usage_Information_ExtIEs__extensionValue_PR present;
		union E1AP_MRDC_Usage_Information_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_MRDC_Usage_Information_ExtIEs_t;
typedef struct E1AP_M4Configuration_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_M4Configuration_ExtIEs__extensionValue {
		E1AP_M4Configuration_ExtIEs__extensionValue_PR present;
		union E1AP_M4Configuration_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_M4Configuration_ExtIEs_t;
typedef struct E1AP_M6Configuration_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_M6Configuration_ExtIEs__extensionValue {
		E1AP_M6Configuration_ExtIEs__extensionValue_PR present;
		union E1AP_M6Configuration_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_M6Configuration_ExtIEs_t;
typedef struct E1AP_M7Configuration_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_M7Configuration_ExtIEs__extensionValue {
		E1AP_M7Configuration_ExtIEs__extensionValue_PR present;
		union E1AP_M7Configuration_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_M7Configuration_ExtIEs_t;
typedef struct E1AP_MDT_Configuration_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_MDT_Configuration_ExtIEs__extensionValue {
		E1AP_MDT_Configuration_ExtIEs__extensionValue_PR present;
		union E1AP_MDT_Configuration_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_MDT_Configuration_ExtIEs_t;
typedef struct E1AP_NGRANAllocationAndRetentionPriority_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_NGRANAllocationAndRetentionPriority_ExtIEs__extensionValue {
		E1AP_NGRANAllocationAndRetentionPriority_ExtIEs__extensionValue_PR present;
		union E1AP_NGRANAllocationAndRetentionPriority_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_NGRANAllocationAndRetentionPriority_ExtIEs_t;
typedef struct E1AP_NG_RAN_QoS_Support_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_NG_RAN_QoS_Support_Item_ExtIEs__extensionValue {
		E1AP_NG_RAN_QoS_Support_Item_ExtIEs__extensionValue_PR present;
		union E1AP_NG_RAN_QoS_Support_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_NG_RAN_QoS_Support_Item_ExtIEs_t;
typedef struct E1AP_Non_Dynamic5QIDescriptor_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_Non_Dynamic5QIDescriptor_ExtIEs__extensionValue {
		E1AP_Non_Dynamic5QIDescriptor_ExtIEs__extensionValue_PR present;
		union E1AP_Non_Dynamic5QIDescriptor_ExtIEs__E1AP_extensionValue_u {
			E1AP_ExtendedPacketDelayBudget_t	 ExtendedPacketDelayBudget;
			E1AP_ExtendedPacketDelayBudget_t	 ExtendedPacketDelayBudget_1;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_Non_Dynamic5QIDescriptor_ExtIEs_t;
typedef struct E1AP_NPNSupportInfo_SNPN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_NPNSupportInfo_SNPN_ExtIEs__extensionValue {
		E1AP_NPNSupportInfo_SNPN_ExtIEs__extensionValue_PR present;
		union E1AP_NPNSupportInfo_SNPN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_NPNSupportInfo_SNPN_ExtIEs_t;
typedef struct E1AP_NPNContextInfo_SNPN_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_NPNContextInfo_SNPN_ExtIEs__extensionValue {
		E1AP_NPNContextInfo_SNPN_ExtIEs__extensionValue_PR present;
		union E1AP_NPNContextInfo_SNPN_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_NPNContextInfo_SNPN_ExtIEs_t;
typedef struct E1AP_NR_CGI_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_NR_CGI_ExtIEs__extensionValue {
		E1AP_NR_CGI_ExtIEs__extensionValue_PR present;
		union E1AP_NR_CGI_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_NR_CGI_ExtIEs_t;
typedef struct E1AP_NR_CGI_Support_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_NR_CGI_Support_Item_ExtIEs__extensionValue {
		E1AP_NR_CGI_Support_Item_ExtIEs__extensionValue_PR present;
		union E1AP_NR_CGI_Support_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_NR_CGI_Support_Item_ExtIEs_t;
typedef struct E1AP_Extended_NR_CGI_Support_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_Extended_NR_CGI_Support_Item_ExtIEs__extensionValue {
		E1AP_Extended_NR_CGI_Support_Item_ExtIEs__extensionValue_PR present;
		union E1AP_Extended_NR_CGI_Support_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_Extended_NR_CGI_Support_Item_ExtIEs_t;
typedef struct E1AP_PacketErrorRate_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_PacketErrorRate_ExtIEs__extensionValue {
		E1AP_PacketErrorRate_ExtIEs__extensionValue_PR present;
		union E1AP_PacketErrorRate_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_PacketErrorRate_ExtIEs_t;
typedef struct E1AP_PDCP_Configuration_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_PDCP_Configuration_ExtIEs__extensionValue {
		E1AP_PDCP_Configuration_ExtIEs__extensionValue_PR present;
		union E1AP_PDCP_Configuration_ExtIEs__E1AP_extensionValue_u {
			E1AP_PDCP_StatusReportIndication_t	 PDCP_StatusReportIndication;
			E1AP_AdditionalPDCPduplicationInformation_t	 AdditionalPDCPduplicationInformation;
			E1AP_EHC_Parameters_t	 EHC_Parameters;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_PDCP_Configuration_ExtIEs_t;
typedef struct E1AP_PDCP_Count_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_PDCP_Count_ExtIEs__extensionValue {
		E1AP_PDCP_Count_ExtIEs__extensionValue_PR present;
		union E1AP_PDCP_Count_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_PDCP_Count_ExtIEs_t;
typedef struct E1AP_PDU_Session_Resource_Data_Usage_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_PDU_Session_Resource_Data_Usage_Item_ExtIEs__extensionValue {
		E1AP_PDU_Session_Resource_Data_Usage_Item_ExtIEs__extensionValue_PR present;
		union E1AP_PDU_Session_Resource_Data_Usage_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_PDU_Session_Resource_Data_Usage_Item_ExtIEs_t;
typedef struct E1AP_PDCP_SN_Status_Information_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_PDCP_SN_Status_Information_ExtIEs__extensionValue {
		E1AP_PDCP_SN_Status_Information_ExtIEs__extensionValue_PR present;
		union E1AP_PDCP_SN_Status_Information_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_PDCP_SN_Status_Information_ExtIEs_t;
typedef struct E1AP_DRBBStatusTransfer_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DRBBStatusTransfer_ExtIEs__extensionValue {
		E1AP_DRBBStatusTransfer_ExtIEs__extensionValue_PR present;
		union E1AP_DRBBStatusTransfer_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DRBBStatusTransfer_ExtIEs_t;
typedef struct E1AP_PDU_Session_Resource_Activity_ItemExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_PDU_Session_Resource_Activity_ItemExtIEs__extensionValue {
		E1AP_PDU_Session_Resource_Activity_ItemExtIEs__extensionValue_PR present;
		union E1AP_PDU_Session_Resource_Activity_ItemExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_PDU_Session_Resource_Activity_ItemExtIEs_t;
typedef struct E1AP_PDU_Session_Resource_Confirm_Modified_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_PDU_Session_Resource_Confirm_Modified_Item_ExtIEs__extensionValue {
		E1AP_PDU_Session_Resource_Confirm_Modified_Item_ExtIEs__extensionValue_PR present;
		union E1AP_PDU_Session_Resource_Confirm_Modified_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_PDU_Session_Resource_Confirm_Modified_Item_ExtIEs_t;
typedef struct E1AP_PDU_Session_Resource_Failed_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_PDU_Session_Resource_Failed_Item_ExtIEs__extensionValue {
		E1AP_PDU_Session_Resource_Failed_Item_ExtIEs__extensionValue_PR present;
		union E1AP_PDU_Session_Resource_Failed_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_PDU_Session_Resource_Failed_Item_ExtIEs_t;
typedef struct E1AP_PDU_Session_Resource_Failed_Mod_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_PDU_Session_Resource_Failed_Mod_Item_ExtIEs__extensionValue {
		E1AP_PDU_Session_Resource_Failed_Mod_Item_ExtIEs__extensionValue_PR present;
		union E1AP_PDU_Session_Resource_Failed_Mod_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_PDU_Session_Resource_Failed_Mod_Item_ExtIEs_t;
typedef struct E1AP_PDU_Session_Resource_Failed_To_Modify_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_PDU_Session_Resource_Failed_To_Modify_Item_ExtIEs__extensionValue {
		E1AP_PDU_Session_Resource_Failed_To_Modify_Item_ExtIEs__extensionValue_PR present;
		union E1AP_PDU_Session_Resource_Failed_To_Modify_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_PDU_Session_Resource_Failed_To_Modify_Item_ExtIEs_t;
typedef struct E1AP_PDU_Session_Resource_Modified_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_PDU_Session_Resource_Modified_Item_ExtIEs__extensionValue {
		E1AP_PDU_Session_Resource_Modified_Item_ExtIEs__extensionValue_PR present;
		union E1AP_PDU_Session_Resource_Modified_Item_ExtIEs__E1AP_extensionValue_u {
			E1AP_UP_TNL_Information_t	 UP_TNL_Information;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_PDU_Session_Resource_Modified_Item_ExtIEs_t;
typedef struct E1AP_PDU_Session_Resource_Required_To_Modify_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_PDU_Session_Resource_Required_To_Modify_Item_ExtIEs__extensionValue {
		E1AP_PDU_Session_Resource_Required_To_Modify_Item_ExtIEs__extensionValue_PR present;
		union E1AP_PDU_Session_Resource_Required_To_Modify_Item_ExtIEs__E1AP_extensionValue_u {
			E1AP_UP_TNL_Information_t	 UP_TNL_Information;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_PDU_Session_Resource_Required_To_Modify_Item_ExtIEs_t;
typedef struct E1AP_PDU_Session_Resource_Setup_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_PDU_Session_Resource_Setup_Item_ExtIEs__extensionValue {
		E1AP_PDU_Session_Resource_Setup_Item_ExtIEs__extensionValue_PR present;
		union E1AP_PDU_Session_Resource_Setup_Item_ExtIEs__E1AP_extensionValue_u {
			E1AP_UP_TNL_Information_t	 UP_TNL_Information;
			E1AP_RedundantPDUSessionInformation_t	 RedundantPDUSessionInformation;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_PDU_Session_Resource_Setup_Item_ExtIEs_t;
typedef struct E1AP_PDU_Session_Resource_Setup_Mod_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_PDU_Session_Resource_Setup_Mod_Item_ExtIEs__extensionValue {
		E1AP_PDU_Session_Resource_Setup_Mod_Item_ExtIEs__extensionValue_PR present;
		union E1AP_PDU_Session_Resource_Setup_Mod_Item_ExtIEs__E1AP_extensionValue_u {
			E1AP_UP_TNL_Information_t	 UP_TNL_Information;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_PDU_Session_Resource_Setup_Mod_Item_ExtIEs_t;
typedef struct E1AP_PDU_Session_Resource_To_Modify_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_PDU_Session_Resource_To_Modify_Item_ExtIEs__extensionValue {
		E1AP_PDU_Session_Resource_To_Modify_Item_ExtIEs__extensionValue_PR present;
		union E1AP_PDU_Session_Resource_To_Modify_Item_ExtIEs__E1AP_extensionValue_u {
			E1AP_SNSSAI_t	 SNSSAI;
			E1AP_CommonNetworkInstance_t	 CommonNetworkInstance;
			E1AP_UP_TNL_Information_t	 UP_TNL_Information;
			E1AP_CommonNetworkInstance_t	 CommonNetworkInstance_1;
			E1AP_DataForwardingtoE_UTRANInformationList_t	 DataForwardingtoE_UTRANInformationList;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_PDU_Session_Resource_To_Modify_Item_ExtIEs_t;
typedef struct E1AP_PDU_Session_Resource_To_Remove_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_PDU_Session_Resource_To_Remove_Item_ExtIEs__extensionValue {
		E1AP_PDU_Session_Resource_To_Remove_Item_ExtIEs__extensionValue_PR present;
		union E1AP_PDU_Session_Resource_To_Remove_Item_ExtIEs__E1AP_extensionValue_u {
			E1AP_Cause_t	 Cause;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_PDU_Session_Resource_To_Remove_Item_ExtIEs_t;
typedef struct E1AP_PDU_Session_Resource_To_Setup_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_PDU_Session_Resource_To_Setup_Item_ExtIEs__extensionValue {
		E1AP_PDU_Session_Resource_To_Setup_Item_ExtIEs__extensionValue_PR present;
		union E1AP_PDU_Session_Resource_To_Setup_Item_ExtIEs__E1AP_extensionValue_u {
			E1AP_CommonNetworkInstance_t	 CommonNetworkInstance;
			E1AP_UP_TNL_Information_t	 UP_TNL_Information;
			E1AP_CommonNetworkInstance_t	 CommonNetworkInstance_1;
			E1AP_RedundantPDUSessionInformation_t	 RedundantPDUSessionInformation;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_PDU_Session_Resource_To_Setup_Item_ExtIEs_t;
typedef struct E1AP_PDU_Session_Resource_To_Setup_Mod_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_PDU_Session_Resource_To_Setup_Mod_Item_ExtIEs__extensionValue {
		E1AP_PDU_Session_Resource_To_Setup_Mod_Item_ExtIEs__extensionValue_PR present;
		union E1AP_PDU_Session_Resource_To_Setup_Mod_Item_ExtIEs__E1AP_extensionValue_u {
			E1AP_NetworkInstance_t	 NetworkInstance;
			E1AP_CommonNetworkInstance_t	 CommonNetworkInstance;
			E1AP_UP_TNL_Information_t	 UP_TNL_Information;
			E1AP_CommonNetworkInstance_t	 CommonNetworkInstance_1;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_PDU_Session_Resource_To_Setup_Mod_Item_ExtIEs_t;
typedef struct E1AP_PDU_Session_To_Notify_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_PDU_Session_To_Notify_Item_ExtIEs__extensionValue {
		E1AP_PDU_Session_To_Notify_Item_ExtIEs__extensionValue_PR present;
		union E1AP_PDU_Session_To_Notify_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_PDU_Session_To_Notify_Item_ExtIEs_t;
typedef struct E1AP_QoS_Flow_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_QoS_Flow_Item_ExtIEs__extensionValue {
		E1AP_QoS_Flow_Item_ExtIEs__extensionValue_PR present;
		union E1AP_QoS_Flow_Item_ExtIEs__E1AP_extensionValue_u {
			E1AP_QoS_Flow_Mapping_Indication_t	 QoS_Flow_Mapping_Indication;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_QoS_Flow_Item_ExtIEs_t;
typedef struct E1AP_QoS_Flow_Failed_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_QoS_Flow_Failed_Item_ExtIEs__extensionValue {
		E1AP_QoS_Flow_Failed_Item_ExtIEs__extensionValue_PR present;
		union E1AP_QoS_Flow_Failed_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_QoS_Flow_Failed_Item_ExtIEs_t;
typedef struct E1AP_QoS_Flow_Mapping_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_QoS_Flow_Mapping_Item_ExtIEs__extensionValue {
		E1AP_QoS_Flow_Mapping_Item_ExtIEs__extensionValue_PR present;
		union E1AP_QoS_Flow_Mapping_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_QoS_Flow_Mapping_Item_ExtIEs_t;
typedef struct E1AP_QoS_Parameters_Support_List_ItemExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_QoS_Parameters_Support_List_ItemExtIEs__extensionValue {
		E1AP_QoS_Parameters_Support_List_ItemExtIEs__extensionValue_PR present;
		union E1AP_QoS_Parameters_Support_List_ItemExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_QoS_Parameters_Support_List_ItemExtIEs_t;
typedef struct E1AP_QoS_Flow_QoS_Parameter_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_QoS_Flow_QoS_Parameter_Item_ExtIEs__extensionValue {
		E1AP_QoS_Flow_QoS_Parameter_Item_ExtIEs__extensionValue_PR present;
		union E1AP_QoS_Flow_QoS_Parameter_Item_ExtIEs__E1AP_extensionValue_u {
			E1AP_RedundantQoSFlowIndicator_t	 RedundantQoSFlowIndicator;
			E1AP_TSCTrafficCharacteristics_t	 TSCTrafficCharacteristics;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_QoS_Flow_QoS_Parameter_Item_ExtIEs_t;
typedef struct E1AP_QoSFlowLevelQoSParameters_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_QoSFlowLevelQoSParameters_ExtIEs__extensionValue {
		E1AP_QoSFlowLevelQoSParameters_ExtIEs__extensionValue_PR present;
		union E1AP_QoSFlowLevelQoSParameters_ExtIEs__E1AP_extensionValue_u {
			E1AP_QosMonitoringRequest_t	 QosMonitoringRequest;
			E1AP_GBR_QoSFlowInformation_t	 GBR_QoSFlowInformation;
			E1AP_QosMonitoringReportingFrequency_t	 QosMonitoringReportingFrequency;
			E1AP_QosMonitoringDisabled_t	 QosMonitoringDisabled;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_QoSFlowLevelQoSParameters_ExtIEs_t;
typedef struct E1AP_QoS_Flow_Removed_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_QoS_Flow_Removed_Item_ExtIEs__extensionValue {
		E1AP_QoS_Flow_Removed_Item_ExtIEs__extensionValue_PR present;
		union E1AP_QoS_Flow_Removed_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_QoS_Flow_Removed_Item_ExtIEs_t;
typedef struct E1AP_QoS_Flows_to_be_forwarded_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_QoS_Flows_to_be_forwarded_Item_ExtIEs__extensionValue {
		E1AP_QoS_Flows_to_be_forwarded_Item_ExtIEs__extensionValue_PR present;
		union E1AP_QoS_Flows_to_be_forwarded_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_QoS_Flows_to_be_forwarded_Item_ExtIEs_t;
typedef struct E1AP_DataForwardingtoNG_RANQoSFlowInformationList_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DataForwardingtoNG_RANQoSFlowInformationList_Item_ExtIEs__extensionValue {
		E1AP_DataForwardingtoNG_RANQoSFlowInformationList_Item_ExtIEs__extensionValue_PR present;
		union E1AP_DataForwardingtoNG_RANQoSFlowInformationList_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DataForwardingtoNG_RANQoSFlowInformationList_Item_ExtIEs_t;
typedef struct E1AP_RedundantPDUSessionInformation_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_RedundantPDUSessionInformation_ExtIEs__extensionValue {
		E1AP_RedundantPDUSessionInformation_ExtIEs__extensionValue_PR present;
		union E1AP_RedundantPDUSessionInformation_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_RedundantPDUSessionInformation_ExtIEs_t;
typedef struct E1AP_ROHC_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_ROHC_ExtIEs__extensionValue {
		E1AP_ROHC_ExtIEs__extensionValue_PR present;
		union E1AP_ROHC_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ROHC_ExtIEs_t;
typedef struct E1AP_SecurityAlgorithm_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_SecurityAlgorithm_ExtIEs__extensionValue {
		E1AP_SecurityAlgorithm_ExtIEs__extensionValue_PR present;
		union E1AP_SecurityAlgorithm_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_SecurityAlgorithm_ExtIEs_t;
typedef struct E1AP_SecurityIndication_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_SecurityIndication_ExtIEs__extensionValue {
		E1AP_SecurityIndication_ExtIEs__extensionValue_PR present;
		union E1AP_SecurityIndication_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_SecurityIndication_ExtIEs_t;
typedef struct E1AP_SecurityInformation_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_SecurityInformation_ExtIEs__extensionValue {
		E1AP_SecurityInformation_ExtIEs__extensionValue_PR present;
		union E1AP_SecurityInformation_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_SecurityInformation_ExtIEs_t;
typedef struct E1AP_SecurityResult_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_SecurityResult_ExtIEs__extensionValue {
		E1AP_SecurityResult_ExtIEs__extensionValue_PR present;
		union E1AP_SecurityResult_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_SecurityResult_ExtIEs_t;
typedef struct E1AP_Slice_Support_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_Slice_Support_Item_ExtIEs__extensionValue {
		E1AP_Slice_Support_Item_ExtIEs__extensionValue_PR present;
		union E1AP_Slice_Support_Item_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_Slice_Support_Item_ExtIEs_t;
typedef struct E1AP_SNSSAI_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_SNSSAI_ExtIEs__extensionValue {
		E1AP_SNSSAI_ExtIEs__extensionValue_PR present;
		union E1AP_SNSSAI_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_SNSSAI_ExtIEs_t;
typedef struct E1AP_SDAP_Configuration_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_SDAP_Configuration_ExtIEs__extensionValue {
		E1AP_SDAP_Configuration_ExtIEs__extensionValue_PR present;
		union E1AP_SDAP_Configuration_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_SDAP_Configuration_ExtIEs_t;
typedef struct E1AP_TNL_AvailableCapacityIndicator_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_TNL_AvailableCapacityIndicator_ExtIEs__extensionValue {
		E1AP_TNL_AvailableCapacityIndicator_ExtIEs__extensionValue_PR present;
		union E1AP_TNL_AvailableCapacityIndicator_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_TNL_AvailableCapacityIndicator_ExtIEs_t;
typedef struct E1AP_TSCTrafficCharacteristics_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_TSCTrafficCharacteristics_ExtIEs__extensionValue {
		E1AP_TSCTrafficCharacteristics_ExtIEs__extensionValue_PR present;
		union E1AP_TSCTrafficCharacteristics_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_TSCTrafficCharacteristics_ExtIEs_t;
typedef struct E1AP_TSCTrafficInformation_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_TSCTrafficInformation_ExtIEs__extensionValue {
		E1AP_TSCTrafficInformation_ExtIEs__extensionValue_PR present;
		union E1AP_TSCTrafficInformation_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_TSCTrafficInformation_ExtIEs_t;
typedef struct E1AP_TraceActivation_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_TraceActivation_ExtIEs__extensionValue {
		E1AP_TraceActivation_ExtIEs__extensionValue_PR present;
		union E1AP_TraceActivation_ExtIEs__E1AP_extensionValue_u {
			E1AP_MDT_Configuration_t	 MDT_Configuration;
			E1AP_URIaddress_t	 URIaddress;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_TraceActivation_ExtIEs_t;
typedef struct E1AP_T_ReorderingTimer_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_T_ReorderingTimer_ExtIEs__extensionValue {
		E1AP_T_ReorderingTimer_ExtIEs__extensionValue_PR present;
		union E1AP_T_ReorderingTimer_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_T_ReorderingTimer_ExtIEs_t;
typedef struct E1AP_Transport_Layer_Address_Info_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_Transport_Layer_Address_Info_ExtIEs__extensionValue {
		E1AP_Transport_Layer_Address_Info_ExtIEs__extensionValue_PR present;
		union E1AP_Transport_Layer_Address_Info_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_Transport_Layer_Address_Info_ExtIEs_t;
typedef struct E1AP_Transport_UP_Layer_Addresses_Info_To_Add_ItemExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_Transport_UP_Layer_Addresses_Info_To_Add_ItemExtIEs__extensionValue {
		E1AP_Transport_UP_Layer_Addresses_Info_To_Add_ItemExtIEs__extensionValue_PR present;
		union E1AP_Transport_UP_Layer_Addresses_Info_To_Add_ItemExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_Transport_UP_Layer_Addresses_Info_To_Add_ItemExtIEs_t;
typedef struct E1AP_Transport_UP_Layer_Addresses_Info_To_Remove_ItemExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_Transport_UP_Layer_Addresses_Info_To_Remove_ItemExtIEs__extensionValue {
		E1AP_Transport_UP_Layer_Addresses_Info_To_Remove_ItemExtIEs__extensionValue_PR present;
		union E1AP_Transport_UP_Layer_Addresses_Info_To_Remove_ItemExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_Transport_UP_Layer_Addresses_Info_To_Remove_ItemExtIEs_t;
typedef struct E1AP_UE_associatedLogicalE1_ConnectionItemExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_UE_associatedLogicalE1_ConnectionItemExtIEs__extensionValue {
		E1AP_UE_associatedLogicalE1_ConnectionItemExtIEs__extensionValue_PR present;
		union E1AP_UE_associatedLogicalE1_ConnectionItemExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_UE_associatedLogicalE1_ConnectionItemExtIEs_t;
typedef struct E1AP_ULUPTNLAddressToUpdateItemExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_ULUPTNLAddressToUpdateItemExtIEs__extensionValue {
		E1AP_ULUPTNLAddressToUpdateItemExtIEs__extensionValue_PR present;
		union E1AP_ULUPTNLAddressToUpdateItemExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ULUPTNLAddressToUpdateItemExtIEs_t;
typedef struct E1AP_UP_Parameters_Item_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_UP_Parameters_Item_ExtIEs__extensionValue {
		E1AP_UP_Parameters_Item_ExtIEs__extensionValue_PR present;
		union E1AP_UP_Parameters_Item_ExtIEs__E1AP_extensionValue_u {
			E1AP_QoS_Mapping_Information_t	 QoS_Mapping_Information;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_UP_Parameters_Item_ExtIEs_t;
typedef struct E1AP_UPSecuritykey_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_UPSecuritykey_ExtIEs__extensionValue {
		E1AP_UPSecuritykey_ExtIEs__extensionValue_PR present;
		union E1AP_UPSecuritykey_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_UPSecuritykey_ExtIEs_t;
typedef struct E1AP_UplinkOnlyROHC_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_UplinkOnlyROHC_ExtIEs__extensionValue {
		E1AP_UplinkOnlyROHC_ExtIEs__extensionValue_PR present;
		union E1AP_UplinkOnlyROHC_ExtIEs__E1AP_extensionValue_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} extensionValue;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_UplinkOnlyROHC_ExtIEs_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_E1AP_SupportedPLMNs_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_SupportedPLMNs_ExtIEs_specs_1;
extern asn_TYPE_member_t asn_MBR_E1AP_SupportedPLMNs_ExtIEs_1[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_AlternativeQoSParaSetItem_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_AlternativeQoSParaSetItem_ExtIEs_specs_5;
extern asn_TYPE_member_t asn_MBR_E1AP_AlternativeQoSParaSetItem_ExtIEs_5[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_Cell_Group_Information_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_Cell_Group_Information_Item_ExtIEs_specs_9;
extern asn_TYPE_member_t asn_MBR_E1AP_Cell_Group_Information_Item_ExtIEs_9[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_CriticalityDiagnostics_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_CriticalityDiagnostics_ExtIEs_specs_13;
extern asn_TYPE_member_t asn_MBR_E1AP_CriticalityDiagnostics_ExtIEs_13[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_CriticalityDiagnostics_IE_List_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_CriticalityDiagnostics_IE_List_ExtIEs_specs_17;
extern asn_TYPE_member_t asn_MBR_E1AP_CriticalityDiagnostics_IE_List_ExtIEs_17[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DAPSRequestInfo_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DAPSRequestInfo_ExtIEs_specs_21;
extern asn_TYPE_member_t asn_MBR_E1AP_DAPSRequestInfo_ExtIEs_21[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_Data_Forwarding_Information_Request_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_Data_Forwarding_Information_Request_ExtIEs_specs_25;
extern asn_TYPE_member_t asn_MBR_E1AP_Data_Forwarding_Information_Request_ExtIEs_25[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_Data_Forwarding_Information_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_Data_Forwarding_Information_ExtIEs_specs_29;
extern asn_TYPE_member_t asn_MBR_E1AP_Data_Forwarding_Information_ExtIEs_29[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DataForwardingtoE_UTRANInformationListItem_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DataForwardingtoE_UTRANInformationListItem_ExtIEs_specs_33;
extern asn_TYPE_member_t asn_MBR_E1AP_DataForwardingtoE_UTRANInformationListItem_ExtIEs_33[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_Data_Usage_per_PDU_Session_Report_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_Data_Usage_per_PDU_Session_Report_ExtIEs_specs_37;
extern asn_TYPE_member_t asn_MBR_E1AP_Data_Usage_per_PDU_Session_Report_ExtIEs_37[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_Data_Usage_per_QoS_Flow_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_Data_Usage_per_QoS_Flow_Item_ExtIEs_specs_41;
extern asn_TYPE_member_t asn_MBR_E1AP_Data_Usage_per_QoS_Flow_Item_ExtIEs_41[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_Data_Usage_Report_ItemExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_Data_Usage_Report_ItemExtIEs_specs_45;
extern asn_TYPE_member_t asn_MBR_E1AP_Data_Usage_Report_ItemExtIEs_45[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DLDiscarding_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DLDiscarding_ExtIEs_specs_49;
extern asn_TYPE_member_t asn_MBR_E1AP_DLDiscarding_ExtIEs_49[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DLUPTNLAddressToUpdateItemExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DLUPTNLAddressToUpdateItemExtIEs_specs_53;
extern asn_TYPE_member_t asn_MBR_E1AP_DLUPTNLAddressToUpdateItemExtIEs_53[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_Activity_ItemExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_Activity_ItemExtIEs_specs_57;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_Activity_ItemExtIEs_57[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_Confirm_Modified_Item_EUTRAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_Confirm_Modified_Item_EUTRAN_ExtIEs_specs_61;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_Confirm_Modified_Item_EUTRAN_ExtIEs_61[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_Confirm_Modified_Item_NG_RAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_Confirm_Modified_Item_NG_RAN_ExtIEs_specs_65;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_Confirm_Modified_Item_NG_RAN_ExtIEs_65[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_Failed_Item_EUTRAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_Failed_Item_EUTRAN_ExtIEs_specs_69;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_Failed_Item_EUTRAN_ExtIEs_69[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_Failed_Mod_Item_EUTRAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_Failed_Mod_Item_EUTRAN_ExtIEs_specs_73;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_Failed_Mod_Item_EUTRAN_ExtIEs_73[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_Failed_Item_NG_RAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_Failed_Item_NG_RAN_ExtIEs_specs_77;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_Failed_Item_NG_RAN_ExtIEs_77[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_Failed_Mod_Item_NG_RAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_Failed_Mod_Item_NG_RAN_ExtIEs_specs_81;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_Failed_Mod_Item_NG_RAN_ExtIEs_81[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_Failed_To_Modify_Item_EUTRAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_Failed_To_Modify_Item_EUTRAN_ExtIEs_specs_85;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_Failed_To_Modify_Item_EUTRAN_ExtIEs_85[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_Failed_To_Modify_Item_NG_RAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_Failed_To_Modify_Item_NG_RAN_ExtIEs_specs_89;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_Failed_To_Modify_Item_NG_RAN_ExtIEs_89[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_Measurement_Results_Information_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_Measurement_Results_Information_Item_ExtIEs_specs_93;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_Measurement_Results_Information_Item_ExtIEs_93[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_Modified_Item_EUTRAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_Modified_Item_EUTRAN_ExtIEs_specs_97;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_Modified_Item_EUTRAN_ExtIEs_97[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_Modified_Item_NG_RAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_Modified_Item_NG_RAN_ExtIEs_specs_101;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_Modified_Item_NG_RAN_ExtIEs_101[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_Removed_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_Removed_Item_ExtIEs_specs_105;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_Removed_Item_ExtIEs_105[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_Required_To_Modify_Item_EUTRAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_Required_To_Modify_Item_EUTRAN_ExtIEs_specs_109;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_Required_To_Modify_Item_EUTRAN_ExtIEs_109[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_Required_To_Modify_Item_NG_RAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_Required_To_Modify_Item_NG_RAN_ExtIEs_specs_113;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_Required_To_Modify_Item_NG_RAN_ExtIEs_113[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_Setup_Item_EUTRAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_Setup_Item_EUTRAN_ExtIEs_specs_117;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_Setup_Item_EUTRAN_ExtIEs_117[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_Setup_Mod_Item_EUTRAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_Setup_Mod_Item_EUTRAN_ExtIEs_specs_121;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_Setup_Mod_Item_EUTRAN_ExtIEs_121[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_Setup_Item_NG_RAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_Setup_Item_NG_RAN_ExtIEs_specs_125;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_Setup_Item_NG_RAN_ExtIEs_125[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_Setup_Mod_Item_NG_RAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_Setup_Mod_Item_NG_RAN_ExtIEs_specs_129;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_Setup_Mod_Item_NG_RAN_ExtIEs_129[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_Status_ItemExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_Status_ItemExtIEs_specs_133;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_Status_ItemExtIEs_133[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRBs_Subject_To_Counter_Check_Item_EUTRAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRBs_Subject_To_Counter_Check_Item_EUTRAN_ExtIEs_specs_137;
extern asn_TYPE_member_t asn_MBR_E1AP_DRBs_Subject_To_Counter_Check_Item_EUTRAN_ExtIEs_137[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRBs_Subject_To_Counter_Check_Item_NG_RAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRBs_Subject_To_Counter_Check_Item_NG_RAN_ExtIEs_specs_141;
extern asn_TYPE_member_t asn_MBR_E1AP_DRBs_Subject_To_Counter_Check_Item_NG_RAN_ExtIEs_141[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRBs_Subject_To_Early_Forwarding_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRBs_Subject_To_Early_Forwarding_Item_ExtIEs_specs_145;
extern asn_TYPE_member_t asn_MBR_E1AP_DRBs_Subject_To_Early_Forwarding_Item_ExtIEs_145[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_To_Modify_Item_EUTRAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_To_Modify_Item_EUTRAN_ExtIEs_specs_149;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_To_Modify_Item_EUTRAN_ExtIEs_149[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_To_Modify_Item_NG_RAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_To_Modify_Item_NG_RAN_ExtIEs_specs_153;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_To_Modify_Item_NG_RAN_ExtIEs_153[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_To_Remove_Item_EUTRAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_To_Remove_Item_EUTRAN_ExtIEs_specs_157;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_To_Remove_Item_EUTRAN_ExtIEs_157[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_Required_To_Remove_Item_EUTRAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_Required_To_Remove_Item_EUTRAN_ExtIEs_specs_161;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_Required_To_Remove_Item_EUTRAN_ExtIEs_161[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_To_Remove_Item_NG_RAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_To_Remove_Item_NG_RAN_ExtIEs_specs_165;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_To_Remove_Item_NG_RAN_ExtIEs_165[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_Required_To_Remove_Item_NG_RAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_Required_To_Remove_Item_NG_RAN_ExtIEs_specs_169;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_Required_To_Remove_Item_NG_RAN_ExtIEs_169[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_To_Setup_Item_EUTRAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_To_Setup_Item_EUTRAN_ExtIEs_specs_173;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_To_Setup_Item_EUTRAN_ExtIEs_173[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_To_Setup_Mod_Item_EUTRAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_To_Setup_Mod_Item_EUTRAN_ExtIEs_specs_177;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_To_Setup_Mod_Item_EUTRAN_ExtIEs_177[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_To_Setup_Item_NG_RAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_To_Setup_Item_NG_RAN_ExtIEs_specs_181;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_To_Setup_Item_NG_RAN_ExtIEs_181[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_To_Setup_Mod_Item_NG_RAN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_To_Setup_Mod_Item_NG_RAN_ExtIEs_specs_185;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_To_Setup_Mod_Item_NG_RAN_ExtIEs_185[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRB_Usage_Report_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRB_Usage_Report_Item_ExtIEs_specs_189;
extern asn_TYPE_member_t asn_MBR_E1AP_DRB_Usage_Report_Item_ExtIEs_189[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_Dynamic5QIDescriptor_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_Dynamic5QIDescriptor_ExtIEs_specs_193;
extern asn_TYPE_member_t asn_MBR_E1AP_Dynamic5QIDescriptor_ExtIEs_193[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_EHC_Common_Parameters_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_EHC_Common_Parameters_ExtIEs_specs_197;
extern asn_TYPE_member_t asn_MBR_E1AP_EHC_Common_Parameters_ExtIEs_197[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_EHC_Downlink_Parameters_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_EHC_Downlink_Parameters_ExtIEs_specs_201;
extern asn_TYPE_member_t asn_MBR_E1AP_EHC_Downlink_Parameters_ExtIEs_201[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_EHC_Uplink_Parameters_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_EHC_Uplink_Parameters_ExtIEs_specs_205;
extern asn_TYPE_member_t asn_MBR_E1AP_EHC_Uplink_Parameters_ExtIEs_205[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_EHC_Parameters_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_EHC_Parameters_ExtIEs_specs_209;
extern asn_TYPE_member_t asn_MBR_E1AP_EHC_Parameters_ExtIEs_209[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_Endpoint_IP_address_and_port_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_Endpoint_IP_address_and_port_ExtIEs_specs_213;
extern asn_TYPE_member_t asn_MBR_E1AP_Endpoint_IP_address_and_port_ExtIEs_213[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_EUTRANAllocationAndRetentionPriority_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_EUTRANAllocationAndRetentionPriority_ExtIEs_specs_217;
extern asn_TYPE_member_t asn_MBR_E1AP_EUTRANAllocationAndRetentionPriority_ExtIEs_217[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_EUTRAN_QoS_Support_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_EUTRAN_QoS_Support_Item_ExtIEs_specs_221;
extern asn_TYPE_member_t asn_MBR_E1AP_EUTRAN_QoS_Support_Item_ExtIEs_221[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_EUTRAN_QoS_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_EUTRAN_QoS_ExtIEs_specs_225;
extern asn_TYPE_member_t asn_MBR_E1AP_EUTRAN_QoS_ExtIEs_225[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_FirstDLCount_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_FirstDLCount_ExtIEs_specs_229;
extern asn_TYPE_member_t asn_MBR_E1AP_FirstDLCount_ExtIEs_229[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_Extended_GNB_CU_CP_Name_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_Extended_GNB_CU_CP_Name_ExtIEs_specs_233;
extern asn_TYPE_member_t asn_MBR_E1AP_Extended_GNB_CU_CP_Name_ExtIEs_233[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GNB_CU_UP_CellGroupRelatedConfiguration_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GNB_CU_UP_CellGroupRelatedConfiguration_Item_ExtIEs_specs_237;
extern asn_TYPE_member_t asn_MBR_E1AP_GNB_CU_UP_CellGroupRelatedConfiguration_Item_ExtIEs_237[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_Extended_GNB_CU_UP_Name_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_Extended_GNB_CU_UP_Name_ExtIEs_specs_241;
extern asn_TYPE_member_t asn_MBR_E1AP_Extended_GNB_CU_UP_Name_ExtIEs_241[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GNB_CU_CP_TNLA_Setup_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GNB_CU_CP_TNLA_Setup_Item_ExtIEs_specs_245;
extern asn_TYPE_member_t asn_MBR_E1AP_GNB_CU_CP_TNLA_Setup_Item_ExtIEs_245[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GNB_CU_CP_TNLA_Failed_To_Setup_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GNB_CU_CP_TNLA_Failed_To_Setup_Item_ExtIEs_specs_249;
extern asn_TYPE_member_t asn_MBR_E1AP_GNB_CU_CP_TNLA_Failed_To_Setup_Item_ExtIEs_249[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GNB_CU_CP_TNLA_To_Add_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GNB_CU_CP_TNLA_To_Add_Item_ExtIEs_specs_253;
extern asn_TYPE_member_t asn_MBR_E1AP_GNB_CU_CP_TNLA_To_Add_Item_ExtIEs_253[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GNB_CU_CP_TNLA_To_Remove_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GNB_CU_CP_TNLA_To_Remove_Item_ExtIEs_specs_257;
extern asn_TYPE_member_t asn_MBR_E1AP_GNB_CU_CP_TNLA_To_Remove_Item_ExtIEs_257[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GNB_CU_CP_TNLA_To_Update_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GNB_CU_CP_TNLA_To_Update_Item_ExtIEs_specs_261;
extern asn_TYPE_member_t asn_MBR_E1AP_GNB_CU_CP_TNLA_To_Update_Item_ExtIEs_261[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GNB_CU_UP_TNLA_To_Remove_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GNB_CU_UP_TNLA_To_Remove_Item_ExtIEs_specs_265;
extern asn_TYPE_member_t asn_MBR_E1AP_GNB_CU_UP_TNLA_To_Remove_Item_ExtIEs_265[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GBR_QosInformation_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GBR_QosInformation_ExtIEs_specs_269;
extern asn_TYPE_member_t asn_MBR_E1AP_GBR_QosInformation_ExtIEs_269[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GBR_QosFlowInformation_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GBR_QosFlowInformation_ExtIEs_specs_273;
extern asn_TYPE_member_t asn_MBR_E1AP_GBR_QosFlowInformation_ExtIEs_273[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GTPTLA_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GTPTLA_Item_ExtIEs_specs_277;
extern asn_TYPE_member_t asn_MBR_E1AP_GTPTLA_Item_ExtIEs_277[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GTPTunnel_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GTPTunnel_ExtIEs_specs_281;
extern asn_TYPE_member_t asn_MBR_E1AP_GTPTunnel_ExtIEs_281[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_HW_CapacityIndicator_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_HW_CapacityIndicator_ExtIEs_specs_285;
extern asn_TYPE_member_t asn_MBR_E1AP_HW_CapacityIndicator_ExtIEs_285[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ImmediateMDT_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_ImmediateMDT_ExtIEs_specs_289;
extern asn_TYPE_member_t asn_MBR_E1AP_ImmediateMDT_ExtIEs_289[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_MaximumIPdatarate_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_MaximumIPdatarate_ExtIEs_specs_293;
extern asn_TYPE_member_t asn_MBR_E1AP_MaximumIPdatarate_ExtIEs_293[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_MRDC_Data_Usage_Report_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_MRDC_Data_Usage_Report_Item_ExtIEs_specs_297;
extern asn_TYPE_member_t asn_MBR_E1AP_MRDC_Data_Usage_Report_Item_ExtIEs_297[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_MRDC_Usage_Information_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_MRDC_Usage_Information_ExtIEs_specs_301;
extern asn_TYPE_member_t asn_MBR_E1AP_MRDC_Usage_Information_ExtIEs_301[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_M4Configuration_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_M4Configuration_ExtIEs_specs_305;
extern asn_TYPE_member_t asn_MBR_E1AP_M4Configuration_ExtIEs_305[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_M6Configuration_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_M6Configuration_ExtIEs_specs_309;
extern asn_TYPE_member_t asn_MBR_E1AP_M6Configuration_ExtIEs_309[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_M7Configuration_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_M7Configuration_ExtIEs_specs_313;
extern asn_TYPE_member_t asn_MBR_E1AP_M7Configuration_ExtIEs_313[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_MDT_Configuration_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_MDT_Configuration_ExtIEs_specs_317;
extern asn_TYPE_member_t asn_MBR_E1AP_MDT_Configuration_ExtIEs_317[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_NGRANAllocationAndRetentionPriority_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_NGRANAllocationAndRetentionPriority_ExtIEs_specs_321;
extern asn_TYPE_member_t asn_MBR_E1AP_NGRANAllocationAndRetentionPriority_ExtIEs_321[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_NG_RAN_QoS_Support_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_NG_RAN_QoS_Support_Item_ExtIEs_specs_325;
extern asn_TYPE_member_t asn_MBR_E1AP_NG_RAN_QoS_Support_Item_ExtIEs_325[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_Non_Dynamic5QIDescriptor_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_Non_Dynamic5QIDescriptor_ExtIEs_specs_329;
extern asn_TYPE_member_t asn_MBR_E1AP_Non_Dynamic5QIDescriptor_ExtIEs_329[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_NPNSupportInfo_SNPN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_NPNSupportInfo_SNPN_ExtIEs_specs_333;
extern asn_TYPE_member_t asn_MBR_E1AP_NPNSupportInfo_SNPN_ExtIEs_333[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_NPNContextInfo_SNPN_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_NPNContextInfo_SNPN_ExtIEs_specs_337;
extern asn_TYPE_member_t asn_MBR_E1AP_NPNContextInfo_SNPN_ExtIEs_337[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_NR_CGI_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_NR_CGI_ExtIEs_specs_341;
extern asn_TYPE_member_t asn_MBR_E1AP_NR_CGI_ExtIEs_341[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_NR_CGI_Support_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_NR_CGI_Support_Item_ExtIEs_specs_345;
extern asn_TYPE_member_t asn_MBR_E1AP_NR_CGI_Support_Item_ExtIEs_345[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_Extended_NR_CGI_Support_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_Extended_NR_CGI_Support_Item_ExtIEs_specs_349;
extern asn_TYPE_member_t asn_MBR_E1AP_Extended_NR_CGI_Support_Item_ExtIEs_349[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_PacketErrorRate_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_PacketErrorRate_ExtIEs_specs_353;
extern asn_TYPE_member_t asn_MBR_E1AP_PacketErrorRate_ExtIEs_353[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_PDCP_Configuration_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_PDCP_Configuration_ExtIEs_specs_357;
extern asn_TYPE_member_t asn_MBR_E1AP_PDCP_Configuration_ExtIEs_357[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_PDCP_Count_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_PDCP_Count_ExtIEs_specs_361;
extern asn_TYPE_member_t asn_MBR_E1AP_PDCP_Count_ExtIEs_361[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_PDU_Session_Resource_Data_Usage_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_PDU_Session_Resource_Data_Usage_Item_ExtIEs_specs_365;
extern asn_TYPE_member_t asn_MBR_E1AP_PDU_Session_Resource_Data_Usage_Item_ExtIEs_365[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_PDCP_SN_Status_Information_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_PDCP_SN_Status_Information_ExtIEs_specs_369;
extern asn_TYPE_member_t asn_MBR_E1AP_PDCP_SN_Status_Information_ExtIEs_369[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DRBBStatusTransfer_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DRBBStatusTransfer_ExtIEs_specs_373;
extern asn_TYPE_member_t asn_MBR_E1AP_DRBBStatusTransfer_ExtIEs_373[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_PDU_Session_Resource_Activity_ItemExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_PDU_Session_Resource_Activity_ItemExtIEs_specs_377;
extern asn_TYPE_member_t asn_MBR_E1AP_PDU_Session_Resource_Activity_ItemExtIEs_377[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_PDU_Session_Resource_Confirm_Modified_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_PDU_Session_Resource_Confirm_Modified_Item_ExtIEs_specs_381;
extern asn_TYPE_member_t asn_MBR_E1AP_PDU_Session_Resource_Confirm_Modified_Item_ExtIEs_381[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_PDU_Session_Resource_Failed_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_PDU_Session_Resource_Failed_Item_ExtIEs_specs_385;
extern asn_TYPE_member_t asn_MBR_E1AP_PDU_Session_Resource_Failed_Item_ExtIEs_385[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_PDU_Session_Resource_Failed_Mod_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_PDU_Session_Resource_Failed_Mod_Item_ExtIEs_specs_389;
extern asn_TYPE_member_t asn_MBR_E1AP_PDU_Session_Resource_Failed_Mod_Item_ExtIEs_389[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_PDU_Session_Resource_Failed_To_Modify_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_PDU_Session_Resource_Failed_To_Modify_Item_ExtIEs_specs_393;
extern asn_TYPE_member_t asn_MBR_E1AP_PDU_Session_Resource_Failed_To_Modify_Item_ExtIEs_393[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_PDU_Session_Resource_Modified_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_PDU_Session_Resource_Modified_Item_ExtIEs_specs_397;
extern asn_TYPE_member_t asn_MBR_E1AP_PDU_Session_Resource_Modified_Item_ExtIEs_397[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_PDU_Session_Resource_Required_To_Modify_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_PDU_Session_Resource_Required_To_Modify_Item_ExtIEs_specs_401;
extern asn_TYPE_member_t asn_MBR_E1AP_PDU_Session_Resource_Required_To_Modify_Item_ExtIEs_401[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_PDU_Session_Resource_Setup_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_PDU_Session_Resource_Setup_Item_ExtIEs_specs_405;
extern asn_TYPE_member_t asn_MBR_E1AP_PDU_Session_Resource_Setup_Item_ExtIEs_405[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_PDU_Session_Resource_Setup_Mod_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_PDU_Session_Resource_Setup_Mod_Item_ExtIEs_specs_409;
extern asn_TYPE_member_t asn_MBR_E1AP_PDU_Session_Resource_Setup_Mod_Item_ExtIEs_409[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_PDU_Session_Resource_To_Modify_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_PDU_Session_Resource_To_Modify_Item_ExtIEs_specs_413;
extern asn_TYPE_member_t asn_MBR_E1AP_PDU_Session_Resource_To_Modify_Item_ExtIEs_413[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_PDU_Session_Resource_To_Remove_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_PDU_Session_Resource_To_Remove_Item_ExtIEs_specs_417;
extern asn_TYPE_member_t asn_MBR_E1AP_PDU_Session_Resource_To_Remove_Item_ExtIEs_417[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_PDU_Session_Resource_To_Setup_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_PDU_Session_Resource_To_Setup_Item_ExtIEs_specs_421;
extern asn_TYPE_member_t asn_MBR_E1AP_PDU_Session_Resource_To_Setup_Item_ExtIEs_421[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_PDU_Session_Resource_To_Setup_Mod_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_PDU_Session_Resource_To_Setup_Mod_Item_ExtIEs_specs_425;
extern asn_TYPE_member_t asn_MBR_E1AP_PDU_Session_Resource_To_Setup_Mod_Item_ExtIEs_425[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_PDU_Session_To_Notify_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_PDU_Session_To_Notify_Item_ExtIEs_specs_429;
extern asn_TYPE_member_t asn_MBR_E1AP_PDU_Session_To_Notify_Item_ExtIEs_429[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_QoS_Flow_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_QoS_Flow_Item_ExtIEs_specs_433;
extern asn_TYPE_member_t asn_MBR_E1AP_QoS_Flow_Item_ExtIEs_433[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_QoS_Flow_Failed_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_QoS_Flow_Failed_Item_ExtIEs_specs_437;
extern asn_TYPE_member_t asn_MBR_E1AP_QoS_Flow_Failed_Item_ExtIEs_437[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_QoS_Flow_Mapping_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_QoS_Flow_Mapping_Item_ExtIEs_specs_441;
extern asn_TYPE_member_t asn_MBR_E1AP_QoS_Flow_Mapping_Item_ExtIEs_441[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_QoS_Parameters_Support_List_ItemExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_QoS_Parameters_Support_List_ItemExtIEs_specs_445;
extern asn_TYPE_member_t asn_MBR_E1AP_QoS_Parameters_Support_List_ItemExtIEs_445[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_QoS_Flow_QoS_Parameter_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_QoS_Flow_QoS_Parameter_Item_ExtIEs_specs_449;
extern asn_TYPE_member_t asn_MBR_E1AP_QoS_Flow_QoS_Parameter_Item_ExtIEs_449[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_QoSFlowLevelQoSParameters_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_QoSFlowLevelQoSParameters_ExtIEs_specs_453;
extern asn_TYPE_member_t asn_MBR_E1AP_QoSFlowLevelQoSParameters_ExtIEs_453[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_QoS_Flow_Removed_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_QoS_Flow_Removed_Item_ExtIEs_specs_457;
extern asn_TYPE_member_t asn_MBR_E1AP_QoS_Flow_Removed_Item_ExtIEs_457[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_QoS_Flows_to_be_forwarded_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_QoS_Flows_to_be_forwarded_Item_ExtIEs_specs_461;
extern asn_TYPE_member_t asn_MBR_E1AP_QoS_Flows_to_be_forwarded_Item_ExtIEs_461[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DataForwardingtoNG_RANQoSFlowInformationList_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DataForwardingtoNG_RANQoSFlowInformationList_Item_ExtIEs_specs_465;
extern asn_TYPE_member_t asn_MBR_E1AP_DataForwardingtoNG_RANQoSFlowInformationList_Item_ExtIEs_465[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_RedundantPDUSessionInformation_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_RedundantPDUSessionInformation_ExtIEs_specs_469;
extern asn_TYPE_member_t asn_MBR_E1AP_RedundantPDUSessionInformation_ExtIEs_469[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ROHC_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_ROHC_ExtIEs_specs_473;
extern asn_TYPE_member_t asn_MBR_E1AP_ROHC_ExtIEs_473[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_SecurityAlgorithm_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_SecurityAlgorithm_ExtIEs_specs_477;
extern asn_TYPE_member_t asn_MBR_E1AP_SecurityAlgorithm_ExtIEs_477[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_SecurityIndication_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_SecurityIndication_ExtIEs_specs_481;
extern asn_TYPE_member_t asn_MBR_E1AP_SecurityIndication_ExtIEs_481[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_SecurityInformation_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_SecurityInformation_ExtIEs_specs_485;
extern asn_TYPE_member_t asn_MBR_E1AP_SecurityInformation_ExtIEs_485[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_SecurityResult_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_SecurityResult_ExtIEs_specs_489;
extern asn_TYPE_member_t asn_MBR_E1AP_SecurityResult_ExtIEs_489[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_Slice_Support_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_Slice_Support_Item_ExtIEs_specs_493;
extern asn_TYPE_member_t asn_MBR_E1AP_Slice_Support_Item_ExtIEs_493[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_SNSSAI_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_SNSSAI_ExtIEs_specs_497;
extern asn_TYPE_member_t asn_MBR_E1AP_SNSSAI_ExtIEs_497[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_SDAP_Configuration_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_SDAP_Configuration_ExtIEs_specs_501;
extern asn_TYPE_member_t asn_MBR_E1AP_SDAP_Configuration_ExtIEs_501[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_TNL_AvailableCapacityIndicator_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_TNL_AvailableCapacityIndicator_ExtIEs_specs_505;
extern asn_TYPE_member_t asn_MBR_E1AP_TNL_AvailableCapacityIndicator_ExtIEs_505[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_TSCTrafficCharacteristics_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_TSCTrafficCharacteristics_ExtIEs_specs_509;
extern asn_TYPE_member_t asn_MBR_E1AP_TSCTrafficCharacteristics_ExtIEs_509[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_TSCTrafficInformation_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_TSCTrafficInformation_ExtIEs_specs_513;
extern asn_TYPE_member_t asn_MBR_E1AP_TSCTrafficInformation_ExtIEs_513[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_TraceActivation_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_TraceActivation_ExtIEs_specs_517;
extern asn_TYPE_member_t asn_MBR_E1AP_TraceActivation_ExtIEs_517[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_T_ReorderingTimer_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_T_ReorderingTimer_ExtIEs_specs_521;
extern asn_TYPE_member_t asn_MBR_E1AP_T_ReorderingTimer_ExtIEs_521[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_Transport_Layer_Address_Info_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_Transport_Layer_Address_Info_ExtIEs_specs_525;
extern asn_TYPE_member_t asn_MBR_E1AP_Transport_Layer_Address_Info_ExtIEs_525[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_Transport_UP_Layer_Addresses_Info_To_Add_ItemExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_Transport_UP_Layer_Addresses_Info_To_Add_ItemExtIEs_specs_529;
extern asn_TYPE_member_t asn_MBR_E1AP_Transport_UP_Layer_Addresses_Info_To_Add_ItemExtIEs_529[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_Transport_UP_Layer_Addresses_Info_To_Remove_ItemExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_Transport_UP_Layer_Addresses_Info_To_Remove_ItemExtIEs_specs_533;
extern asn_TYPE_member_t asn_MBR_E1AP_Transport_UP_Layer_Addresses_Info_To_Remove_ItemExtIEs_533[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_UE_associatedLogicalE1_ConnectionItemExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_UE_associatedLogicalE1_ConnectionItemExtIEs_specs_537;
extern asn_TYPE_member_t asn_MBR_E1AP_UE_associatedLogicalE1_ConnectionItemExtIEs_537[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ULUPTNLAddressToUpdateItemExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_ULUPTNLAddressToUpdateItemExtIEs_specs_541;
extern asn_TYPE_member_t asn_MBR_E1AP_ULUPTNLAddressToUpdateItemExtIEs_541[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_UP_Parameters_Item_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_UP_Parameters_Item_ExtIEs_specs_545;
extern asn_TYPE_member_t asn_MBR_E1AP_UP_Parameters_Item_ExtIEs_545[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_UPSecuritykey_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_UPSecuritykey_ExtIEs_specs_549;
extern asn_TYPE_member_t asn_MBR_E1AP_UPSecuritykey_ExtIEs_549[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_UplinkOnlyROHC_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_UplinkOnlyROHC_ExtIEs_specs_553;
extern asn_TYPE_member_t asn_MBR_E1AP_UplinkOnlyROHC_ExtIEs_553[3];

#ifdef __cplusplus
}
#endif

#endif	/* _E1AP_ProtocolExtensionField_H_ */
#include <asn_internal.h>
