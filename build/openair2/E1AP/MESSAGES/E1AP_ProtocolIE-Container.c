/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-Containers"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#include "E1AP_ProtocolIE-Container.h"

#include "E1AP_ProtocolIE-Field.h"
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P0_constr_1 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P1_constr_3 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P2_constr_5 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P3_constr_7 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P4_constr_9 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P5_constr_11 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P6_constr_13 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P7_constr_15 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P8_constr_17 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P9_constr_19 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P10_constr_21 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P11_constr_23 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P12_constr_25 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P13_constr_27 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P14_constr_29 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P15_constr_31 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P16_constr_33 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P17_constr_35 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P18_constr_37 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P19_constr_39 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P20_constr_41 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P21_constr_43 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P22_constr_45 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P23_constr_47 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P24_constr_49 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P25_constr_51 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P26_constr_53 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P27_constr_55 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P28_constr_57 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P29_constr_59 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P30_constr_61 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P31_constr_63 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P32_constr_65 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P33_constr_67 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P34_constr_69 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P35_constr_71 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P36_constr_73 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P37_constr_75 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P38_constr_77 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P39_constr_79 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P40_constr_81 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P41_constr_83 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P42_constr_85 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P43_constr_87 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P44_constr_89 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P45_constr_91 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P46_constr_93 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P47_constr_95 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P48_constr_97 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P49_constr_99 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P50_constr_101 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P51_constr_103 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P52_constr_105 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P53_constr_107 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P54_constr_109 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P55_constr_111 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P56_constr_113 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P57_constr_115 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P58_constr_117 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P59_constr_119 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P60_constr_121 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 16,  16,  0,  65535 }	/* (SIZE(0..65535)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P0_1[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_ResetIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P0_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P0_specs_1 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P0),
	offsetof(struct E1AP_ProtocolIE_Container_4932P0, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P0 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P0_tags_1,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P0_tags_1)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P0_tags_1[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P0_tags_1,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P0_tags_1)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P0_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P0_constr_1,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P0_1,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P0_specs_1	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P1_3[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_ResetAcknowledgeIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P1_tags_3[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P1_specs_3 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P1),
	offsetof(struct E1AP_ProtocolIE_Container_4932P1, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P1 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P1_tags_3,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P1_tags_3)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P1_tags_3[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P1_tags_3,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P1_tags_3)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P1_tags_3[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P1_constr_3,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P1_3,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P1_specs_3	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P2_5[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_ErrorIndication_IEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P2_tags_5[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P2_specs_5 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P2),
	offsetof(struct E1AP_ProtocolIE_Container_4932P2, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P2 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P2_tags_5,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P2_tags_5)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P2_tags_5[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P2_tags_5,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P2_tags_5)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P2_tags_5[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P2_constr_5,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P2_5,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P2_specs_5	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P3_7[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_UP_E1SetupRequestIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P3_tags_7[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P3_specs_7 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P3),
	offsetof(struct E1AP_ProtocolIE_Container_4932P3, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P3 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P3_tags_7,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P3_tags_7)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P3_tags_7[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P3_tags_7,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P3_tags_7)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P3_tags_7[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P3_constr_7,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P3_7,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P3_specs_7	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P4_9[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_UP_E1SetupResponseIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P4_tags_9[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P4_specs_9 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P4),
	offsetof(struct E1AP_ProtocolIE_Container_4932P4, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P4 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P4_tags_9,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P4_tags_9)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P4_tags_9[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P4_tags_9,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P4_tags_9)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P4_tags_9[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P4_constr_9,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P4_9,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P4_specs_9	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P5_11[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_UP_E1SetupFailureIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P5_tags_11[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P5_specs_11 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P5),
	offsetof(struct E1AP_ProtocolIE_Container_4932P5, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P5 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P5_tags_11,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P5_tags_11)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P5_tags_11[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P5_tags_11,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P5_tags_11)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P5_tags_11[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P5_constr_11,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P5_11,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P5_specs_11	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P6_13[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_CP_E1SetupRequestIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P6_tags_13[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P6_specs_13 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P6),
	offsetof(struct E1AP_ProtocolIE_Container_4932P6, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P6 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P6_tags_13,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P6_tags_13)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P6_tags_13[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P6_tags_13,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P6_tags_13)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P6_tags_13[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P6_constr_13,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P6_13,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P6_specs_13	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P7_15[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_CP_E1SetupResponseIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P7_tags_15[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P7_specs_15 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P7),
	offsetof(struct E1AP_ProtocolIE_Container_4932P7, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P7 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P7_tags_15,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P7_tags_15)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P7_tags_15[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P7_tags_15,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P7_tags_15)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P7_tags_15[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P7_constr_15,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P7_15,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P7_specs_15	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P8_17[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_CP_E1SetupFailureIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P8_tags_17[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P8_specs_17 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P8),
	offsetof(struct E1AP_ProtocolIE_Container_4932P8, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P8 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P8_tags_17,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P8_tags_17)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P8_tags_17[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P8_tags_17,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P8_tags_17)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P8_tags_17[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P8_constr_17,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P8_17,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P8_specs_17	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P9_19[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_UP_ConfigurationUpdateIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P9_tags_19[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P9_specs_19 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P9),
	offsetof(struct E1AP_ProtocolIE_Container_4932P9, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P9 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P9_tags_19,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P9_tags_19)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P9_tags_19[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P9_tags_19,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P9_tags_19)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P9_tags_19[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P9_constr_19,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P9_19,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P9_specs_19	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P10_21[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_UP_ConfigurationUpdateAcknowledgeIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P10_tags_21[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P10_specs_21 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P10),
	offsetof(struct E1AP_ProtocolIE_Container_4932P10, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P10 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P10_tags_21,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P10_tags_21)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P10_tags_21[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P10_tags_21,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P10_tags_21)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P10_tags_21[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P10_constr_21,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P10_21,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P10_specs_21	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P11_23[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_UP_ConfigurationUpdateFailureIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P11_tags_23[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P11_specs_23 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P11),
	offsetof(struct E1AP_ProtocolIE_Container_4932P11, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P11 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P11_tags_23,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P11_tags_23)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P11_tags_23[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P11_tags_23,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P11_tags_23)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P11_tags_23[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P11_constr_23,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P11_23,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P11_specs_23	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P12_25[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_CP_ConfigurationUpdateIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P12_tags_25[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P12_specs_25 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P12),
	offsetof(struct E1AP_ProtocolIE_Container_4932P12, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P12 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P12_tags_25,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P12_tags_25)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P12_tags_25[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P12_tags_25,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P12_tags_25)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P12_tags_25[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P12_constr_25,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P12_25,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P12_specs_25	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P13_27[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_CP_ConfigurationUpdateAcknowledgeIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P13_tags_27[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P13_specs_27 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P13),
	offsetof(struct E1AP_ProtocolIE_Container_4932P13, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P13 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P13_tags_27,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P13_tags_27)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P13_tags_27[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P13_tags_27,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P13_tags_27)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P13_tags_27[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P13_constr_27,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P13_27,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P13_specs_27	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P14_29[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_CP_ConfigurationUpdateFailureIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P14_tags_29[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P14_specs_29 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P14),
	offsetof(struct E1AP_ProtocolIE_Container_4932P14, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P14 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P14_tags_29,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P14_tags_29)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P14_tags_29[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P14_tags_29,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P14_tags_29)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P14_tags_29[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P14_constr_29,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P14_29,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P14_specs_29	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P15_31[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_E1ReleaseRequestIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P15_tags_31[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P15_specs_31 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P15),
	offsetof(struct E1AP_ProtocolIE_Container_4932P15, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P15 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P15_tags_31,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P15_tags_31)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P15_tags_31[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P15_tags_31,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P15_tags_31)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P15_tags_31[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P15_constr_31,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P15_31,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P15_specs_31	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P16_33[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_E1ReleaseResponseIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P16_tags_33[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P16_specs_33 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P16),
	offsetof(struct E1AP_ProtocolIE_Container_4932P16, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P16 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P16_tags_33,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P16_tags_33)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P16_tags_33[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P16_tags_33,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P16_tags_33)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P16_tags_33[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P16_constr_33,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P16_33,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P16_specs_33	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P17_35[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_BearerContextSetupRequestIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P17_tags_35[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P17_specs_35 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P17),
	offsetof(struct E1AP_ProtocolIE_Container_4932P17, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P17 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P17_tags_35,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P17_tags_35)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P17_tags_35[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P17_tags_35,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P17_tags_35)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P17_tags_35[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P17_constr_35,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P17_35,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P17_specs_35	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P18_37[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_EUTRAN_BearerContextSetupRequest,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P18_tags_37[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P18_specs_37 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P18),
	offsetof(struct E1AP_ProtocolIE_Container_4932P18, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P18 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P18_tags_37,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P18_tags_37)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P18_tags_37[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P18_tags_37,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P18_tags_37)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P18_tags_37[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P18_constr_37,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P18_37,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P18_specs_37	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P19_39[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_NG_RAN_BearerContextSetupRequest,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P19_tags_39[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P19_specs_39 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P19),
	offsetof(struct E1AP_ProtocolIE_Container_4932P19, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P19 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P19_tags_39,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P19_tags_39)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P19_tags_39[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P19_tags_39,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P19_tags_39)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P19_tags_39[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P19_constr_39,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P19_39,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P19_specs_39	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P20_41[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_BearerContextSetupResponseIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P20_tags_41[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P20_specs_41 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P20),
	offsetof(struct E1AP_ProtocolIE_Container_4932P20, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P20 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P20_tags_41,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P20_tags_41)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P20_tags_41[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P20_tags_41,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P20_tags_41)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P20_tags_41[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P20_constr_41,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P20_41,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P20_specs_41	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P21_43[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_EUTRAN_BearerContextSetupResponse,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P21_tags_43[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P21_specs_43 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P21),
	offsetof(struct E1AP_ProtocolIE_Container_4932P21, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P21 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P21_tags_43,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P21_tags_43)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P21_tags_43[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P21_tags_43,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P21_tags_43)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P21_tags_43[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P21_constr_43,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P21_43,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P21_specs_43	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P22_45[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_NG_RAN_BearerContextSetupResponse,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P22_tags_45[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P22_specs_45 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P22),
	offsetof(struct E1AP_ProtocolIE_Container_4932P22, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P22 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P22_tags_45,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P22_tags_45)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P22_tags_45[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P22_tags_45,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P22_tags_45)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P22_tags_45[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P22_constr_45,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P22_45,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P22_specs_45	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P23_47[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_BearerContextSetupFailureIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P23_tags_47[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P23_specs_47 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P23),
	offsetof(struct E1AP_ProtocolIE_Container_4932P23, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P23 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P23_tags_47,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P23_tags_47)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P23_tags_47[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P23_tags_47,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P23_tags_47)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P23_tags_47[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P23_constr_47,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P23_47,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P23_specs_47	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P24_49[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_BearerContextModificationRequestIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P24_tags_49[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P24_specs_49 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P24),
	offsetof(struct E1AP_ProtocolIE_Container_4932P24, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P24 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P24_tags_49,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P24_tags_49)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P24_tags_49[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P24_tags_49,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P24_tags_49)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P24_tags_49[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P24_constr_49,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P24_49,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P24_specs_49	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P25_51[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_EUTRAN_BearerContextModificationRequest,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P25_tags_51[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P25_specs_51 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P25),
	offsetof(struct E1AP_ProtocolIE_Container_4932P25, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P25 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P25_tags_51,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P25_tags_51)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P25_tags_51[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P25_tags_51,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P25_tags_51)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P25_tags_51[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P25_constr_51,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P25_51,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P25_specs_51	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P26_53[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_NG_RAN_BearerContextModificationRequest,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P26_tags_53[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P26_specs_53 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P26),
	offsetof(struct E1AP_ProtocolIE_Container_4932P26, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P26 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P26_tags_53,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P26_tags_53)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P26_tags_53[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P26_tags_53,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P26_tags_53)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P26_tags_53[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P26_constr_53,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P26_53,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P26_specs_53	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P27_55[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_BearerContextModificationResponseIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P27_tags_55[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P27_specs_55 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P27),
	offsetof(struct E1AP_ProtocolIE_Container_4932P27, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P27 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P27_tags_55,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P27_tags_55)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P27_tags_55[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P27_tags_55,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P27_tags_55)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P27_tags_55[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P27_constr_55,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P27_55,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P27_specs_55	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P28_57[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_EUTRAN_BearerContextModificationResponse,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P28_tags_57[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P28_specs_57 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P28),
	offsetof(struct E1AP_ProtocolIE_Container_4932P28, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P28 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P28_tags_57,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P28_tags_57)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P28_tags_57[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P28_tags_57,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P28_tags_57)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P28_tags_57[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P28_constr_57,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P28_57,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P28_specs_57	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P29_59[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_NG_RAN_BearerContextModificationResponse,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P29_tags_59[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P29_specs_59 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P29),
	offsetof(struct E1AP_ProtocolIE_Container_4932P29, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P29 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P29_tags_59,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P29_tags_59)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P29_tags_59[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P29_tags_59,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P29_tags_59)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P29_tags_59[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P29_constr_59,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P29_59,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P29_specs_59	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P30_61[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_BearerContextModificationFailureIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P30_tags_61[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P30_specs_61 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P30),
	offsetof(struct E1AP_ProtocolIE_Container_4932P30, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P30 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P30_tags_61,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P30_tags_61)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P30_tags_61[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P30_tags_61,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P30_tags_61)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P30_tags_61[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P30_constr_61,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P30_61,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P30_specs_61	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P31_63[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_BearerContextModificationRequiredIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P31_tags_63[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P31_specs_63 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P31),
	offsetof(struct E1AP_ProtocolIE_Container_4932P31, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P31 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P31_tags_63,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P31_tags_63)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P31_tags_63[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P31_tags_63,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P31_tags_63)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P31_tags_63[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P31_constr_63,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P31_63,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P31_specs_63	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P32_65[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_EUTRAN_BearerContextModificationRequired,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P32_tags_65[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P32_specs_65 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P32),
	offsetof(struct E1AP_ProtocolIE_Container_4932P32, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P32 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P32_tags_65,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P32_tags_65)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P32_tags_65[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P32_tags_65,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P32_tags_65)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P32_tags_65[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P32_constr_65,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P32_65,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P32_specs_65	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P33_67[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_NG_RAN_BearerContextModificationRequired,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P33_tags_67[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P33_specs_67 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P33),
	offsetof(struct E1AP_ProtocolIE_Container_4932P33, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P33 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P33_tags_67,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P33_tags_67)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P33_tags_67[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P33_tags_67,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P33_tags_67)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P33_tags_67[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P33_constr_67,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P33_67,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P33_specs_67	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P34_69[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_BearerContextModificationConfirmIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P34_tags_69[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P34_specs_69 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P34),
	offsetof(struct E1AP_ProtocolIE_Container_4932P34, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P34 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P34_tags_69,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P34_tags_69)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P34_tags_69[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P34_tags_69,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P34_tags_69)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P34_tags_69[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P34_constr_69,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P34_69,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P34_specs_69	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P35_71[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_EUTRAN_BearerContextModificationConfirm,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P35_tags_71[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P35_specs_71 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P35),
	offsetof(struct E1AP_ProtocolIE_Container_4932P35, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P35 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P35_tags_71,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P35_tags_71)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P35_tags_71[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P35_tags_71,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P35_tags_71)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P35_tags_71[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P35_constr_71,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P35_71,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P35_specs_71	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P36_73[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_NG_RAN_BearerContextModificationConfirm,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P36_tags_73[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P36_specs_73 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P36),
	offsetof(struct E1AP_ProtocolIE_Container_4932P36, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P36 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P36_tags_73,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P36_tags_73)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P36_tags_73[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P36_tags_73,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P36_tags_73)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P36_tags_73[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P36_constr_73,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P36_73,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P36_specs_73	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P37_75[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_BearerContextReleaseCommandIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P37_tags_75[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P37_specs_75 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P37),
	offsetof(struct E1AP_ProtocolIE_Container_4932P37, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P37 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P37_tags_75,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P37_tags_75)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P37_tags_75[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P37_tags_75,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P37_tags_75)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P37_tags_75[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P37_constr_75,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P37_75,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P37_specs_75	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P38_77[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_BearerContextReleaseCompleteIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P38_tags_77[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P38_specs_77 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P38),
	offsetof(struct E1AP_ProtocolIE_Container_4932P38, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P38 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P38_tags_77,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P38_tags_77)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P38_tags_77[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P38_tags_77,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P38_tags_77)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P38_tags_77[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P38_constr_77,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P38_77,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P38_specs_77	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P39_79[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_BearerContextReleaseRequestIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P39_tags_79[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P39_specs_79 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P39),
	offsetof(struct E1AP_ProtocolIE_Container_4932P39, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P39 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P39_tags_79,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P39_tags_79)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P39_tags_79[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P39_tags_79,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P39_tags_79)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P39_tags_79[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P39_constr_79,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P39_79,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P39_specs_79	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P40_81[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_BearerContextInactivityNotificationIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P40_tags_81[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P40_specs_81 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P40),
	offsetof(struct E1AP_ProtocolIE_Container_4932P40, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P40 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P40_tags_81,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P40_tags_81)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P40_tags_81[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P40_tags_81,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P40_tags_81)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P40_tags_81[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P40_constr_81,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P40_81,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P40_specs_81	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P41_83[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DLDataNotificationIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P41_tags_83[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P41_specs_83 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P41),
	offsetof(struct E1AP_ProtocolIE_Container_4932P41, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P41 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P41_tags_83,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P41_tags_83)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P41_tags_83[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P41_tags_83,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P41_tags_83)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P41_tags_83[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P41_constr_83,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P41_83,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P41_specs_83	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P42_85[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_ULDataNotificationIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P42_tags_85[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P42_specs_85 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P42),
	offsetof(struct E1AP_ProtocolIE_Container_4932P42, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P42 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P42_tags_85,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P42_tags_85)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P42_tags_85[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P42_tags_85,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P42_tags_85)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P42_tags_85[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P42_constr_85,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P42_85,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P42_specs_85	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P43_87[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DataUsageReportIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P43_tags_87[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P43_specs_87 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P43),
	offsetof(struct E1AP_ProtocolIE_Container_4932P43, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P43 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P43_tags_87,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P43_tags_87)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P43_tags_87[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P43_tags_87,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P43_tags_87)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P43_tags_87[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P43_constr_87,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P43_87,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P43_specs_87	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P44_89[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_UP_CounterCheckRequestIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P44_tags_89[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P44_specs_89 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P44),
	offsetof(struct E1AP_ProtocolIE_Container_4932P44, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P44 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P44_tags_89,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P44_tags_89)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P44_tags_89[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P44_tags_89,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P44_tags_89)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P44_tags_89[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P44_constr_89,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P44_89,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P44_specs_89	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P45_91[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_EUTRAN_GNB_CU_UP_CounterCheckRequest,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P45_tags_91[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P45_specs_91 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P45),
	offsetof(struct E1AP_ProtocolIE_Container_4932P45, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P45 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P45_tags_91,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P45_tags_91)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P45_tags_91[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P45_tags_91,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P45_tags_91)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P45_tags_91[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P45_constr_91,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P45_91,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P45_specs_91	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P46_93[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_NG_RAN_GNB_CU_UP_CounterCheckRequest,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P46_tags_93[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P46_specs_93 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P46),
	offsetof(struct E1AP_ProtocolIE_Container_4932P46, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P46 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P46_tags_93,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P46_tags_93)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P46_tags_93[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P46_tags_93,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P46_tags_93)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P46_tags_93[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P46_constr_93,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P46_93,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P46_specs_93	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P47_95[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_UP_StatusIndicationIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P47_tags_95[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P47_specs_95 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P47),
	offsetof(struct E1AP_ProtocolIE_Container_4932P47, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P47 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P47_tags_95,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P47_tags_95)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P47_tags_95[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P47_tags_95,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P47_tags_95)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P47_tags_95[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P47_constr_95,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P47_95,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P47_specs_95	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P48_97[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_CPMeasurementResultsInformationIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P48_tags_97[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P48_specs_97 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P48),
	offsetof(struct E1AP_ProtocolIE_Container_4932P48, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P48 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P48_tags_97,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P48_tags_97)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P48_tags_97[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P48_tags_97,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P48_tags_97)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P48_tags_97[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P48_constr_97,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P48_97,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P48_specs_97	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P49_99[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_MRDC_DataUsageReportIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P49_tags_99[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P49_specs_99 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P49),
	offsetof(struct E1AP_ProtocolIE_Container_4932P49, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P49 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P49_tags_99,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P49_tags_99)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P49_tags_99[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P49_tags_99,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P49_tags_99)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P49_tags_99[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P49_constr_99,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P49_99,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P49_specs_99	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P50_101[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_TraceStartIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P50_tags_101[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P50_specs_101 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P50),
	offsetof(struct E1AP_ProtocolIE_Container_4932P50, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P50 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P50_tags_101,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P50_tags_101)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P50_tags_101[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P50_tags_101,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P50_tags_101)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P50_tags_101[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P50_constr_101,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P50_101,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P50_specs_101	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P51_103[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_DeactivateTraceIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P51_tags_103[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P51_specs_103 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P51),
	offsetof(struct E1AP_ProtocolIE_Container_4932P51, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P51 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P51_tags_103,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P51_tags_103)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P51_tags_103[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P51_tags_103,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P51_tags_103)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P51_tags_103[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P51_constr_103,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P51_103,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P51_specs_103	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P52_105[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_CellTrafficTraceIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P52_tags_105[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P52_specs_105 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P52),
	offsetof(struct E1AP_ProtocolIE_Container_4932P52, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P52 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P52_tags_105,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P52_tags_105)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P52_tags_105[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P52_tags_105,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P52_tags_105)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P52_tags_105[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P52_constr_105,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P52_105,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P52_specs_105	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P53_107[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_ResourceStatusRequestIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P53_tags_107[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P53_specs_107 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P53),
	offsetof(struct E1AP_ProtocolIE_Container_4932P53, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P53 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P53_tags_107,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P53_tags_107)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P53_tags_107[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P53_tags_107,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P53_tags_107)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P53_tags_107[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P53_constr_107,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P53_107,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P53_specs_107	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P54_109[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_ResourceStatusResponseIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P54_tags_109[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P54_specs_109 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P54),
	offsetof(struct E1AP_ProtocolIE_Container_4932P54, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P54 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P54_tags_109,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P54_tags_109)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P54_tags_109[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P54_tags_109,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P54_tags_109)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P54_tags_109[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P54_constr_109,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P54_109,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P54_specs_109	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P55_111[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_ResourceStatusFailureIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P55_tags_111[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P55_specs_111 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P55),
	offsetof(struct E1AP_ProtocolIE_Container_4932P55, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P55 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P55_tags_111,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P55_tags_111)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P55_tags_111[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P55_tags_111,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P55_tags_111)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P55_tags_111[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P55_constr_111,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P55_111,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P55_specs_111	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P56_113[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_ResourceStatusUpdateIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P56_tags_113[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P56_specs_113 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P56),
	offsetof(struct E1AP_ProtocolIE_Container_4932P56, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P56 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P56_tags_113,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P56_tags_113)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P56_tags_113[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P56_tags_113,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P56_tags_113)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P56_tags_113[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P56_constr_113,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P56_113,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P56_specs_113	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P57_115[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_IAB_UPTNLAddressUpdateIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P57_tags_115[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P57_specs_115 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P57),
	offsetof(struct E1AP_ProtocolIE_Container_4932P57, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P57 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P57_tags_115,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P57_tags_115)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P57_tags_115[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P57_tags_115,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P57_tags_115)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P57_tags_115[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P57_constr_115,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P57_115,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P57_specs_115	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P58_117[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_IAB_UPTNLAddressUpdateAcknowledgeIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P58_tags_117[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P58_specs_117 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P58),
	offsetof(struct E1AP_ProtocolIE_Container_4932P58, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P58 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P58_tags_117,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P58_tags_117)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P58_tags_117[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P58_tags_117,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P58_tags_117)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P58_tags_117[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P58_constr_117,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P58_117,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P58_specs_117	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P59_119[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_IAB_UPTNLAddressUpdateFailureIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P59_tags_119[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P59_specs_119 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P59),
	offsetof(struct E1AP_ProtocolIE_Container_4932P59, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P59 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P59_tags_119,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P59_tags_119)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P59_tags_119[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P59_tags_119,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P59_tags_119)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P59_tags_119[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P59_constr_119,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P59_119,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P59_specs_119	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P60_121[] = {
	{ ATF_POINTER, 0, 0,
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_EarlyForwardingSNTransferIEs,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		""
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_Container_4932P60_tags_121[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P60_specs_121 = {
	sizeof(struct E1AP_ProtocolIE_Container_4932P60),
	offsetof(struct E1AP_ProtocolIE_Container_4932P60, _asn_ctx),
	0,	/* XER encoding is XMLDelimitedItemList */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P60 = {
	"ProtocolIE-Container",
	"ProtocolIE-Container",
	&asn_OP_SEQUENCE_OF,
	asn_DEF_E1AP_ProtocolIE_Container_4932P60_tags_121,
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P60_tags_121)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P60_tags_121[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_Container_4932P60_tags_121,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P60_tags_121)
		/sizeof(asn_DEF_E1AP_ProtocolIE_Container_4932P60_tags_121[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_ProtocolIE_Container_4932P60_constr_121,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_OF_constraint
	},
	asn_MBR_E1AP_ProtocolIE_Container_4932P60_121,
	1,	/* Single element */
	&asn_SPC_E1AP_ProtocolIE_Container_4932P60_specs_121	/* Additional specs */
};

