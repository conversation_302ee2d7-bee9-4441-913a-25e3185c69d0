/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-Containers"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#ifndef	_E1AP_ProtocolIE_Container_H_
#define	_E1AP_ProtocolIE_Container_H_


#include <asn_application.h>

/* Including external dependencies */
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct E1AP_ResetIEs;
struct E1AP_ResetAcknowledgeIEs;
struct E1AP_ErrorIndication_IEs;
struct E1AP_GNB_CU_UP_E1SetupRequestIEs;
struct E1AP_GNB_CU_UP_E1SetupResponseIEs;
struct E1AP_GNB_CU_UP_E1SetupFailureIEs;
struct E1AP_GNB_CU_CP_E1SetupRequestIEs;
struct E1AP_GNB_CU_CP_E1SetupResponseIEs;
struct E1AP_GNB_CU_CP_E1SetupFailureIEs;
struct E1AP_GNB_CU_UP_ConfigurationUpdateIEs;
struct E1AP_GNB_CU_UP_ConfigurationUpdateAcknowledgeIEs;
struct E1AP_GNB_CU_UP_ConfigurationUpdateFailureIEs;
struct E1AP_GNB_CU_CP_ConfigurationUpdateIEs;
struct E1AP_GNB_CU_CP_ConfigurationUpdateAcknowledgeIEs;
struct E1AP_GNB_CU_CP_ConfigurationUpdateFailureIEs;
struct E1AP_E1ReleaseRequestIEs;
struct E1AP_E1ReleaseResponseIEs;
struct E1AP_BearerContextSetupRequestIEs;
struct E1AP_EUTRAN_BearerContextSetupRequest;
struct E1AP_NG_RAN_BearerContextSetupRequest;
struct E1AP_BearerContextSetupResponseIEs;
struct E1AP_EUTRAN_BearerContextSetupResponse;
struct E1AP_NG_RAN_BearerContextSetupResponse;
struct E1AP_BearerContextSetupFailureIEs;
struct E1AP_BearerContextModificationRequestIEs;
struct E1AP_EUTRAN_BearerContextModificationRequest;
struct E1AP_NG_RAN_BearerContextModificationRequest;
struct E1AP_BearerContextModificationResponseIEs;
struct E1AP_EUTRAN_BearerContextModificationResponse;
struct E1AP_NG_RAN_BearerContextModificationResponse;
struct E1AP_BearerContextModificationFailureIEs;
struct E1AP_BearerContextModificationRequiredIEs;
struct E1AP_EUTRAN_BearerContextModificationRequired;
struct E1AP_NG_RAN_BearerContextModificationRequired;
struct E1AP_BearerContextModificationConfirmIEs;
struct E1AP_EUTRAN_BearerContextModificationConfirm;
struct E1AP_NG_RAN_BearerContextModificationConfirm;
struct E1AP_BearerContextReleaseCommandIEs;
struct E1AP_BearerContextReleaseCompleteIEs;
struct E1AP_BearerContextReleaseRequestIEs;
struct E1AP_BearerContextInactivityNotificationIEs;
struct E1AP_DLDataNotificationIEs;
struct E1AP_ULDataNotificationIEs;
struct E1AP_DataUsageReportIEs;
struct E1AP_GNB_CU_UP_CounterCheckRequestIEs;
struct E1AP_EUTRAN_GNB_CU_UP_CounterCheckRequest;
struct E1AP_NG_RAN_GNB_CU_UP_CounterCheckRequest;
struct E1AP_GNB_CU_UP_StatusIndicationIEs;
struct E1AP_GNB_CU_CPMeasurementResultsInformationIEs;
struct E1AP_MRDC_DataUsageReportIEs;
struct E1AP_TraceStartIEs;
struct E1AP_DeactivateTraceIEs;
struct E1AP_CellTrafficTraceIEs;
struct E1AP_ResourceStatusRequestIEs;
struct E1AP_ResourceStatusResponseIEs;
struct E1AP_ResourceStatusFailureIEs;
struct E1AP_ResourceStatusUpdateIEs;
struct E1AP_IAB_UPTNLAddressUpdateIEs;
struct E1AP_IAB_UPTNLAddressUpdateAcknowledgeIEs;
struct E1AP_IAB_UPTNLAddressUpdateFailureIEs;
struct E1AP_EarlyForwardingSNTransferIEs;

/* E1AP_ProtocolIE-Container */
typedef struct E1AP_ProtocolIE_Container_4932P0 {
	A_SEQUENCE_OF(struct E1AP_ResetIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P0_t;
typedef struct E1AP_ProtocolIE_Container_4932P1 {
	A_SEQUENCE_OF(struct E1AP_ResetAcknowledgeIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P1_t;
typedef struct E1AP_ProtocolIE_Container_4932P2 {
	A_SEQUENCE_OF(struct E1AP_ErrorIndication_IEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P2_t;
typedef struct E1AP_ProtocolIE_Container_4932P3 {
	A_SEQUENCE_OF(struct E1AP_GNB_CU_UP_E1SetupRequestIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P3_t;
typedef struct E1AP_ProtocolIE_Container_4932P4 {
	A_SEQUENCE_OF(struct E1AP_GNB_CU_UP_E1SetupResponseIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P4_t;
typedef struct E1AP_ProtocolIE_Container_4932P5 {
	A_SEQUENCE_OF(struct E1AP_GNB_CU_UP_E1SetupFailureIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P5_t;
typedef struct E1AP_ProtocolIE_Container_4932P6 {
	A_SEQUENCE_OF(struct E1AP_GNB_CU_CP_E1SetupRequestIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P6_t;
typedef struct E1AP_ProtocolIE_Container_4932P7 {
	A_SEQUENCE_OF(struct E1AP_GNB_CU_CP_E1SetupResponseIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P7_t;
typedef struct E1AP_ProtocolIE_Container_4932P8 {
	A_SEQUENCE_OF(struct E1AP_GNB_CU_CP_E1SetupFailureIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P8_t;
typedef struct E1AP_ProtocolIE_Container_4932P9 {
	A_SEQUENCE_OF(struct E1AP_GNB_CU_UP_ConfigurationUpdateIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P9_t;
typedef struct E1AP_ProtocolIE_Container_4932P10 {
	A_SEQUENCE_OF(struct E1AP_GNB_CU_UP_ConfigurationUpdateAcknowledgeIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P10_t;
typedef struct E1AP_ProtocolIE_Container_4932P11 {
	A_SEQUENCE_OF(struct E1AP_GNB_CU_UP_ConfigurationUpdateFailureIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P11_t;
typedef struct E1AP_ProtocolIE_Container_4932P12 {
	A_SEQUENCE_OF(struct E1AP_GNB_CU_CP_ConfigurationUpdateIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P12_t;
typedef struct E1AP_ProtocolIE_Container_4932P13 {
	A_SEQUENCE_OF(struct E1AP_GNB_CU_CP_ConfigurationUpdateAcknowledgeIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P13_t;
typedef struct E1AP_ProtocolIE_Container_4932P14 {
	A_SEQUENCE_OF(struct E1AP_GNB_CU_CP_ConfigurationUpdateFailureIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P14_t;
typedef struct E1AP_ProtocolIE_Container_4932P15 {
	A_SEQUENCE_OF(struct E1AP_E1ReleaseRequestIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P15_t;
typedef struct E1AP_ProtocolIE_Container_4932P16 {
	A_SEQUENCE_OF(struct E1AP_E1ReleaseResponseIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P16_t;
typedef struct E1AP_ProtocolIE_Container_4932P17 {
	A_SEQUENCE_OF(struct E1AP_BearerContextSetupRequestIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P17_t;
typedef struct E1AP_ProtocolIE_Container_4932P18 {
	A_SEQUENCE_OF(struct E1AP_EUTRAN_BearerContextSetupRequest) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P18_t;
typedef struct E1AP_ProtocolIE_Container_4932P19 {
	A_SEQUENCE_OF(struct E1AP_NG_RAN_BearerContextSetupRequest) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P19_t;
typedef struct E1AP_ProtocolIE_Container_4932P20 {
	A_SEQUENCE_OF(struct E1AP_BearerContextSetupResponseIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P20_t;
typedef struct E1AP_ProtocolIE_Container_4932P21 {
	A_SEQUENCE_OF(struct E1AP_EUTRAN_BearerContextSetupResponse) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P21_t;
typedef struct E1AP_ProtocolIE_Container_4932P22 {
	A_SEQUENCE_OF(struct E1AP_NG_RAN_BearerContextSetupResponse) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P22_t;
typedef struct E1AP_ProtocolIE_Container_4932P23 {
	A_SEQUENCE_OF(struct E1AP_BearerContextSetupFailureIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P23_t;
typedef struct E1AP_ProtocolIE_Container_4932P24 {
	A_SEQUENCE_OF(struct E1AP_BearerContextModificationRequestIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P24_t;
typedef struct E1AP_ProtocolIE_Container_4932P25 {
	A_SEQUENCE_OF(struct E1AP_EUTRAN_BearerContextModificationRequest) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P25_t;
typedef struct E1AP_ProtocolIE_Container_4932P26 {
	A_SEQUENCE_OF(struct E1AP_NG_RAN_BearerContextModificationRequest) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P26_t;
typedef struct E1AP_ProtocolIE_Container_4932P27 {
	A_SEQUENCE_OF(struct E1AP_BearerContextModificationResponseIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P27_t;
typedef struct E1AP_ProtocolIE_Container_4932P28 {
	A_SEQUENCE_OF(struct E1AP_EUTRAN_BearerContextModificationResponse) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P28_t;
typedef struct E1AP_ProtocolIE_Container_4932P29 {
	A_SEQUENCE_OF(struct E1AP_NG_RAN_BearerContextModificationResponse) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P29_t;
typedef struct E1AP_ProtocolIE_Container_4932P30 {
	A_SEQUENCE_OF(struct E1AP_BearerContextModificationFailureIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P30_t;
typedef struct E1AP_ProtocolIE_Container_4932P31 {
	A_SEQUENCE_OF(struct E1AP_BearerContextModificationRequiredIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P31_t;
typedef struct E1AP_ProtocolIE_Container_4932P32 {
	A_SEQUENCE_OF(struct E1AP_EUTRAN_BearerContextModificationRequired) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P32_t;
typedef struct E1AP_ProtocolIE_Container_4932P33 {
	A_SEQUENCE_OF(struct E1AP_NG_RAN_BearerContextModificationRequired) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P33_t;
typedef struct E1AP_ProtocolIE_Container_4932P34 {
	A_SEQUENCE_OF(struct E1AP_BearerContextModificationConfirmIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P34_t;
typedef struct E1AP_ProtocolIE_Container_4932P35 {
	A_SEQUENCE_OF(struct E1AP_EUTRAN_BearerContextModificationConfirm) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P35_t;
typedef struct E1AP_ProtocolIE_Container_4932P36 {
	A_SEQUENCE_OF(struct E1AP_NG_RAN_BearerContextModificationConfirm) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P36_t;
typedef struct E1AP_ProtocolIE_Container_4932P37 {
	A_SEQUENCE_OF(struct E1AP_BearerContextReleaseCommandIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P37_t;
typedef struct E1AP_ProtocolIE_Container_4932P38 {
	A_SEQUENCE_OF(struct E1AP_BearerContextReleaseCompleteIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P38_t;
typedef struct E1AP_ProtocolIE_Container_4932P39 {
	A_SEQUENCE_OF(struct E1AP_BearerContextReleaseRequestIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P39_t;
typedef struct E1AP_ProtocolIE_Container_4932P40 {
	A_SEQUENCE_OF(struct E1AP_BearerContextInactivityNotificationIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P40_t;
typedef struct E1AP_ProtocolIE_Container_4932P41 {
	A_SEQUENCE_OF(struct E1AP_DLDataNotificationIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P41_t;
typedef struct E1AP_ProtocolIE_Container_4932P42 {
	A_SEQUENCE_OF(struct E1AP_ULDataNotificationIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P42_t;
typedef struct E1AP_ProtocolIE_Container_4932P43 {
	A_SEQUENCE_OF(struct E1AP_DataUsageReportIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P43_t;
typedef struct E1AP_ProtocolIE_Container_4932P44 {
	A_SEQUENCE_OF(struct E1AP_GNB_CU_UP_CounterCheckRequestIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P44_t;
typedef struct E1AP_ProtocolIE_Container_4932P45 {
	A_SEQUENCE_OF(struct E1AP_EUTRAN_GNB_CU_UP_CounterCheckRequest) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P45_t;
typedef struct E1AP_ProtocolIE_Container_4932P46 {
	A_SEQUENCE_OF(struct E1AP_NG_RAN_GNB_CU_UP_CounterCheckRequest) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P46_t;
typedef struct E1AP_ProtocolIE_Container_4932P47 {
	A_SEQUENCE_OF(struct E1AP_GNB_CU_UP_StatusIndicationIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P47_t;
typedef struct E1AP_ProtocolIE_Container_4932P48 {
	A_SEQUENCE_OF(struct E1AP_GNB_CU_CPMeasurementResultsInformationIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P48_t;
typedef struct E1AP_ProtocolIE_Container_4932P49 {
	A_SEQUENCE_OF(struct E1AP_MRDC_DataUsageReportIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P49_t;
typedef struct E1AP_ProtocolIE_Container_4932P50 {
	A_SEQUENCE_OF(struct E1AP_TraceStartIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P50_t;
typedef struct E1AP_ProtocolIE_Container_4932P51 {
	A_SEQUENCE_OF(struct E1AP_DeactivateTraceIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P51_t;
typedef struct E1AP_ProtocolIE_Container_4932P52 {
	A_SEQUENCE_OF(struct E1AP_CellTrafficTraceIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P52_t;
typedef struct E1AP_ProtocolIE_Container_4932P53 {
	A_SEQUENCE_OF(struct E1AP_ResourceStatusRequestIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P53_t;
typedef struct E1AP_ProtocolIE_Container_4932P54 {
	A_SEQUENCE_OF(struct E1AP_ResourceStatusResponseIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P54_t;
typedef struct E1AP_ProtocolIE_Container_4932P55 {
	A_SEQUENCE_OF(struct E1AP_ResourceStatusFailureIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P55_t;
typedef struct E1AP_ProtocolIE_Container_4932P56 {
	A_SEQUENCE_OF(struct E1AP_ResourceStatusUpdateIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P56_t;
typedef struct E1AP_ProtocolIE_Container_4932P57 {
	A_SEQUENCE_OF(struct E1AP_IAB_UPTNLAddressUpdateIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P57_t;
typedef struct E1AP_ProtocolIE_Container_4932P58 {
	A_SEQUENCE_OF(struct E1AP_IAB_UPTNLAddressUpdateAcknowledgeIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P58_t;
typedef struct E1AP_ProtocolIE_Container_4932P59 {
	A_SEQUENCE_OF(struct E1AP_IAB_UPTNLAddressUpdateFailureIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P59_t;
typedef struct E1AP_ProtocolIE_Container_4932P60 {
	A_SEQUENCE_OF(struct E1AP_EarlyForwardingSNTransferIEs) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ProtocolIE_Container_4932P60_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P0;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P0_specs_1;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P0_1[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P0_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P1;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P1_specs_3;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P1_3[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P1_constr_3;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P2;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P2_specs_5;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P2_5[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P2_constr_5;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P3;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P3_specs_7;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P3_7[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P3_constr_7;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P4;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P4_specs_9;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P4_9[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P4_constr_9;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P5;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P5_specs_11;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P5_11[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P5_constr_11;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P6;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P6_specs_13;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P6_13[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P6_constr_13;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P7;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P7_specs_15;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P7_15[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P7_constr_15;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P8;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P8_specs_17;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P8_17[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P8_constr_17;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P9;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P9_specs_19;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P9_19[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P9_constr_19;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P10;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P10_specs_21;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P10_21[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P10_constr_21;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P11;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P11_specs_23;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P11_23[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P11_constr_23;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P12;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P12_specs_25;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P12_25[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P12_constr_25;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P13;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P13_specs_27;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P13_27[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P13_constr_27;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P14;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P14_specs_29;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P14_29[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P14_constr_29;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P15;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P15_specs_31;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P15_31[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P15_constr_31;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P16;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P16_specs_33;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P16_33[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P16_constr_33;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P17;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P17_specs_35;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P17_35[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P17_constr_35;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P18;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P18_specs_37;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P18_37[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P18_constr_37;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P19;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P19_specs_39;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P19_39[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P19_constr_39;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P20;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P20_specs_41;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P20_41[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P20_constr_41;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P21;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P21_specs_43;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P21_43[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P21_constr_43;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P22;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P22_specs_45;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P22_45[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P22_constr_45;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P23;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P23_specs_47;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P23_47[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P23_constr_47;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P24;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P24_specs_49;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P24_49[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P24_constr_49;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P25;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P25_specs_51;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P25_51[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P25_constr_51;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P26;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P26_specs_53;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P26_53[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P26_constr_53;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P27;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P27_specs_55;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P27_55[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P27_constr_55;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P28;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P28_specs_57;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P28_57[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P28_constr_57;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P29;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P29_specs_59;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P29_59[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P29_constr_59;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P30;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P30_specs_61;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P30_61[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P30_constr_61;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P31;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P31_specs_63;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P31_63[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P31_constr_63;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P32;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P32_specs_65;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P32_65[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P32_constr_65;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P33;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P33_specs_67;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P33_67[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P33_constr_67;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P34;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P34_specs_69;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P34_69[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P34_constr_69;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P35;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P35_specs_71;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P35_71[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P35_constr_71;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P36;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P36_specs_73;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P36_73[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P36_constr_73;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P37;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P37_specs_75;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P37_75[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P37_constr_75;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P38;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P38_specs_77;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P38_77[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P38_constr_77;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P39;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P39_specs_79;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P39_79[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P39_constr_79;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P40;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P40_specs_81;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P40_81[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P40_constr_81;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P41;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P41_specs_83;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P41_83[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P41_constr_83;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P42;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P42_specs_85;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P42_85[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P42_constr_85;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P43;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P43_specs_87;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P43_87[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P43_constr_87;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P44;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P44_specs_89;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P44_89[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P44_constr_89;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P45;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P45_specs_91;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P45_91[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P45_constr_91;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P46;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P46_specs_93;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P46_93[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P46_constr_93;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P47;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P47_specs_95;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P47_95[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P47_constr_95;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P48;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P48_specs_97;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P48_97[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P48_constr_97;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P49;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P49_specs_99;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P49_99[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P49_constr_99;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P50;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P50_specs_101;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P50_101[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P50_constr_101;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P51;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P51_specs_103;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P51_103[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P51_constr_103;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P52;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P52_specs_105;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P52_105[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P52_constr_105;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P53;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P53_specs_107;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P53_107[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P53_constr_107;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P54;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P54_specs_109;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P54_109[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P54_constr_109;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P55;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P55_specs_111;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P55_111[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P55_constr_111;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P56;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P56_specs_113;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P56_113[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P56_constr_113;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P57;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P57_specs_115;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P57_115[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P57_constr_115;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P58;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P58_specs_117;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P58_117[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P58_constr_117;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P59;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P59_specs_119;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P59_119[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P59_constr_119;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_Container_4932P60;
extern asn_SET_OF_specifics_t asn_SPC_E1AP_ProtocolIE_Container_4932P60_specs_121;
extern asn_TYPE_member_t asn_MBR_E1AP_ProtocolIE_Container_4932P60_121[1];
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_Container_4932P60_constr_121;

#ifdef __cplusplus
}
#endif

#endif	/* _E1AP_ProtocolIE_Container_H_ */
#include <asn_internal.h>
