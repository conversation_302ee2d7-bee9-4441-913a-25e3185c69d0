/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-Containers"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#ifndef	_E1AP_ProtocolIE_Field_H_
#define	_E1AP_ProtocolIE_Field_H_


#include <asn_application.h>

/* Including external dependencies */
#include "E1AP_ProtocolIE-ID.h"
#include "E1AP_Criticality.h"
#include <ANY.h>
#include <asn_ioc.h>
#include <OPEN_TYPE.h>
#include <constr_CHOICE.h>
#include <constr_SEQUENCE.h>
#include "E1AP_UE-associatedLogicalE1-ConnectionItem.h"
#include "E1AP_Presence.h"
#include "E1AP_Endpoint-IP-address-and-port.h"
#include "E1AP_TransactionID.h"
#include "E1AP_Cause.h"
#include "E1AP_ResetType.h"
#include "E1AP_UE-associatedLogicalE1-ConnectionListResAck.h"
#include "E1AP_CriticalityDiagnostics.h"
#include "E1AP_GNB-CU-CP-UE-E1AP-ID.h"
#include "E1AP_GNB-CU-UP-UE-E1AP-ID.h"
#include "E1AP_GNB-CU-UP-ID.h"
#include "E1AP_GNB-CU-UP-Name.h"
#include "E1AP_CNSupport.h"
#include "E1AP_SupportedPLMNs-List.h"
#include "E1AP_GNB-CU-UP-Capacity.h"
#include "E1AP_Transport-Layer-Address-Info.h"
#include "E1AP_Extended-GNB-CU-UP-Name.h"
#include "E1AP_GNB-CU-CP-Name.h"
#include "E1AP_Extended-GNB-CU-CP-Name.h"
#include "E1AP_TimeToWait.h"
#include "E1AP_GNB-CU-UP-TNLA-To-Remove-List.h"
#include "E1AP_GNB-CU-CP-TNLA-To-Add-List.h"
#include "E1AP_GNB-CU-CP-TNLA-To-Remove-List.h"
#include "E1AP_GNB-CU-CP-TNLA-To-Update-List.h"
#include "E1AP_GNB-CU-CP-TNLA-Setup-List.h"
#include "E1AP_GNB-CU-CP-TNLA-Failed-To-Setup-List.h"
#include "E1AP_SecurityInformation.h"
#include "E1AP_BitRate.h"
#include "E1AP_PLMN-Identity.h"
#include "E1AP_ActivityNotificationLevel.h"
#include "E1AP_Inactivity-Timer.h"
#include "E1AP_BearerContextStatusChange.h"
#include "E1AP_System-BearerContextSetupRequest.h"
#include "E1AP_RANUEID.h"
#include "E1AP_GNB-DU-ID.h"
#include "E1AP_TraceActivation.h"
#include "E1AP_NPNContextInfo.h"
#include "E1AP_MDTPLMNList.h"
#include "E1AP_CHOInitiation.h"
#include "E1AP_AdditionalHandoverInfo.h"
#include "E1AP_DirectForwardingPathAvailability.h"
#include "E1AP_DRB-To-Setup-List-EUTRAN.h"
#include "E1AP_SubscriberProfileIDforRFP.h"
#include "E1AP_AdditionalRRMPriorityIndex.h"
#include "E1AP_PDU-Session-Resource-To-Setup-List.h"
#include "E1AP_System-BearerContextSetupResponse.h"
#include "E1AP_DRB-Setup-List-EUTRAN.h"
#include "E1AP_DRB-Failed-List-EUTRAN.h"
#include "E1AP_PDU-Session-Resource-Setup-List.h"
#include "E1AP_PDU-Session-Resource-Failed-List.h"
#include "E1AP_New-UL-TNL-Information-Required.h"
#include "E1AP_DataDiscardRequired.h"
#include "E1AP_System-BearerContextModificationRequest.h"
#include "E1AP_DRB-To-Setup-Mod-List-EUTRAN.h"
#include "E1AP_DRB-To-Modify-List-EUTRAN.h"
#include "E1AP_DRB-To-Remove-List-EUTRAN.h"
#include "E1AP_PDU-Session-Resource-To-Setup-Mod-List.h"
#include "E1AP_PDU-Session-Resource-To-Modify-List.h"
#include "E1AP_PDU-Session-Resource-To-Remove-List.h"
#include "E1AP_System-BearerContextModificationResponse.h"
#include "E1AP_DRB-Setup-Mod-List-EUTRAN.h"
#include "E1AP_DRB-Failed-Mod-List-EUTRAN.h"
#include "E1AP_DRB-Modified-List-EUTRAN.h"
#include "E1AP_DRB-Failed-To-Modify-List-EUTRAN.h"
#include "E1AP_RetainabilityMeasurementsInfo.h"
#include "E1AP_PDU-Session-Resource-Setup-Mod-List.h"
#include "E1AP_PDU-Session-Resource-Failed-Mod-List.h"
#include "E1AP_PDU-Session-Resource-Modified-List.h"
#include "E1AP_PDU-Session-Resource-Failed-To-Modify-List.h"
#include "E1AP_System-BearerContextModificationRequired.h"
#include "E1AP_DRB-Required-To-Modify-List-EUTRAN.h"
#include "E1AP_DRB-Required-To-Remove-List-EUTRAN.h"
#include "E1AP_PDU-Session-Resource-Required-To-Modify-List.h"
#include "E1AP_System-BearerContextModificationConfirm.h"
#include "E1AP_DRB-Confirm-Modified-List-EUTRAN.h"
#include "E1AP_PDU-Session-Resource-Confirm-Modified-List.h"
#include "E1AP_DRB-Status-List.h"
#include "E1AP_ActivityInformation.h"
#include "E1AP_PPI.h"
#include "E1AP_PDU-Session-To-Notify-List.h"
#include "E1AP_Data-Usage-Report-List.h"
#include "E1AP_System-GNB-CU-UP-CounterCheckRequest.h"
#include "E1AP_DRBs-Subject-To-Counter-Check-List-EUTRAN.h"
#include "E1AP_DRBs-Subject-To-Counter-Check-List-NG-RAN.h"
#include "E1AP_GNB-CU-UP-OverloadInformation.h"
#include "E1AP_DRB-Measurement-Results-Information-List.h"
#include "E1AP_PDU-Session-Resource-Data-Usage-List.h"
#include "E1AP_TraceID.h"
#include "E1AP_TransportLayerAddress.h"
#include "E1AP_PrivacyIndicator.h"
#include "E1AP_URIaddress.h"
#include <NativeInteger.h>
#include "E1AP_RegistrationRequest.h"
#include "E1AP_ReportCharacteristics.h"
#include "E1AP_ReportingPeriodicity.h"
#include "E1AP_TNL-AvailableCapacityIndicator.h"
#include "E1AP_HW-CapacityIndicator.h"
#include "E1AP_DLUPTNLAddressToUpdateList.h"
#include "E1AP_ULUPTNLAddressToUpdateList.h"
#include "E1AP_DRBs-Subject-To-Early-Forwarding-List.h"

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum E1AP_ResetType_ExtIEs__value_PR {
	E1AP_ResetType_ExtIEs__value_PR_NOTHING	/* No components present */
	
} E1AP_ResetType_ExtIEs__value_PR;
typedef enum E1AP_UE_associatedLogicalE1_ConnectionItemRes__value_PR {
	E1AP_UE_associatedLogicalE1_ConnectionItemRes__value_PR_NOTHING,	/* No components present */
	E1AP_UE_associatedLogicalE1_ConnectionItemRes__value_PR_UE_associatedLogicalE1_ConnectionItem
} E1AP_UE_associatedLogicalE1_ConnectionItemRes__value_PR;
typedef enum E1AP_UE_associatedLogicalE1_ConnectionItemResAck__value_PR {
	E1AP_UE_associatedLogicalE1_ConnectionItemResAck__value_PR_NOTHING,	/* No components present */
	E1AP_UE_associatedLogicalE1_ConnectionItemResAck__value_PR_UE_associatedLogicalE1_ConnectionItem
} E1AP_UE_associatedLogicalE1_ConnectionItemResAck__value_PR;
typedef enum E1AP_System_BearerContextSetupRequest_ExtIEs__value_PR {
	E1AP_System_BearerContextSetupRequest_ExtIEs__value_PR_NOTHING	/* No components present */
	
} E1AP_System_BearerContextSetupRequest_ExtIEs__value_PR;
typedef enum E1AP_System_BearerContextSetupResponse_ExtIEs__value_PR {
	E1AP_System_BearerContextSetupResponse_ExtIEs__value_PR_NOTHING	/* No components present */
	
} E1AP_System_BearerContextSetupResponse_ExtIEs__value_PR;
typedef enum E1AP_System_BearerContextModificationRequest_ExtIEs__value_PR {
	E1AP_System_BearerContextModificationRequest_ExtIEs__value_PR_NOTHING	/* No components present */
	
} E1AP_System_BearerContextModificationRequest_ExtIEs__value_PR;
typedef enum E1AP_System_BearerContextModificationResponse_ExtIEs__value_PR {
	E1AP_System_BearerContextModificationResponse_ExtIEs__value_PR_NOTHING	/* No components present */
	
} E1AP_System_BearerContextModificationResponse_ExtIEs__value_PR;
typedef enum E1AP_System_BearerContextModificationRequired_ExtIEs__value_PR {
	E1AP_System_BearerContextModificationRequired_ExtIEs__value_PR_NOTHING	/* No components present */
	
} E1AP_System_BearerContextModificationRequired_ExtIEs__value_PR;
typedef enum E1AP_System_BearerContextModificationConfirm_ExtIEs__value_PR {
	E1AP_System_BearerContextModificationConfirm_ExtIEs__value_PR_NOTHING	/* No components present */
	
} E1AP_System_BearerContextModificationConfirm_ExtIEs__value_PR;
typedef enum E1AP_System_GNB_CU_UP_CounterCheckRequest_ExtIEs__value_PR {
	E1AP_System_GNB_CU_UP_CounterCheckRequest_ExtIEs__value_PR_NOTHING	/* No components present */
	
} E1AP_System_GNB_CU_UP_CounterCheckRequest_ExtIEs__value_PR;
typedef enum E1AP_ActivityInformation_ExtIEs__value_PR {
	E1AP_ActivityInformation_ExtIEs__value_PR_NOTHING	/* No components present */
	
} E1AP_ActivityInformation_ExtIEs__value_PR;
typedef enum E1AP_Cause_ExtIEs__value_PR {
	E1AP_Cause_ExtIEs__value_PR_NOTHING	/* No components present */
	
} E1AP_Cause_ExtIEs__value_PR;
typedef enum E1AP_CP_TNL_Information_ExtIEs__value_PR {
	E1AP_CP_TNL_Information_ExtIEs__value_PR_NOTHING,	/* No components present */
	E1AP_CP_TNL_Information_ExtIEs__value_PR_Endpoint_IP_address_and_port
} E1AP_CP_TNL_Information_ExtIEs__value_PR;
typedef enum E1AP_EarlyForwardingCOUNTInfo_ExtIEs__value_PR {
	E1AP_EarlyForwardingCOUNTInfo_ExtIEs__value_PR_NOTHING	/* No components present */
	
} E1AP_EarlyForwardingCOUNTInfo_ExtIEs__value_PR;
typedef enum E1AP_MDTMode_ExtIEs__value_PR {
	E1AP_MDTMode_ExtIEs__value_PR_NOTHING	/* No components present */
	
} E1AP_MDTMode_ExtIEs__value_PR;
typedef enum E1AP_NPNSupportInfo_ExtIEs__value_PR {
	E1AP_NPNSupportInfo_ExtIEs__value_PR_NOTHING	/* No components present */
	
} E1AP_NPNSupportInfo_ExtIEs__value_PR;
typedef enum E1AP_NPNContextInfo_ExtIEs__value_PR {
	E1AP_NPNContextInfo_ExtIEs__value_PR_NOTHING	/* No components present */
	
} E1AP_NPNContextInfo_ExtIEs__value_PR;
typedef enum E1AP_QoS_Characteristics_ExtIEs__value_PR {
	E1AP_QoS_Characteristics_ExtIEs__value_PR_NOTHING	/* No components present */
	
} E1AP_QoS_Characteristics_ExtIEs__value_PR;
typedef enum E1AP_ROHC_Parameters_ExtIEs__value_PR {
	E1AP_ROHC_Parameters_ExtIEs__value_PR_NOTHING	/* No components present */
	
} E1AP_ROHC_Parameters_ExtIEs__value_PR;
typedef enum E1AP_UP_TNL_Information_ExtIEs__value_PR {
	E1AP_UP_TNL_Information_ExtIEs__value_PR_NOTHING	/* No components present */
	
} E1AP_UP_TNL_Information_ExtIEs__value_PR;
typedef enum E1AP_ResetIEs__value_PR {
	E1AP_ResetIEs__value_PR_NOTHING,	/* No components present */
	E1AP_ResetIEs__value_PR_TransactionID,
	E1AP_ResetIEs__value_PR_Cause,
	E1AP_ResetIEs__value_PR_ResetType
} E1AP_ResetIEs__value_PR;
typedef enum E1AP_ResetAcknowledgeIEs__value_PR {
	E1AP_ResetAcknowledgeIEs__value_PR_NOTHING,	/* No components present */
	E1AP_ResetAcknowledgeIEs__value_PR_TransactionID,
	E1AP_ResetAcknowledgeIEs__value_PR_UE_associatedLogicalE1_ConnectionListResAck,
	E1AP_ResetAcknowledgeIEs__value_PR_CriticalityDiagnostics
} E1AP_ResetAcknowledgeIEs__value_PR;
typedef enum E1AP_ErrorIndication_IEs__value_PR {
	E1AP_ErrorIndication_IEs__value_PR_NOTHING,	/* No components present */
	E1AP_ErrorIndication_IEs__value_PR_TransactionID,
	E1AP_ErrorIndication_IEs__value_PR_GNB_CU_CP_UE_E1AP_ID,
	E1AP_ErrorIndication_IEs__value_PR_GNB_CU_UP_UE_E1AP_ID,
	E1AP_ErrorIndication_IEs__value_PR_Cause,
	E1AP_ErrorIndication_IEs__value_PR_CriticalityDiagnostics
} E1AP_ErrorIndication_IEs__value_PR;
typedef enum E1AP_GNB_CU_UP_E1SetupRequestIEs__value_PR {
	E1AP_GNB_CU_UP_E1SetupRequestIEs__value_PR_NOTHING,	/* No components present */
	E1AP_GNB_CU_UP_E1SetupRequestIEs__value_PR_TransactionID,
	E1AP_GNB_CU_UP_E1SetupRequestIEs__value_PR_GNB_CU_UP_ID,
	E1AP_GNB_CU_UP_E1SetupRequestIEs__value_PR_GNB_CU_UP_Name,
	E1AP_GNB_CU_UP_E1SetupRequestIEs__value_PR_CNSupport,
	E1AP_GNB_CU_UP_E1SetupRequestIEs__value_PR_SupportedPLMNs_List,
	E1AP_GNB_CU_UP_E1SetupRequestIEs__value_PR_GNB_CU_UP_Capacity,
	E1AP_GNB_CU_UP_E1SetupRequestIEs__value_PR_Transport_Layer_Address_Info,
	E1AP_GNB_CU_UP_E1SetupRequestIEs__value_PR_Extended_GNB_CU_UP_Name
} E1AP_GNB_CU_UP_E1SetupRequestIEs__value_PR;
typedef enum E1AP_GNB_CU_UP_E1SetupResponseIEs__value_PR {
	E1AP_GNB_CU_UP_E1SetupResponseIEs__value_PR_NOTHING,	/* No components present */
	E1AP_GNB_CU_UP_E1SetupResponseIEs__value_PR_TransactionID,
	E1AP_GNB_CU_UP_E1SetupResponseIEs__value_PR_GNB_CU_CP_Name,
	E1AP_GNB_CU_UP_E1SetupResponseIEs__value_PR_Transport_Layer_Address_Info,
	E1AP_GNB_CU_UP_E1SetupResponseIEs__value_PR_Extended_GNB_CU_CP_Name
} E1AP_GNB_CU_UP_E1SetupResponseIEs__value_PR;
typedef enum E1AP_GNB_CU_UP_E1SetupFailureIEs__value_PR {
	E1AP_GNB_CU_UP_E1SetupFailureIEs__value_PR_NOTHING,	/* No components present */
	E1AP_GNB_CU_UP_E1SetupFailureIEs__value_PR_TransactionID,
	E1AP_GNB_CU_UP_E1SetupFailureIEs__value_PR_Cause,
	E1AP_GNB_CU_UP_E1SetupFailureIEs__value_PR_TimeToWait,
	E1AP_GNB_CU_UP_E1SetupFailureIEs__value_PR_CriticalityDiagnostics
} E1AP_GNB_CU_UP_E1SetupFailureIEs__value_PR;
typedef enum E1AP_GNB_CU_CP_E1SetupRequestIEs__value_PR {
	E1AP_GNB_CU_CP_E1SetupRequestIEs__value_PR_NOTHING,	/* No components present */
	E1AP_GNB_CU_CP_E1SetupRequestIEs__value_PR_TransactionID,
	E1AP_GNB_CU_CP_E1SetupRequestIEs__value_PR_GNB_CU_CP_Name,
	E1AP_GNB_CU_CP_E1SetupRequestIEs__value_PR_Transport_Layer_Address_Info,
	E1AP_GNB_CU_CP_E1SetupRequestIEs__value_PR_Extended_GNB_CU_CP_Name
} E1AP_GNB_CU_CP_E1SetupRequestIEs__value_PR;
typedef enum E1AP_GNB_CU_CP_E1SetupResponseIEs__value_PR {
	E1AP_GNB_CU_CP_E1SetupResponseIEs__value_PR_NOTHING,	/* No components present */
	E1AP_GNB_CU_CP_E1SetupResponseIEs__value_PR_TransactionID,
	E1AP_GNB_CU_CP_E1SetupResponseIEs__value_PR_GNB_CU_UP_ID,
	E1AP_GNB_CU_CP_E1SetupResponseIEs__value_PR_GNB_CU_UP_Name,
	E1AP_GNB_CU_CP_E1SetupResponseIEs__value_PR_CNSupport,
	E1AP_GNB_CU_CP_E1SetupResponseIEs__value_PR_SupportedPLMNs_List,
	E1AP_GNB_CU_CP_E1SetupResponseIEs__value_PR_GNB_CU_UP_Capacity,
	E1AP_GNB_CU_CP_E1SetupResponseIEs__value_PR_Transport_Layer_Address_Info,
	E1AP_GNB_CU_CP_E1SetupResponseIEs__value_PR_Extended_GNB_CU_UP_Name
} E1AP_GNB_CU_CP_E1SetupResponseIEs__value_PR;
typedef enum E1AP_GNB_CU_CP_E1SetupFailureIEs__value_PR {
	E1AP_GNB_CU_CP_E1SetupFailureIEs__value_PR_NOTHING,	/* No components present */
	E1AP_GNB_CU_CP_E1SetupFailureIEs__value_PR_TransactionID,
	E1AP_GNB_CU_CP_E1SetupFailureIEs__value_PR_Cause,
	E1AP_GNB_CU_CP_E1SetupFailureIEs__value_PR_TimeToWait,
	E1AP_GNB_CU_CP_E1SetupFailureIEs__value_PR_CriticalityDiagnostics
} E1AP_GNB_CU_CP_E1SetupFailureIEs__value_PR;
typedef enum E1AP_GNB_CU_UP_ConfigurationUpdateIEs__value_PR {
	E1AP_GNB_CU_UP_ConfigurationUpdateIEs__value_PR_NOTHING,	/* No components present */
	E1AP_GNB_CU_UP_ConfigurationUpdateIEs__value_PR_TransactionID,
	E1AP_GNB_CU_UP_ConfigurationUpdateIEs__value_PR_GNB_CU_UP_ID,
	E1AP_GNB_CU_UP_ConfigurationUpdateIEs__value_PR_GNB_CU_UP_Name,
	E1AP_GNB_CU_UP_ConfigurationUpdateIEs__value_PR_SupportedPLMNs_List,
	E1AP_GNB_CU_UP_ConfigurationUpdateIEs__value_PR_GNB_CU_UP_Capacity,
	E1AP_GNB_CU_UP_ConfigurationUpdateIEs__value_PR_GNB_CU_UP_TNLA_To_Remove_List,
	E1AP_GNB_CU_UP_ConfigurationUpdateIEs__value_PR_Transport_Layer_Address_Info,
	E1AP_GNB_CU_UP_ConfigurationUpdateIEs__value_PR_Extended_GNB_CU_UP_Name
} E1AP_GNB_CU_UP_ConfigurationUpdateIEs__value_PR;
typedef enum E1AP_GNB_CU_UP_ConfigurationUpdateAcknowledgeIEs__value_PR {
	E1AP_GNB_CU_UP_ConfigurationUpdateAcknowledgeIEs__value_PR_NOTHING,	/* No components present */
	E1AP_GNB_CU_UP_ConfigurationUpdateAcknowledgeIEs__value_PR_TransactionID,
	E1AP_GNB_CU_UP_ConfigurationUpdateAcknowledgeIEs__value_PR_CriticalityDiagnostics,
	E1AP_GNB_CU_UP_ConfigurationUpdateAcknowledgeIEs__value_PR_Transport_Layer_Address_Info
} E1AP_GNB_CU_UP_ConfigurationUpdateAcknowledgeIEs__value_PR;
typedef enum E1AP_GNB_CU_UP_ConfigurationUpdateFailureIEs__value_PR {
	E1AP_GNB_CU_UP_ConfigurationUpdateFailureIEs__value_PR_NOTHING,	/* No components present */
	E1AP_GNB_CU_UP_ConfigurationUpdateFailureIEs__value_PR_TransactionID,
	E1AP_GNB_CU_UP_ConfigurationUpdateFailureIEs__value_PR_Cause,
	E1AP_GNB_CU_UP_ConfigurationUpdateFailureIEs__value_PR_TimeToWait,
	E1AP_GNB_CU_UP_ConfigurationUpdateFailureIEs__value_PR_CriticalityDiagnostics
} E1AP_GNB_CU_UP_ConfigurationUpdateFailureIEs__value_PR;
typedef enum E1AP_GNB_CU_CP_ConfigurationUpdateIEs__value_PR {
	E1AP_GNB_CU_CP_ConfigurationUpdateIEs__value_PR_NOTHING,	/* No components present */
	E1AP_GNB_CU_CP_ConfigurationUpdateIEs__value_PR_TransactionID,
	E1AP_GNB_CU_CP_ConfigurationUpdateIEs__value_PR_GNB_CU_CP_Name,
	E1AP_GNB_CU_CP_ConfigurationUpdateIEs__value_PR_GNB_CU_CP_TNLA_To_Add_List,
	E1AP_GNB_CU_CP_ConfigurationUpdateIEs__value_PR_GNB_CU_CP_TNLA_To_Remove_List,
	E1AP_GNB_CU_CP_ConfigurationUpdateIEs__value_PR_GNB_CU_CP_TNLA_To_Update_List,
	E1AP_GNB_CU_CP_ConfigurationUpdateIEs__value_PR_Transport_Layer_Address_Info,
	E1AP_GNB_CU_CP_ConfigurationUpdateIEs__value_PR_Extended_GNB_CU_CP_Name
} E1AP_GNB_CU_CP_ConfigurationUpdateIEs__value_PR;
typedef enum E1AP_GNB_CU_CP_ConfigurationUpdateAcknowledgeIEs__value_PR {
	E1AP_GNB_CU_CP_ConfigurationUpdateAcknowledgeIEs__value_PR_NOTHING,	/* No components present */
	E1AP_GNB_CU_CP_ConfigurationUpdateAcknowledgeIEs__value_PR_TransactionID,
	E1AP_GNB_CU_CP_ConfigurationUpdateAcknowledgeIEs__value_PR_CriticalityDiagnostics,
	E1AP_GNB_CU_CP_ConfigurationUpdateAcknowledgeIEs__value_PR_GNB_CU_CP_TNLA_Setup_List,
	E1AP_GNB_CU_CP_ConfigurationUpdateAcknowledgeIEs__value_PR_GNB_CU_CP_TNLA_Failed_To_Setup_List,
	E1AP_GNB_CU_CP_ConfigurationUpdateAcknowledgeIEs__value_PR_Transport_Layer_Address_Info
} E1AP_GNB_CU_CP_ConfigurationUpdateAcknowledgeIEs__value_PR;
typedef enum E1AP_GNB_CU_CP_ConfigurationUpdateFailureIEs__value_PR {
	E1AP_GNB_CU_CP_ConfigurationUpdateFailureIEs__value_PR_NOTHING,	/* No components present */
	E1AP_GNB_CU_CP_ConfigurationUpdateFailureIEs__value_PR_TransactionID,
	E1AP_GNB_CU_CP_ConfigurationUpdateFailureIEs__value_PR_Cause,
	E1AP_GNB_CU_CP_ConfigurationUpdateFailureIEs__value_PR_TimeToWait,
	E1AP_GNB_CU_CP_ConfigurationUpdateFailureIEs__value_PR_CriticalityDiagnostics
} E1AP_GNB_CU_CP_ConfigurationUpdateFailureIEs__value_PR;
typedef enum E1AP_E1ReleaseRequestIEs__value_PR {
	E1AP_E1ReleaseRequestIEs__value_PR_NOTHING,	/* No components present */
	E1AP_E1ReleaseRequestIEs__value_PR_TransactionID,
	E1AP_E1ReleaseRequestIEs__value_PR_Cause
} E1AP_E1ReleaseRequestIEs__value_PR;
typedef enum E1AP_E1ReleaseResponseIEs__value_PR {
	E1AP_E1ReleaseResponseIEs__value_PR_NOTHING,	/* No components present */
	E1AP_E1ReleaseResponseIEs__value_PR_TransactionID
} E1AP_E1ReleaseResponseIEs__value_PR;
typedef enum E1AP_BearerContextSetupRequestIEs__value_PR {
	E1AP_BearerContextSetupRequestIEs__value_PR_NOTHING,	/* No components present */
	E1AP_BearerContextSetupRequestIEs__value_PR_GNB_CU_CP_UE_E1AP_ID,
	E1AP_BearerContextSetupRequestIEs__value_PR_SecurityInformation,
	E1AP_BearerContextSetupRequestIEs__value_PR_BitRate,
	E1AP_BearerContextSetupRequestIEs__value_PR_BitRate_1,
	E1AP_BearerContextSetupRequestIEs__value_PR_PLMN_Identity,
	E1AP_BearerContextSetupRequestIEs__value_PR_ActivityNotificationLevel,
	E1AP_BearerContextSetupRequestIEs__value_PR_Inactivity_Timer,
	E1AP_BearerContextSetupRequestIEs__value_PR_BearerContextStatusChange,
	E1AP_BearerContextSetupRequestIEs__value_PR_System_BearerContextSetupRequest,
	E1AP_BearerContextSetupRequestIEs__value_PR_RANUEID,
	E1AP_BearerContextSetupRequestIEs__value_PR_GNB_DU_ID,
	E1AP_BearerContextSetupRequestIEs__value_PR_TraceActivation,
	E1AP_BearerContextSetupRequestIEs__value_PR_NPNContextInfo,
	E1AP_BearerContextSetupRequestIEs__value_PR_MDTPLMNList,
	E1AP_BearerContextSetupRequestIEs__value_PR_CHOInitiation,
	E1AP_BearerContextSetupRequestIEs__value_PR_AdditionalHandoverInfo,
	E1AP_BearerContextSetupRequestIEs__value_PR_DirectForwardingPathAvailability,
	E1AP_BearerContextSetupRequestIEs__value_PR_GNB_CU_UP_UE_E1AP_ID
} E1AP_BearerContextSetupRequestIEs__value_PR;
typedef enum E1AP_EUTRAN_BearerContextSetupRequest__value_PR {
	E1AP_EUTRAN_BearerContextSetupRequest__value_PR_NOTHING,	/* No components present */
	E1AP_EUTRAN_BearerContextSetupRequest__value_PR_DRB_To_Setup_List_EUTRAN,
	E1AP_EUTRAN_BearerContextSetupRequest__value_PR_SubscriberProfileIDforRFP,
	E1AP_EUTRAN_BearerContextSetupRequest__value_PR_AdditionalRRMPriorityIndex
} E1AP_EUTRAN_BearerContextSetupRequest__value_PR;
typedef enum E1AP_NG_RAN_BearerContextSetupRequest__value_PR {
	E1AP_NG_RAN_BearerContextSetupRequest__value_PR_NOTHING,	/* No components present */
	E1AP_NG_RAN_BearerContextSetupRequest__value_PR_PDU_Session_Resource_To_Setup_List
} E1AP_NG_RAN_BearerContextSetupRequest__value_PR;
typedef enum E1AP_BearerContextSetupResponseIEs__value_PR {
	E1AP_BearerContextSetupResponseIEs__value_PR_NOTHING,	/* No components present */
	E1AP_BearerContextSetupResponseIEs__value_PR_GNB_CU_CP_UE_E1AP_ID,
	E1AP_BearerContextSetupResponseIEs__value_PR_GNB_CU_UP_UE_E1AP_ID,
	E1AP_BearerContextSetupResponseIEs__value_PR_System_BearerContextSetupResponse
} E1AP_BearerContextSetupResponseIEs__value_PR;
typedef enum E1AP_EUTRAN_BearerContextSetupResponse__value_PR {
	E1AP_EUTRAN_BearerContextSetupResponse__value_PR_NOTHING,	/* No components present */
	E1AP_EUTRAN_BearerContextSetupResponse__value_PR_DRB_Setup_List_EUTRAN,
	E1AP_EUTRAN_BearerContextSetupResponse__value_PR_DRB_Failed_List_EUTRAN
} E1AP_EUTRAN_BearerContextSetupResponse__value_PR;
typedef enum E1AP_NG_RAN_BearerContextSetupResponse__value_PR {
	E1AP_NG_RAN_BearerContextSetupResponse__value_PR_NOTHING,	/* No components present */
	E1AP_NG_RAN_BearerContextSetupResponse__value_PR_PDU_Session_Resource_Setup_List,
	E1AP_NG_RAN_BearerContextSetupResponse__value_PR_PDU_Session_Resource_Failed_List
} E1AP_NG_RAN_BearerContextSetupResponse__value_PR;
typedef enum E1AP_BearerContextSetupFailureIEs__value_PR {
	E1AP_BearerContextSetupFailureIEs__value_PR_NOTHING,	/* No components present */
	E1AP_BearerContextSetupFailureIEs__value_PR_GNB_CU_CP_UE_E1AP_ID,
	E1AP_BearerContextSetupFailureIEs__value_PR_GNB_CU_UP_UE_E1AP_ID,
	E1AP_BearerContextSetupFailureIEs__value_PR_Cause,
	E1AP_BearerContextSetupFailureIEs__value_PR_CriticalityDiagnostics
} E1AP_BearerContextSetupFailureIEs__value_PR;
typedef enum E1AP_BearerContextModificationRequestIEs__value_PR {
	E1AP_BearerContextModificationRequestIEs__value_PR_NOTHING,	/* No components present */
	E1AP_BearerContextModificationRequestIEs__value_PR_GNB_CU_CP_UE_E1AP_ID,
	E1AP_BearerContextModificationRequestIEs__value_PR_GNB_CU_UP_UE_E1AP_ID,
	E1AP_BearerContextModificationRequestIEs__value_PR_SecurityInformation,
	E1AP_BearerContextModificationRequestIEs__value_PR_BitRate,
	E1AP_BearerContextModificationRequestIEs__value_PR_BitRate_1,
	E1AP_BearerContextModificationRequestIEs__value_PR_BearerContextStatusChange,
	E1AP_BearerContextModificationRequestIEs__value_PR_New_UL_TNL_Information_Required,
	E1AP_BearerContextModificationRequestIEs__value_PR_Inactivity_Timer,
	E1AP_BearerContextModificationRequestIEs__value_PR_DataDiscardRequired,
	E1AP_BearerContextModificationRequestIEs__value_PR_System_BearerContextModificationRequest,
	E1AP_BearerContextModificationRequestIEs__value_PR_RANUEID,
	E1AP_BearerContextModificationRequestIEs__value_PR_GNB_DU_ID,
	E1AP_BearerContextModificationRequestIEs__value_PR_ActivityNotificationLevel
} E1AP_BearerContextModificationRequestIEs__value_PR;
typedef enum E1AP_EUTRAN_BearerContextModificationRequest__value_PR {
	E1AP_EUTRAN_BearerContextModificationRequest__value_PR_NOTHING,	/* No components present */
	E1AP_EUTRAN_BearerContextModificationRequest__value_PR_DRB_To_Setup_Mod_List_EUTRAN,
	E1AP_EUTRAN_BearerContextModificationRequest__value_PR_DRB_To_Modify_List_EUTRAN,
	E1AP_EUTRAN_BearerContextModificationRequest__value_PR_DRB_To_Remove_List_EUTRAN,
	E1AP_EUTRAN_BearerContextModificationRequest__value_PR_SubscriberProfileIDforRFP,
	E1AP_EUTRAN_BearerContextModificationRequest__value_PR_AdditionalRRMPriorityIndex
} E1AP_EUTRAN_BearerContextModificationRequest__value_PR;
typedef enum E1AP_NG_RAN_BearerContextModificationRequest__value_PR {
	E1AP_NG_RAN_BearerContextModificationRequest__value_PR_NOTHING,	/* No components present */
	E1AP_NG_RAN_BearerContextModificationRequest__value_PR_PDU_Session_Resource_To_Setup_Mod_List,
	E1AP_NG_RAN_BearerContextModificationRequest__value_PR_PDU_Session_Resource_To_Modify_List,
	E1AP_NG_RAN_BearerContextModificationRequest__value_PR_PDU_Session_Resource_To_Remove_List
} E1AP_NG_RAN_BearerContextModificationRequest__value_PR;
typedef enum E1AP_BearerContextModificationResponseIEs__value_PR {
	E1AP_BearerContextModificationResponseIEs__value_PR_NOTHING,	/* No components present */
	E1AP_BearerContextModificationResponseIEs__value_PR_GNB_CU_CP_UE_E1AP_ID,
	E1AP_BearerContextModificationResponseIEs__value_PR_GNB_CU_UP_UE_E1AP_ID,
	E1AP_BearerContextModificationResponseIEs__value_PR_System_BearerContextModificationResponse
} E1AP_BearerContextModificationResponseIEs__value_PR;
typedef enum E1AP_EUTRAN_BearerContextModificationResponse__value_PR {
	E1AP_EUTRAN_BearerContextModificationResponse__value_PR_NOTHING,	/* No components present */
	E1AP_EUTRAN_BearerContextModificationResponse__value_PR_DRB_Setup_Mod_List_EUTRAN,
	E1AP_EUTRAN_BearerContextModificationResponse__value_PR_DRB_Failed_Mod_List_EUTRAN,
	E1AP_EUTRAN_BearerContextModificationResponse__value_PR_DRB_Modified_List_EUTRAN,
	E1AP_EUTRAN_BearerContextModificationResponse__value_PR_DRB_Failed_To_Modify_List_EUTRAN,
	E1AP_EUTRAN_BearerContextModificationResponse__value_PR_RetainabilityMeasurementsInfo
} E1AP_EUTRAN_BearerContextModificationResponse__value_PR;
typedef enum E1AP_NG_RAN_BearerContextModificationResponse__value_PR {
	E1AP_NG_RAN_BearerContextModificationResponse__value_PR_NOTHING,	/* No components present */
	E1AP_NG_RAN_BearerContextModificationResponse__value_PR_PDU_Session_Resource_Setup_Mod_List,
	E1AP_NG_RAN_BearerContextModificationResponse__value_PR_PDU_Session_Resource_Failed_Mod_List,
	E1AP_NG_RAN_BearerContextModificationResponse__value_PR_PDU_Session_Resource_Modified_List,
	E1AP_NG_RAN_BearerContextModificationResponse__value_PR_PDU_Session_Resource_Failed_To_Modify_List,
	E1AP_NG_RAN_BearerContextModificationResponse__value_PR_RetainabilityMeasurementsInfo
} E1AP_NG_RAN_BearerContextModificationResponse__value_PR;
typedef enum E1AP_BearerContextModificationFailureIEs__value_PR {
	E1AP_BearerContextModificationFailureIEs__value_PR_NOTHING,	/* No components present */
	E1AP_BearerContextModificationFailureIEs__value_PR_GNB_CU_CP_UE_E1AP_ID,
	E1AP_BearerContextModificationFailureIEs__value_PR_GNB_CU_UP_UE_E1AP_ID,
	E1AP_BearerContextModificationFailureIEs__value_PR_Cause,
	E1AP_BearerContextModificationFailureIEs__value_PR_CriticalityDiagnostics
} E1AP_BearerContextModificationFailureIEs__value_PR;
typedef enum E1AP_BearerContextModificationRequiredIEs__value_PR {
	E1AP_BearerContextModificationRequiredIEs__value_PR_NOTHING,	/* No components present */
	E1AP_BearerContextModificationRequiredIEs__value_PR_GNB_CU_CP_UE_E1AP_ID,
	E1AP_BearerContextModificationRequiredIEs__value_PR_GNB_CU_UP_UE_E1AP_ID,
	E1AP_BearerContextModificationRequiredIEs__value_PR_System_BearerContextModificationRequired
} E1AP_BearerContextModificationRequiredIEs__value_PR;
typedef enum E1AP_EUTRAN_BearerContextModificationRequired__value_PR {
	E1AP_EUTRAN_BearerContextModificationRequired__value_PR_NOTHING,	/* No components present */
	E1AP_EUTRAN_BearerContextModificationRequired__value_PR_DRB_Required_To_Modify_List_EUTRAN,
	E1AP_EUTRAN_BearerContextModificationRequired__value_PR_DRB_Required_To_Remove_List_EUTRAN
} E1AP_EUTRAN_BearerContextModificationRequired__value_PR;
typedef enum E1AP_NG_RAN_BearerContextModificationRequired__value_PR {
	E1AP_NG_RAN_BearerContextModificationRequired__value_PR_NOTHING,	/* No components present */
	E1AP_NG_RAN_BearerContextModificationRequired__value_PR_PDU_Session_Resource_Required_To_Modify_List,
	E1AP_NG_RAN_BearerContextModificationRequired__value_PR_PDU_Session_Resource_To_Remove_List
} E1AP_NG_RAN_BearerContextModificationRequired__value_PR;
typedef enum E1AP_BearerContextModificationConfirmIEs__value_PR {
	E1AP_BearerContextModificationConfirmIEs__value_PR_NOTHING,	/* No components present */
	E1AP_BearerContextModificationConfirmIEs__value_PR_GNB_CU_CP_UE_E1AP_ID,
	E1AP_BearerContextModificationConfirmIEs__value_PR_GNB_CU_UP_UE_E1AP_ID,
	E1AP_BearerContextModificationConfirmIEs__value_PR_System_BearerContextModificationConfirm
} E1AP_BearerContextModificationConfirmIEs__value_PR;
typedef enum E1AP_EUTRAN_BearerContextModificationConfirm__value_PR {
	E1AP_EUTRAN_BearerContextModificationConfirm__value_PR_NOTHING,	/* No components present */
	E1AP_EUTRAN_BearerContextModificationConfirm__value_PR_DRB_Confirm_Modified_List_EUTRAN
} E1AP_EUTRAN_BearerContextModificationConfirm__value_PR;
typedef enum E1AP_NG_RAN_BearerContextModificationConfirm__value_PR {
	E1AP_NG_RAN_BearerContextModificationConfirm__value_PR_NOTHING,	/* No components present */
	E1AP_NG_RAN_BearerContextModificationConfirm__value_PR_PDU_Session_Resource_Confirm_Modified_List
} E1AP_NG_RAN_BearerContextModificationConfirm__value_PR;
typedef enum E1AP_BearerContextReleaseCommandIEs__value_PR {
	E1AP_BearerContextReleaseCommandIEs__value_PR_NOTHING,	/* No components present */
	E1AP_BearerContextReleaseCommandIEs__value_PR_GNB_CU_CP_UE_E1AP_ID,
	E1AP_BearerContextReleaseCommandIEs__value_PR_GNB_CU_UP_UE_E1AP_ID,
	E1AP_BearerContextReleaseCommandIEs__value_PR_Cause
} E1AP_BearerContextReleaseCommandIEs__value_PR;
typedef enum E1AP_BearerContextReleaseCompleteIEs__value_PR {
	E1AP_BearerContextReleaseCompleteIEs__value_PR_NOTHING,	/* No components present */
	E1AP_BearerContextReleaseCompleteIEs__value_PR_GNB_CU_CP_UE_E1AP_ID,
	E1AP_BearerContextReleaseCompleteIEs__value_PR_GNB_CU_UP_UE_E1AP_ID,
	E1AP_BearerContextReleaseCompleteIEs__value_PR_CriticalityDiagnostics,
	E1AP_BearerContextReleaseCompleteIEs__value_PR_RetainabilityMeasurementsInfo
} E1AP_BearerContextReleaseCompleteIEs__value_PR;
typedef enum E1AP_BearerContextReleaseRequestIEs__value_PR {
	E1AP_BearerContextReleaseRequestIEs__value_PR_NOTHING,	/* No components present */
	E1AP_BearerContextReleaseRequestIEs__value_PR_GNB_CU_CP_UE_E1AP_ID,
	E1AP_BearerContextReleaseRequestIEs__value_PR_GNB_CU_UP_UE_E1AP_ID,
	E1AP_BearerContextReleaseRequestIEs__value_PR_DRB_Status_List,
	E1AP_BearerContextReleaseRequestIEs__value_PR_Cause
} E1AP_BearerContextReleaseRequestIEs__value_PR;
typedef enum E1AP_BearerContextInactivityNotificationIEs__value_PR {
	E1AP_BearerContextInactivityNotificationIEs__value_PR_NOTHING,	/* No components present */
	E1AP_BearerContextInactivityNotificationIEs__value_PR_GNB_CU_CP_UE_E1AP_ID,
	E1AP_BearerContextInactivityNotificationIEs__value_PR_GNB_CU_UP_UE_E1AP_ID,
	E1AP_BearerContextInactivityNotificationIEs__value_PR_ActivityInformation
} E1AP_BearerContextInactivityNotificationIEs__value_PR;
typedef enum E1AP_DLDataNotificationIEs__value_PR {
	E1AP_DLDataNotificationIEs__value_PR_NOTHING,	/* No components present */
	E1AP_DLDataNotificationIEs__value_PR_GNB_CU_CP_UE_E1AP_ID,
	E1AP_DLDataNotificationIEs__value_PR_GNB_CU_UP_UE_E1AP_ID,
	E1AP_DLDataNotificationIEs__value_PR_PPI,
	E1AP_DLDataNotificationIEs__value_PR_PDU_Session_To_Notify_List
} E1AP_DLDataNotificationIEs__value_PR;
typedef enum E1AP_ULDataNotificationIEs__value_PR {
	E1AP_ULDataNotificationIEs__value_PR_NOTHING,	/* No components present */
	E1AP_ULDataNotificationIEs__value_PR_GNB_CU_CP_UE_E1AP_ID,
	E1AP_ULDataNotificationIEs__value_PR_GNB_CU_UP_UE_E1AP_ID,
	E1AP_ULDataNotificationIEs__value_PR_PDU_Session_To_Notify_List
} E1AP_ULDataNotificationIEs__value_PR;
typedef enum E1AP_DataUsageReportIEs__value_PR {
	E1AP_DataUsageReportIEs__value_PR_NOTHING,	/* No components present */
	E1AP_DataUsageReportIEs__value_PR_GNB_CU_CP_UE_E1AP_ID,
	E1AP_DataUsageReportIEs__value_PR_GNB_CU_UP_UE_E1AP_ID,
	E1AP_DataUsageReportIEs__value_PR_Data_Usage_Report_List
} E1AP_DataUsageReportIEs__value_PR;
typedef enum E1AP_GNB_CU_UP_CounterCheckRequestIEs__value_PR {
	E1AP_GNB_CU_UP_CounterCheckRequestIEs__value_PR_NOTHING,	/* No components present */
	E1AP_GNB_CU_UP_CounterCheckRequestIEs__value_PR_GNB_CU_CP_UE_E1AP_ID,
	E1AP_GNB_CU_UP_CounterCheckRequestIEs__value_PR_GNB_CU_UP_UE_E1AP_ID,
	E1AP_GNB_CU_UP_CounterCheckRequestIEs__value_PR_System_GNB_CU_UP_CounterCheckRequest
} E1AP_GNB_CU_UP_CounterCheckRequestIEs__value_PR;
typedef enum E1AP_EUTRAN_GNB_CU_UP_CounterCheckRequest__value_PR {
	E1AP_EUTRAN_GNB_CU_UP_CounterCheckRequest__value_PR_NOTHING,	/* No components present */
	E1AP_EUTRAN_GNB_CU_UP_CounterCheckRequest__value_PR_DRBs_Subject_To_Counter_Check_List_EUTRAN
} E1AP_EUTRAN_GNB_CU_UP_CounterCheckRequest__value_PR;
typedef enum E1AP_NG_RAN_GNB_CU_UP_CounterCheckRequest__value_PR {
	E1AP_NG_RAN_GNB_CU_UP_CounterCheckRequest__value_PR_NOTHING,	/* No components present */
	E1AP_NG_RAN_GNB_CU_UP_CounterCheckRequest__value_PR_DRBs_Subject_To_Counter_Check_List_NG_RAN
} E1AP_NG_RAN_GNB_CU_UP_CounterCheckRequest__value_PR;
typedef enum E1AP_GNB_CU_UP_StatusIndicationIEs__value_PR {
	E1AP_GNB_CU_UP_StatusIndicationIEs__value_PR_NOTHING,	/* No components present */
	E1AP_GNB_CU_UP_StatusIndicationIEs__value_PR_TransactionID,
	E1AP_GNB_CU_UP_StatusIndicationIEs__value_PR_GNB_CU_UP_OverloadInformation
} E1AP_GNB_CU_UP_StatusIndicationIEs__value_PR;
typedef enum E1AP_GNB_CU_CPMeasurementResultsInformationIEs__value_PR {
	E1AP_GNB_CU_CPMeasurementResultsInformationIEs__value_PR_NOTHING,	/* No components present */
	E1AP_GNB_CU_CPMeasurementResultsInformationIEs__value_PR_GNB_CU_CP_UE_E1AP_ID,
	E1AP_GNB_CU_CPMeasurementResultsInformationIEs__value_PR_GNB_CU_UP_UE_E1AP_ID,
	E1AP_GNB_CU_CPMeasurementResultsInformationIEs__value_PR_DRB_Measurement_Results_Information_List
} E1AP_GNB_CU_CPMeasurementResultsInformationIEs__value_PR;
typedef enum E1AP_MRDC_DataUsageReportIEs__value_PR {
	E1AP_MRDC_DataUsageReportIEs__value_PR_NOTHING,	/* No components present */
	E1AP_MRDC_DataUsageReportIEs__value_PR_GNB_CU_CP_UE_E1AP_ID,
	E1AP_MRDC_DataUsageReportIEs__value_PR_GNB_CU_UP_UE_E1AP_ID,
	E1AP_MRDC_DataUsageReportIEs__value_PR_PDU_Session_Resource_Data_Usage_List
} E1AP_MRDC_DataUsageReportIEs__value_PR;
typedef enum E1AP_TraceStartIEs__value_PR {
	E1AP_TraceStartIEs__value_PR_NOTHING,	/* No components present */
	E1AP_TraceStartIEs__value_PR_GNB_CU_CP_UE_E1AP_ID,
	E1AP_TraceStartIEs__value_PR_GNB_CU_UP_UE_E1AP_ID,
	E1AP_TraceStartIEs__value_PR_TraceActivation
} E1AP_TraceStartIEs__value_PR;
typedef enum E1AP_DeactivateTraceIEs__value_PR {
	E1AP_DeactivateTraceIEs__value_PR_NOTHING,	/* No components present */
	E1AP_DeactivateTraceIEs__value_PR_GNB_CU_CP_UE_E1AP_ID,
	E1AP_DeactivateTraceIEs__value_PR_GNB_CU_UP_UE_E1AP_ID,
	E1AP_DeactivateTraceIEs__value_PR_TraceID
} E1AP_DeactivateTraceIEs__value_PR;
typedef enum E1AP_CellTrafficTraceIEs__value_PR {
	E1AP_CellTrafficTraceIEs__value_PR_NOTHING,	/* No components present */
	E1AP_CellTrafficTraceIEs__value_PR_GNB_CU_CP_UE_E1AP_ID,
	E1AP_CellTrafficTraceIEs__value_PR_GNB_CU_UP_UE_E1AP_ID,
	E1AP_CellTrafficTraceIEs__value_PR_TraceID,
	E1AP_CellTrafficTraceIEs__value_PR_TransportLayerAddress,
	E1AP_CellTrafficTraceIEs__value_PR_PrivacyIndicator,
	E1AP_CellTrafficTraceIEs__value_PR_URIaddress
} E1AP_CellTrafficTraceIEs__value_PR;
typedef enum E1AP_ResourceStatusRequestIEs__value_PR {
	E1AP_ResourceStatusRequestIEs__value_PR_NOTHING,	/* No components present */
	E1AP_ResourceStatusRequestIEs__value_PR_TransactionID,
	E1AP_ResourceStatusRequestIEs__value_PR_INTEGER_1_4095_,
	E1AP_ResourceStatusRequestIEs__value_PR_INTEGER_1_4095__1,
	E1AP_ResourceStatusRequestIEs__value_PR_RegistrationRequest,
	E1AP_ResourceStatusRequestIEs__value_PR_ReportCharacteristics,
	E1AP_ResourceStatusRequestIEs__value_PR_ReportingPeriodicity
} E1AP_ResourceStatusRequestIEs__value_PR;
typedef enum E1AP_ResourceStatusResponseIEs__value_PR {
	E1AP_ResourceStatusResponseIEs__value_PR_NOTHING,	/* No components present */
	E1AP_ResourceStatusResponseIEs__value_PR_TransactionID,
	E1AP_ResourceStatusResponseIEs__value_PR_INTEGER_1_4095_,
	E1AP_ResourceStatusResponseIEs__value_PR_INTEGER_1_4095__1,
	E1AP_ResourceStatusResponseIEs__value_PR_CriticalityDiagnostics
} E1AP_ResourceStatusResponseIEs__value_PR;
typedef enum E1AP_ResourceStatusFailureIEs__value_PR {
	E1AP_ResourceStatusFailureIEs__value_PR_NOTHING,	/* No components present */
	E1AP_ResourceStatusFailureIEs__value_PR_TransactionID,
	E1AP_ResourceStatusFailureIEs__value_PR_INTEGER_1_4095_,
	E1AP_ResourceStatusFailureIEs__value_PR_INTEGER_1_4095__1,
	E1AP_ResourceStatusFailureIEs__value_PR_Cause,
	E1AP_ResourceStatusFailureIEs__value_PR_CriticalityDiagnostics
} E1AP_ResourceStatusFailureIEs__value_PR;
typedef enum E1AP_ResourceStatusUpdateIEs__value_PR {
	E1AP_ResourceStatusUpdateIEs__value_PR_NOTHING,	/* No components present */
	E1AP_ResourceStatusUpdateIEs__value_PR_TransactionID,
	E1AP_ResourceStatusUpdateIEs__value_PR_INTEGER_1_4095_,
	E1AP_ResourceStatusUpdateIEs__value_PR_INTEGER_1_4095__1,
	E1AP_ResourceStatusUpdateIEs__value_PR_TNL_AvailableCapacityIndicator,
	E1AP_ResourceStatusUpdateIEs__value_PR_HW_CapacityIndicator
} E1AP_ResourceStatusUpdateIEs__value_PR;
typedef enum E1AP_IAB_UPTNLAddressUpdateIEs__value_PR {
	E1AP_IAB_UPTNLAddressUpdateIEs__value_PR_NOTHING,	/* No components present */
	E1AP_IAB_UPTNLAddressUpdateIEs__value_PR_TransactionID,
	E1AP_IAB_UPTNLAddressUpdateIEs__value_PR_DLUPTNLAddressToUpdateList
} E1AP_IAB_UPTNLAddressUpdateIEs__value_PR;
typedef enum E1AP_IAB_UPTNLAddressUpdateAcknowledgeIEs__value_PR {
	E1AP_IAB_UPTNLAddressUpdateAcknowledgeIEs__value_PR_NOTHING,	/* No components present */
	E1AP_IAB_UPTNLAddressUpdateAcknowledgeIEs__value_PR_TransactionID,
	E1AP_IAB_UPTNLAddressUpdateAcknowledgeIEs__value_PR_CriticalityDiagnostics,
	E1AP_IAB_UPTNLAddressUpdateAcknowledgeIEs__value_PR_ULUPTNLAddressToUpdateList
} E1AP_IAB_UPTNLAddressUpdateAcknowledgeIEs__value_PR;
typedef enum E1AP_IAB_UPTNLAddressUpdateFailureIEs__value_PR {
	E1AP_IAB_UPTNLAddressUpdateFailureIEs__value_PR_NOTHING,	/* No components present */
	E1AP_IAB_UPTNLAddressUpdateFailureIEs__value_PR_TransactionID,
	E1AP_IAB_UPTNLAddressUpdateFailureIEs__value_PR_Cause,
	E1AP_IAB_UPTNLAddressUpdateFailureIEs__value_PR_TimeToWait,
	E1AP_IAB_UPTNLAddressUpdateFailureIEs__value_PR_CriticalityDiagnostics
} E1AP_IAB_UPTNLAddressUpdateFailureIEs__value_PR;
typedef enum E1AP_EarlyForwardingSNTransferIEs__value_PR {
	E1AP_EarlyForwardingSNTransferIEs__value_PR_NOTHING,	/* No components present */
	E1AP_EarlyForwardingSNTransferIEs__value_PR_GNB_CU_CP_UE_E1AP_ID,
	E1AP_EarlyForwardingSNTransferIEs__value_PR_GNB_CU_UP_UE_E1AP_ID,
	E1AP_EarlyForwardingSNTransferIEs__value_PR_DRBs_Subject_To_Early_Forwarding_List
} E1AP_EarlyForwardingSNTransferIEs__value_PR;

/* E1AP_ProtocolIE-Field */
typedef struct E1AP_ResetType_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_ResetType_ExtIEs__value {
		E1AP_ResetType_ExtIEs__value_PR present;
		union E1AP_ResetType_ExtIEs__E1AP_value_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ResetType_ExtIEs_t;
typedef struct E1AP_UE_associatedLogicalE1_ConnectionItemRes {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_UE_associatedLogicalE1_ConnectionItemRes__value {
		E1AP_UE_associatedLogicalE1_ConnectionItemRes__value_PR present;
		union E1AP_UE_associatedLogicalE1_ConnectionItemRes__E1AP_value_u {
			E1AP_UE_associatedLogicalE1_ConnectionItem_t	 UE_associatedLogicalE1_ConnectionItem;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_UE_associatedLogicalE1_ConnectionItemRes_t;
typedef struct E1AP_UE_associatedLogicalE1_ConnectionItemResAck {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_UE_associatedLogicalE1_ConnectionItemResAck__value {
		E1AP_UE_associatedLogicalE1_ConnectionItemResAck__value_PR present;
		union E1AP_UE_associatedLogicalE1_ConnectionItemResAck__E1AP_value_u {
			E1AP_UE_associatedLogicalE1_ConnectionItem_t	 UE_associatedLogicalE1_ConnectionItem;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_UE_associatedLogicalE1_ConnectionItemResAck_t;
typedef struct E1AP_System_BearerContextSetupRequest_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_System_BearerContextSetupRequest_ExtIEs__value {
		E1AP_System_BearerContextSetupRequest_ExtIEs__value_PR present;
		union E1AP_System_BearerContextSetupRequest_ExtIEs__E1AP_value_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_System_BearerContextSetupRequest_ExtIEs_t;
typedef struct E1AP_System_BearerContextSetupResponse_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_System_BearerContextSetupResponse_ExtIEs__value {
		E1AP_System_BearerContextSetupResponse_ExtIEs__value_PR present;
		union E1AP_System_BearerContextSetupResponse_ExtIEs__E1AP_value_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_System_BearerContextSetupResponse_ExtIEs_t;
typedef struct E1AP_System_BearerContextModificationRequest_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_System_BearerContextModificationRequest_ExtIEs__value {
		E1AP_System_BearerContextModificationRequest_ExtIEs__value_PR present;
		union E1AP_System_BearerContextModificationRequest_ExtIEs__E1AP_value_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_System_BearerContextModificationRequest_ExtIEs_t;
typedef struct E1AP_System_BearerContextModificationResponse_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_System_BearerContextModificationResponse_ExtIEs__value {
		E1AP_System_BearerContextModificationResponse_ExtIEs__value_PR present;
		union E1AP_System_BearerContextModificationResponse_ExtIEs__E1AP_value_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_System_BearerContextModificationResponse_ExtIEs_t;
typedef struct E1AP_System_BearerContextModificationRequired_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_System_BearerContextModificationRequired_ExtIEs__value {
		E1AP_System_BearerContextModificationRequired_ExtIEs__value_PR present;
		union E1AP_System_BearerContextModificationRequired_ExtIEs__E1AP_value_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_System_BearerContextModificationRequired_ExtIEs_t;
typedef struct E1AP_System_BearerContextModificationConfirm_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_System_BearerContextModificationConfirm_ExtIEs__value {
		E1AP_System_BearerContextModificationConfirm_ExtIEs__value_PR present;
		union E1AP_System_BearerContextModificationConfirm_ExtIEs__E1AP_value_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_System_BearerContextModificationConfirm_ExtIEs_t;
typedef struct E1AP_System_GNB_CU_UP_CounterCheckRequest_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_System_GNB_CU_UP_CounterCheckRequest_ExtIEs__value {
		E1AP_System_GNB_CU_UP_CounterCheckRequest_ExtIEs__value_PR present;
		union E1AP_System_GNB_CU_UP_CounterCheckRequest_ExtIEs__E1AP_value_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_System_GNB_CU_UP_CounterCheckRequest_ExtIEs_t;
typedef struct E1AP_ActivityInformation_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_ActivityInformation_ExtIEs__value {
		E1AP_ActivityInformation_ExtIEs__value_PR present;
		union E1AP_ActivityInformation_ExtIEs__E1AP_value_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ActivityInformation_ExtIEs_t;
typedef struct E1AP_Cause_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_Cause_ExtIEs__value {
		E1AP_Cause_ExtIEs__value_PR present;
		union E1AP_Cause_ExtIEs__E1AP_value_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_Cause_ExtIEs_t;
typedef struct E1AP_CP_TNL_Information_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_CP_TNL_Information_ExtIEs__value {
		E1AP_CP_TNL_Information_ExtIEs__value_PR present;
		union E1AP_CP_TNL_Information_ExtIEs__E1AP_value_u {
			E1AP_Endpoint_IP_address_and_port_t	 Endpoint_IP_address_and_port;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_CP_TNL_Information_ExtIEs_t;
typedef struct E1AP_EarlyForwardingCOUNTInfo_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_EarlyForwardingCOUNTInfo_ExtIEs__value {
		E1AP_EarlyForwardingCOUNTInfo_ExtIEs__value_PR present;
		union E1AP_EarlyForwardingCOUNTInfo_ExtIEs__E1AP_value_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_EarlyForwardingCOUNTInfo_ExtIEs_t;
typedef struct E1AP_MDTMode_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_MDTMode_ExtIEs__value {
		E1AP_MDTMode_ExtIEs__value_PR present;
		union E1AP_MDTMode_ExtIEs__E1AP_value_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_MDTMode_ExtIEs_t;
typedef struct E1AP_NPNSupportInfo_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_NPNSupportInfo_ExtIEs__value {
		E1AP_NPNSupportInfo_ExtIEs__value_PR present;
		union E1AP_NPNSupportInfo_ExtIEs__E1AP_value_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_NPNSupportInfo_ExtIEs_t;
typedef struct E1AP_NPNContextInfo_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_NPNContextInfo_ExtIEs__value {
		E1AP_NPNContextInfo_ExtIEs__value_PR present;
		union E1AP_NPNContextInfo_ExtIEs__E1AP_value_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_NPNContextInfo_ExtIEs_t;
typedef struct E1AP_QoS_Characteristics_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_QoS_Characteristics_ExtIEs__value {
		E1AP_QoS_Characteristics_ExtIEs__value_PR present;
		union E1AP_QoS_Characteristics_ExtIEs__E1AP_value_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_QoS_Characteristics_ExtIEs_t;
typedef struct E1AP_ROHC_Parameters_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_ROHC_Parameters_ExtIEs__value {
		E1AP_ROHC_Parameters_ExtIEs__value_PR present;
		union E1AP_ROHC_Parameters_ExtIEs__E1AP_value_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ROHC_Parameters_ExtIEs_t;
typedef struct E1AP_UP_TNL_Information_ExtIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_UP_TNL_Information_ExtIEs__value {
		E1AP_UP_TNL_Information_ExtIEs__value_PR present;
		union E1AP_UP_TNL_Information_ExtIEs__E1AP_value_u {
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_UP_TNL_Information_ExtIEs_t;
typedef struct E1AP_ResetIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_ResetIEs__value {
		E1AP_ResetIEs__value_PR present;
		union E1AP_ResetIEs__E1AP_value_u {
			E1AP_TransactionID_t	 TransactionID;
			E1AP_Cause_t	 Cause;
			E1AP_ResetType_t	 ResetType;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ResetIEs_t;
typedef struct E1AP_ResetAcknowledgeIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_ResetAcknowledgeIEs__value {
		E1AP_ResetAcknowledgeIEs__value_PR present;
		union E1AP_ResetAcknowledgeIEs__E1AP_value_u {
			E1AP_TransactionID_t	 TransactionID;
			E1AP_UE_associatedLogicalE1_ConnectionListResAck_t	 UE_associatedLogicalE1_ConnectionListResAck;
			E1AP_CriticalityDiagnostics_t	 CriticalityDiagnostics;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ResetAcknowledgeIEs_t;
typedef struct E1AP_ErrorIndication_IEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_ErrorIndication_IEs__value {
		E1AP_ErrorIndication_IEs__value_PR present;
		union E1AP_ErrorIndication_IEs__E1AP_value_u {
			E1AP_TransactionID_t	 TransactionID;
			E1AP_GNB_CU_CP_UE_E1AP_ID_t	 GNB_CU_CP_UE_E1AP_ID;
			E1AP_GNB_CU_UP_UE_E1AP_ID_t	 GNB_CU_UP_UE_E1AP_ID;
			E1AP_Cause_t	 Cause;
			E1AP_CriticalityDiagnostics_t	 CriticalityDiagnostics;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ErrorIndication_IEs_t;
typedef struct E1AP_GNB_CU_UP_E1SetupRequestIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GNB_CU_UP_E1SetupRequestIEs__value {
		E1AP_GNB_CU_UP_E1SetupRequestIEs__value_PR present;
		union E1AP_GNB_CU_UP_E1SetupRequestIEs__E1AP_value_u {
			E1AP_TransactionID_t	 TransactionID;
			E1AP_GNB_CU_UP_ID_t	 GNB_CU_UP_ID;
			E1AP_GNB_CU_UP_Name_t	 GNB_CU_UP_Name;
			E1AP_CNSupport_t	 CNSupport;
			E1AP_SupportedPLMNs_List_t	 SupportedPLMNs_List;
			E1AP_GNB_CU_UP_Capacity_t	 GNB_CU_UP_Capacity;
			E1AP_Transport_Layer_Address_Info_t	 Transport_Layer_Address_Info;
			E1AP_Extended_GNB_CU_UP_Name_t	 Extended_GNB_CU_UP_Name;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GNB_CU_UP_E1SetupRequestIEs_t;
typedef struct E1AP_GNB_CU_UP_E1SetupResponseIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GNB_CU_UP_E1SetupResponseIEs__value {
		E1AP_GNB_CU_UP_E1SetupResponseIEs__value_PR present;
		union E1AP_GNB_CU_UP_E1SetupResponseIEs__E1AP_value_u {
			E1AP_TransactionID_t	 TransactionID;
			E1AP_GNB_CU_CP_Name_t	 GNB_CU_CP_Name;
			E1AP_Transport_Layer_Address_Info_t	 Transport_Layer_Address_Info;
			E1AP_Extended_GNB_CU_CP_Name_t	 Extended_GNB_CU_CP_Name;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GNB_CU_UP_E1SetupResponseIEs_t;
typedef struct E1AP_GNB_CU_UP_E1SetupFailureIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GNB_CU_UP_E1SetupFailureIEs__value {
		E1AP_GNB_CU_UP_E1SetupFailureIEs__value_PR present;
		union E1AP_GNB_CU_UP_E1SetupFailureIEs__E1AP_value_u {
			E1AP_TransactionID_t	 TransactionID;
			E1AP_Cause_t	 Cause;
			E1AP_TimeToWait_t	 TimeToWait;
			E1AP_CriticalityDiagnostics_t	 CriticalityDiagnostics;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GNB_CU_UP_E1SetupFailureIEs_t;
typedef struct E1AP_GNB_CU_CP_E1SetupRequestIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GNB_CU_CP_E1SetupRequestIEs__value {
		E1AP_GNB_CU_CP_E1SetupRequestIEs__value_PR present;
		union E1AP_GNB_CU_CP_E1SetupRequestIEs__E1AP_value_u {
			E1AP_TransactionID_t	 TransactionID;
			E1AP_GNB_CU_CP_Name_t	 GNB_CU_CP_Name;
			E1AP_Transport_Layer_Address_Info_t	 Transport_Layer_Address_Info;
			E1AP_Extended_GNB_CU_CP_Name_t	 Extended_GNB_CU_CP_Name;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GNB_CU_CP_E1SetupRequestIEs_t;
typedef struct E1AP_GNB_CU_CP_E1SetupResponseIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GNB_CU_CP_E1SetupResponseIEs__value {
		E1AP_GNB_CU_CP_E1SetupResponseIEs__value_PR present;
		union E1AP_GNB_CU_CP_E1SetupResponseIEs__E1AP_value_u {
			E1AP_TransactionID_t	 TransactionID;
			E1AP_GNB_CU_UP_ID_t	 GNB_CU_UP_ID;
			E1AP_GNB_CU_UP_Name_t	 GNB_CU_UP_Name;
			E1AP_CNSupport_t	 CNSupport;
			E1AP_SupportedPLMNs_List_t	 SupportedPLMNs_List;
			E1AP_GNB_CU_UP_Capacity_t	 GNB_CU_UP_Capacity;
			E1AP_Transport_Layer_Address_Info_t	 Transport_Layer_Address_Info;
			E1AP_Extended_GNB_CU_UP_Name_t	 Extended_GNB_CU_UP_Name;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GNB_CU_CP_E1SetupResponseIEs_t;
typedef struct E1AP_GNB_CU_CP_E1SetupFailureIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GNB_CU_CP_E1SetupFailureIEs__value {
		E1AP_GNB_CU_CP_E1SetupFailureIEs__value_PR present;
		union E1AP_GNB_CU_CP_E1SetupFailureIEs__E1AP_value_u {
			E1AP_TransactionID_t	 TransactionID;
			E1AP_Cause_t	 Cause;
			E1AP_TimeToWait_t	 TimeToWait;
			E1AP_CriticalityDiagnostics_t	 CriticalityDiagnostics;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GNB_CU_CP_E1SetupFailureIEs_t;
typedef struct E1AP_GNB_CU_UP_ConfigurationUpdateIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GNB_CU_UP_ConfigurationUpdateIEs__value {
		E1AP_GNB_CU_UP_ConfigurationUpdateIEs__value_PR present;
		union E1AP_GNB_CU_UP_ConfigurationUpdateIEs__E1AP_value_u {
			E1AP_TransactionID_t	 TransactionID;
			E1AP_GNB_CU_UP_ID_t	 GNB_CU_UP_ID;
			E1AP_GNB_CU_UP_Name_t	 GNB_CU_UP_Name;
			E1AP_SupportedPLMNs_List_t	 SupportedPLMNs_List;
			E1AP_GNB_CU_UP_Capacity_t	 GNB_CU_UP_Capacity;
			E1AP_GNB_CU_UP_TNLA_To_Remove_List_t	 GNB_CU_UP_TNLA_To_Remove_List;
			E1AP_Transport_Layer_Address_Info_t	 Transport_Layer_Address_Info;
			E1AP_Extended_GNB_CU_UP_Name_t	 Extended_GNB_CU_UP_Name;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GNB_CU_UP_ConfigurationUpdateIEs_t;
typedef struct E1AP_GNB_CU_UP_ConfigurationUpdateAcknowledgeIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GNB_CU_UP_ConfigurationUpdateAcknowledgeIEs__value {
		E1AP_GNB_CU_UP_ConfigurationUpdateAcknowledgeIEs__value_PR present;
		union E1AP_GNB_CU_UP_ConfigurationUpdateAcknowledgeIEs__E1AP_value_u {
			E1AP_TransactionID_t	 TransactionID;
			E1AP_CriticalityDiagnostics_t	 CriticalityDiagnostics;
			E1AP_Transport_Layer_Address_Info_t	 Transport_Layer_Address_Info;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GNB_CU_UP_ConfigurationUpdateAcknowledgeIEs_t;
typedef struct E1AP_GNB_CU_UP_ConfigurationUpdateFailureIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GNB_CU_UP_ConfigurationUpdateFailureIEs__value {
		E1AP_GNB_CU_UP_ConfigurationUpdateFailureIEs__value_PR present;
		union E1AP_GNB_CU_UP_ConfigurationUpdateFailureIEs__E1AP_value_u {
			E1AP_TransactionID_t	 TransactionID;
			E1AP_Cause_t	 Cause;
			E1AP_TimeToWait_t	 TimeToWait;
			E1AP_CriticalityDiagnostics_t	 CriticalityDiagnostics;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GNB_CU_UP_ConfigurationUpdateFailureIEs_t;
typedef struct E1AP_GNB_CU_CP_ConfigurationUpdateIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GNB_CU_CP_ConfigurationUpdateIEs__value {
		E1AP_GNB_CU_CP_ConfigurationUpdateIEs__value_PR present;
		union E1AP_GNB_CU_CP_ConfigurationUpdateIEs__E1AP_value_u {
			E1AP_TransactionID_t	 TransactionID;
			E1AP_GNB_CU_CP_Name_t	 GNB_CU_CP_Name;
			E1AP_GNB_CU_CP_TNLA_To_Add_List_t	 GNB_CU_CP_TNLA_To_Add_List;
			E1AP_GNB_CU_CP_TNLA_To_Remove_List_t	 GNB_CU_CP_TNLA_To_Remove_List;
			E1AP_GNB_CU_CP_TNLA_To_Update_List_t	 GNB_CU_CP_TNLA_To_Update_List;
			E1AP_Transport_Layer_Address_Info_t	 Transport_Layer_Address_Info;
			E1AP_Extended_GNB_CU_CP_Name_t	 Extended_GNB_CU_CP_Name;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GNB_CU_CP_ConfigurationUpdateIEs_t;
typedef struct E1AP_GNB_CU_CP_ConfigurationUpdateAcknowledgeIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GNB_CU_CP_ConfigurationUpdateAcknowledgeIEs__value {
		E1AP_GNB_CU_CP_ConfigurationUpdateAcknowledgeIEs__value_PR present;
		union E1AP_GNB_CU_CP_ConfigurationUpdateAcknowledgeIEs__E1AP_value_u {
			E1AP_TransactionID_t	 TransactionID;
			E1AP_CriticalityDiagnostics_t	 CriticalityDiagnostics;
			E1AP_GNB_CU_CP_TNLA_Setup_List_t	 GNB_CU_CP_TNLA_Setup_List;
			E1AP_GNB_CU_CP_TNLA_Failed_To_Setup_List_t	 GNB_CU_CP_TNLA_Failed_To_Setup_List;
			E1AP_Transport_Layer_Address_Info_t	 Transport_Layer_Address_Info;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GNB_CU_CP_ConfigurationUpdateAcknowledgeIEs_t;
typedef struct E1AP_GNB_CU_CP_ConfigurationUpdateFailureIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GNB_CU_CP_ConfigurationUpdateFailureIEs__value {
		E1AP_GNB_CU_CP_ConfigurationUpdateFailureIEs__value_PR present;
		union E1AP_GNB_CU_CP_ConfigurationUpdateFailureIEs__E1AP_value_u {
			E1AP_TransactionID_t	 TransactionID;
			E1AP_Cause_t	 Cause;
			E1AP_TimeToWait_t	 TimeToWait;
			E1AP_CriticalityDiagnostics_t	 CriticalityDiagnostics;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GNB_CU_CP_ConfigurationUpdateFailureIEs_t;
typedef struct E1AP_E1ReleaseRequestIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_E1ReleaseRequestIEs__value {
		E1AP_E1ReleaseRequestIEs__value_PR present;
		union E1AP_E1ReleaseRequestIEs__E1AP_value_u {
			E1AP_TransactionID_t	 TransactionID;
			E1AP_Cause_t	 Cause;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_E1ReleaseRequestIEs_t;
typedef struct E1AP_E1ReleaseResponseIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_E1ReleaseResponseIEs__value {
		E1AP_E1ReleaseResponseIEs__value_PR present;
		union E1AP_E1ReleaseResponseIEs__E1AP_value_u {
			E1AP_TransactionID_t	 TransactionID;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_E1ReleaseResponseIEs_t;
typedef struct E1AP_BearerContextSetupRequestIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_BearerContextSetupRequestIEs__value {
		E1AP_BearerContextSetupRequestIEs__value_PR present;
		union E1AP_BearerContextSetupRequestIEs__E1AP_value_u {
			E1AP_GNB_CU_CP_UE_E1AP_ID_t	 GNB_CU_CP_UE_E1AP_ID;
			E1AP_SecurityInformation_t	 SecurityInformation;
			E1AP_BitRate_t	 BitRate;
			E1AP_BitRate_t	 BitRate_1;
			E1AP_PLMN_Identity_t	 PLMN_Identity;
			E1AP_ActivityNotificationLevel_t	 ActivityNotificationLevel;
			E1AP_Inactivity_Timer_t	 Inactivity_Timer;
			E1AP_BearerContextStatusChange_t	 BearerContextStatusChange;
			E1AP_System_BearerContextSetupRequest_t	 System_BearerContextSetupRequest;
			E1AP_RANUEID_t	 RANUEID;
			E1AP_GNB_DU_ID_t	 GNB_DU_ID;
			E1AP_TraceActivation_t	 TraceActivation;
			E1AP_NPNContextInfo_t	 NPNContextInfo;
			E1AP_MDTPLMNList_t	 MDTPLMNList;
			E1AP_CHOInitiation_t	 CHOInitiation;
			E1AP_AdditionalHandoverInfo_t	 AdditionalHandoverInfo;
			E1AP_DirectForwardingPathAvailability_t	 DirectForwardingPathAvailability;
			E1AP_GNB_CU_UP_UE_E1AP_ID_t	 GNB_CU_UP_UE_E1AP_ID;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_BearerContextSetupRequestIEs_t;
typedef struct E1AP_EUTRAN_BearerContextSetupRequest {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_EUTRAN_BearerContextSetupRequest__value {
		E1AP_EUTRAN_BearerContextSetupRequest__value_PR present;
		union E1AP_EUTRAN_BearerContextSetupRequest__E1AP_value_u {
			E1AP_DRB_To_Setup_List_EUTRAN_t	 DRB_To_Setup_List_EUTRAN;
			E1AP_SubscriberProfileIDforRFP_t	 SubscriberProfileIDforRFP;
			E1AP_AdditionalRRMPriorityIndex_t	 AdditionalRRMPriorityIndex;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_EUTRAN_BearerContextSetupRequest_t;
typedef struct E1AP_NG_RAN_BearerContextSetupRequest {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_NG_RAN_BearerContextSetupRequest__value {
		E1AP_NG_RAN_BearerContextSetupRequest__value_PR present;
		union E1AP_NG_RAN_BearerContextSetupRequest__E1AP_value_u {
			E1AP_PDU_Session_Resource_To_Setup_List_t	 PDU_Session_Resource_To_Setup_List;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_NG_RAN_BearerContextSetupRequest_t;
typedef struct E1AP_BearerContextSetupResponseIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_BearerContextSetupResponseIEs__value {
		E1AP_BearerContextSetupResponseIEs__value_PR present;
		union E1AP_BearerContextSetupResponseIEs__E1AP_value_u {
			E1AP_GNB_CU_CP_UE_E1AP_ID_t	 GNB_CU_CP_UE_E1AP_ID;
			E1AP_GNB_CU_UP_UE_E1AP_ID_t	 GNB_CU_UP_UE_E1AP_ID;
			E1AP_System_BearerContextSetupResponse_t	 System_BearerContextSetupResponse;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_BearerContextSetupResponseIEs_t;
typedef struct E1AP_EUTRAN_BearerContextSetupResponse {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_EUTRAN_BearerContextSetupResponse__value {
		E1AP_EUTRAN_BearerContextSetupResponse__value_PR present;
		union E1AP_EUTRAN_BearerContextSetupResponse__E1AP_value_u {
			E1AP_DRB_Setup_List_EUTRAN_t	 DRB_Setup_List_EUTRAN;
			E1AP_DRB_Failed_List_EUTRAN_t	 DRB_Failed_List_EUTRAN;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_EUTRAN_BearerContextSetupResponse_t;
typedef struct E1AP_NG_RAN_BearerContextSetupResponse {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_NG_RAN_BearerContextSetupResponse__value {
		E1AP_NG_RAN_BearerContextSetupResponse__value_PR present;
		union E1AP_NG_RAN_BearerContextSetupResponse__E1AP_value_u {
			E1AP_PDU_Session_Resource_Setup_List_t	 PDU_Session_Resource_Setup_List;
			E1AP_PDU_Session_Resource_Failed_List_t	 PDU_Session_Resource_Failed_List;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_NG_RAN_BearerContextSetupResponse_t;
typedef struct E1AP_BearerContextSetupFailureIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_BearerContextSetupFailureIEs__value {
		E1AP_BearerContextSetupFailureIEs__value_PR present;
		union E1AP_BearerContextSetupFailureIEs__E1AP_value_u {
			E1AP_GNB_CU_CP_UE_E1AP_ID_t	 GNB_CU_CP_UE_E1AP_ID;
			E1AP_GNB_CU_UP_UE_E1AP_ID_t	 GNB_CU_UP_UE_E1AP_ID;
			E1AP_Cause_t	 Cause;
			E1AP_CriticalityDiagnostics_t	 CriticalityDiagnostics;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_BearerContextSetupFailureIEs_t;
typedef struct E1AP_BearerContextModificationRequestIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_BearerContextModificationRequestIEs__value {
		E1AP_BearerContextModificationRequestIEs__value_PR present;
		union E1AP_BearerContextModificationRequestIEs__E1AP_value_u {
			E1AP_GNB_CU_CP_UE_E1AP_ID_t	 GNB_CU_CP_UE_E1AP_ID;
			E1AP_GNB_CU_UP_UE_E1AP_ID_t	 GNB_CU_UP_UE_E1AP_ID;
			E1AP_SecurityInformation_t	 SecurityInformation;
			E1AP_BitRate_t	 BitRate;
			E1AP_BitRate_t	 BitRate_1;
			E1AP_BearerContextStatusChange_t	 BearerContextStatusChange;
			E1AP_New_UL_TNL_Information_Required_t	 New_UL_TNL_Information_Required;
			E1AP_Inactivity_Timer_t	 Inactivity_Timer;
			E1AP_DataDiscardRequired_t	 DataDiscardRequired;
			E1AP_System_BearerContextModificationRequest_t	 System_BearerContextModificationRequest;
			E1AP_RANUEID_t	 RANUEID;
			E1AP_GNB_DU_ID_t	 GNB_DU_ID;
			E1AP_ActivityNotificationLevel_t	 ActivityNotificationLevel;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_BearerContextModificationRequestIEs_t;
typedef struct E1AP_EUTRAN_BearerContextModificationRequest {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_EUTRAN_BearerContextModificationRequest__value {
		E1AP_EUTRAN_BearerContextModificationRequest__value_PR present;
		union E1AP_EUTRAN_BearerContextModificationRequest__E1AP_value_u {
			E1AP_DRB_To_Setup_Mod_List_EUTRAN_t	 DRB_To_Setup_Mod_List_EUTRAN;
			E1AP_DRB_To_Modify_List_EUTRAN_t	 DRB_To_Modify_List_EUTRAN;
			E1AP_DRB_To_Remove_List_EUTRAN_t	 DRB_To_Remove_List_EUTRAN;
			E1AP_SubscriberProfileIDforRFP_t	 SubscriberProfileIDforRFP;
			E1AP_AdditionalRRMPriorityIndex_t	 AdditionalRRMPriorityIndex;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_EUTRAN_BearerContextModificationRequest_t;
typedef struct E1AP_NG_RAN_BearerContextModificationRequest {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_NG_RAN_BearerContextModificationRequest__value {
		E1AP_NG_RAN_BearerContextModificationRequest__value_PR present;
		union E1AP_NG_RAN_BearerContextModificationRequest__E1AP_value_u {
			E1AP_PDU_Session_Resource_To_Setup_Mod_List_t	 PDU_Session_Resource_To_Setup_Mod_List;
			E1AP_PDU_Session_Resource_To_Modify_List_t	 PDU_Session_Resource_To_Modify_List;
			E1AP_PDU_Session_Resource_To_Remove_List_t	 PDU_Session_Resource_To_Remove_List;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_NG_RAN_BearerContextModificationRequest_t;
typedef struct E1AP_BearerContextModificationResponseIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_BearerContextModificationResponseIEs__value {
		E1AP_BearerContextModificationResponseIEs__value_PR present;
		union E1AP_BearerContextModificationResponseIEs__E1AP_value_u {
			E1AP_GNB_CU_CP_UE_E1AP_ID_t	 GNB_CU_CP_UE_E1AP_ID;
			E1AP_GNB_CU_UP_UE_E1AP_ID_t	 GNB_CU_UP_UE_E1AP_ID;
			E1AP_System_BearerContextModificationResponse_t	 System_BearerContextModificationResponse;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_BearerContextModificationResponseIEs_t;
typedef struct E1AP_EUTRAN_BearerContextModificationResponse {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_EUTRAN_BearerContextModificationResponse__value {
		E1AP_EUTRAN_BearerContextModificationResponse__value_PR present;
		union E1AP_EUTRAN_BearerContextModificationResponse__E1AP_value_u {
			E1AP_DRB_Setup_Mod_List_EUTRAN_t	 DRB_Setup_Mod_List_EUTRAN;
			E1AP_DRB_Failed_Mod_List_EUTRAN_t	 DRB_Failed_Mod_List_EUTRAN;
			E1AP_DRB_Modified_List_EUTRAN_t	 DRB_Modified_List_EUTRAN;
			E1AP_DRB_Failed_To_Modify_List_EUTRAN_t	 DRB_Failed_To_Modify_List_EUTRAN;
			E1AP_RetainabilityMeasurementsInfo_t	 RetainabilityMeasurementsInfo;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_EUTRAN_BearerContextModificationResponse_t;
typedef struct E1AP_NG_RAN_BearerContextModificationResponse {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_NG_RAN_BearerContextModificationResponse__value {
		E1AP_NG_RAN_BearerContextModificationResponse__value_PR present;
		union E1AP_NG_RAN_BearerContextModificationResponse__E1AP_value_u {
			E1AP_PDU_Session_Resource_Setup_Mod_List_t	 PDU_Session_Resource_Setup_Mod_List;
			E1AP_PDU_Session_Resource_Failed_Mod_List_t	 PDU_Session_Resource_Failed_Mod_List;
			E1AP_PDU_Session_Resource_Modified_List_t	 PDU_Session_Resource_Modified_List;
			E1AP_PDU_Session_Resource_Failed_To_Modify_List_t	 PDU_Session_Resource_Failed_To_Modify_List;
			E1AP_RetainabilityMeasurementsInfo_t	 RetainabilityMeasurementsInfo;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_NG_RAN_BearerContextModificationResponse_t;
typedef struct E1AP_BearerContextModificationFailureIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_BearerContextModificationFailureIEs__value {
		E1AP_BearerContextModificationFailureIEs__value_PR present;
		union E1AP_BearerContextModificationFailureIEs__E1AP_value_u {
			E1AP_GNB_CU_CP_UE_E1AP_ID_t	 GNB_CU_CP_UE_E1AP_ID;
			E1AP_GNB_CU_UP_UE_E1AP_ID_t	 GNB_CU_UP_UE_E1AP_ID;
			E1AP_Cause_t	 Cause;
			E1AP_CriticalityDiagnostics_t	 CriticalityDiagnostics;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_BearerContextModificationFailureIEs_t;
typedef struct E1AP_BearerContextModificationRequiredIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_BearerContextModificationRequiredIEs__value {
		E1AP_BearerContextModificationRequiredIEs__value_PR present;
		union E1AP_BearerContextModificationRequiredIEs__E1AP_value_u {
			E1AP_GNB_CU_CP_UE_E1AP_ID_t	 GNB_CU_CP_UE_E1AP_ID;
			E1AP_GNB_CU_UP_UE_E1AP_ID_t	 GNB_CU_UP_UE_E1AP_ID;
			E1AP_System_BearerContextModificationRequired_t	 System_BearerContextModificationRequired;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_BearerContextModificationRequiredIEs_t;
typedef struct E1AP_EUTRAN_BearerContextModificationRequired {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_EUTRAN_BearerContextModificationRequired__value {
		E1AP_EUTRAN_BearerContextModificationRequired__value_PR present;
		union E1AP_EUTRAN_BearerContextModificationRequired__E1AP_value_u {
			E1AP_DRB_Required_To_Modify_List_EUTRAN_t	 DRB_Required_To_Modify_List_EUTRAN;
			E1AP_DRB_Required_To_Remove_List_EUTRAN_t	 DRB_Required_To_Remove_List_EUTRAN;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_EUTRAN_BearerContextModificationRequired_t;
typedef struct E1AP_NG_RAN_BearerContextModificationRequired {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_NG_RAN_BearerContextModificationRequired__value {
		E1AP_NG_RAN_BearerContextModificationRequired__value_PR present;
		union E1AP_NG_RAN_BearerContextModificationRequired__E1AP_value_u {
			E1AP_PDU_Session_Resource_Required_To_Modify_List_t	 PDU_Session_Resource_Required_To_Modify_List;
			E1AP_PDU_Session_Resource_To_Remove_List_t	 PDU_Session_Resource_To_Remove_List;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_NG_RAN_BearerContextModificationRequired_t;
typedef struct E1AP_BearerContextModificationConfirmIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_BearerContextModificationConfirmIEs__value {
		E1AP_BearerContextModificationConfirmIEs__value_PR present;
		union E1AP_BearerContextModificationConfirmIEs__E1AP_value_u {
			E1AP_GNB_CU_CP_UE_E1AP_ID_t	 GNB_CU_CP_UE_E1AP_ID;
			E1AP_GNB_CU_UP_UE_E1AP_ID_t	 GNB_CU_UP_UE_E1AP_ID;
			E1AP_System_BearerContextModificationConfirm_t	 System_BearerContextModificationConfirm;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_BearerContextModificationConfirmIEs_t;
typedef struct E1AP_EUTRAN_BearerContextModificationConfirm {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_EUTRAN_BearerContextModificationConfirm__value {
		E1AP_EUTRAN_BearerContextModificationConfirm__value_PR present;
		union E1AP_EUTRAN_BearerContextModificationConfirm__E1AP_value_u {
			E1AP_DRB_Confirm_Modified_List_EUTRAN_t	 DRB_Confirm_Modified_List_EUTRAN;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_EUTRAN_BearerContextModificationConfirm_t;
typedef struct E1AP_NG_RAN_BearerContextModificationConfirm {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_NG_RAN_BearerContextModificationConfirm__value {
		E1AP_NG_RAN_BearerContextModificationConfirm__value_PR present;
		union E1AP_NG_RAN_BearerContextModificationConfirm__E1AP_value_u {
			E1AP_PDU_Session_Resource_Confirm_Modified_List_t	 PDU_Session_Resource_Confirm_Modified_List;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_NG_RAN_BearerContextModificationConfirm_t;
typedef struct E1AP_BearerContextReleaseCommandIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_BearerContextReleaseCommandIEs__value {
		E1AP_BearerContextReleaseCommandIEs__value_PR present;
		union E1AP_BearerContextReleaseCommandIEs__E1AP_value_u {
			E1AP_GNB_CU_CP_UE_E1AP_ID_t	 GNB_CU_CP_UE_E1AP_ID;
			E1AP_GNB_CU_UP_UE_E1AP_ID_t	 GNB_CU_UP_UE_E1AP_ID;
			E1AP_Cause_t	 Cause;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_BearerContextReleaseCommandIEs_t;
typedef struct E1AP_BearerContextReleaseCompleteIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_BearerContextReleaseCompleteIEs__value {
		E1AP_BearerContextReleaseCompleteIEs__value_PR present;
		union E1AP_BearerContextReleaseCompleteIEs__E1AP_value_u {
			E1AP_GNB_CU_CP_UE_E1AP_ID_t	 GNB_CU_CP_UE_E1AP_ID;
			E1AP_GNB_CU_UP_UE_E1AP_ID_t	 GNB_CU_UP_UE_E1AP_ID;
			E1AP_CriticalityDiagnostics_t	 CriticalityDiagnostics;
			E1AP_RetainabilityMeasurementsInfo_t	 RetainabilityMeasurementsInfo;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_BearerContextReleaseCompleteIEs_t;
typedef struct E1AP_BearerContextReleaseRequestIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_BearerContextReleaseRequestIEs__value {
		E1AP_BearerContextReleaseRequestIEs__value_PR present;
		union E1AP_BearerContextReleaseRequestIEs__E1AP_value_u {
			E1AP_GNB_CU_CP_UE_E1AP_ID_t	 GNB_CU_CP_UE_E1AP_ID;
			E1AP_GNB_CU_UP_UE_E1AP_ID_t	 GNB_CU_UP_UE_E1AP_ID;
			E1AP_DRB_Status_List_t	 DRB_Status_List;
			E1AP_Cause_t	 Cause;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_BearerContextReleaseRequestIEs_t;
typedef struct E1AP_BearerContextInactivityNotificationIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_BearerContextInactivityNotificationIEs__value {
		E1AP_BearerContextInactivityNotificationIEs__value_PR present;
		union E1AP_BearerContextInactivityNotificationIEs__E1AP_value_u {
			E1AP_GNB_CU_CP_UE_E1AP_ID_t	 GNB_CU_CP_UE_E1AP_ID;
			E1AP_GNB_CU_UP_UE_E1AP_ID_t	 GNB_CU_UP_UE_E1AP_ID;
			E1AP_ActivityInformation_t	 ActivityInformation;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_BearerContextInactivityNotificationIEs_t;
typedef struct E1AP_DLDataNotificationIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DLDataNotificationIEs__value {
		E1AP_DLDataNotificationIEs__value_PR present;
		union E1AP_DLDataNotificationIEs__E1AP_value_u {
			E1AP_GNB_CU_CP_UE_E1AP_ID_t	 GNB_CU_CP_UE_E1AP_ID;
			E1AP_GNB_CU_UP_UE_E1AP_ID_t	 GNB_CU_UP_UE_E1AP_ID;
			E1AP_PPI_t	 PPI;
			E1AP_PDU_Session_To_Notify_List_t	 PDU_Session_To_Notify_List;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DLDataNotificationIEs_t;
typedef struct E1AP_ULDataNotificationIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_ULDataNotificationIEs__value {
		E1AP_ULDataNotificationIEs__value_PR present;
		union E1AP_ULDataNotificationIEs__E1AP_value_u {
			E1AP_GNB_CU_CP_UE_E1AP_ID_t	 GNB_CU_CP_UE_E1AP_ID;
			E1AP_GNB_CU_UP_UE_E1AP_ID_t	 GNB_CU_UP_UE_E1AP_ID;
			E1AP_PDU_Session_To_Notify_List_t	 PDU_Session_To_Notify_List;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ULDataNotificationIEs_t;
typedef struct E1AP_DataUsageReportIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DataUsageReportIEs__value {
		E1AP_DataUsageReportIEs__value_PR present;
		union E1AP_DataUsageReportIEs__E1AP_value_u {
			E1AP_GNB_CU_CP_UE_E1AP_ID_t	 GNB_CU_CP_UE_E1AP_ID;
			E1AP_GNB_CU_UP_UE_E1AP_ID_t	 GNB_CU_UP_UE_E1AP_ID;
			E1AP_Data_Usage_Report_List_t	 Data_Usage_Report_List;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DataUsageReportIEs_t;
typedef struct E1AP_GNB_CU_UP_CounterCheckRequestIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GNB_CU_UP_CounterCheckRequestIEs__value {
		E1AP_GNB_CU_UP_CounterCheckRequestIEs__value_PR present;
		union E1AP_GNB_CU_UP_CounterCheckRequestIEs__E1AP_value_u {
			E1AP_GNB_CU_CP_UE_E1AP_ID_t	 GNB_CU_CP_UE_E1AP_ID;
			E1AP_GNB_CU_UP_UE_E1AP_ID_t	 GNB_CU_UP_UE_E1AP_ID;
			E1AP_System_GNB_CU_UP_CounterCheckRequest_t	 System_GNB_CU_UP_CounterCheckRequest;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GNB_CU_UP_CounterCheckRequestIEs_t;
typedef struct E1AP_EUTRAN_GNB_CU_UP_CounterCheckRequest {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_EUTRAN_GNB_CU_UP_CounterCheckRequest__value {
		E1AP_EUTRAN_GNB_CU_UP_CounterCheckRequest__value_PR present;
		union E1AP_EUTRAN_GNB_CU_UP_CounterCheckRequest__E1AP_value_u {
			E1AP_DRBs_Subject_To_Counter_Check_List_EUTRAN_t	 DRBs_Subject_To_Counter_Check_List_EUTRAN;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_EUTRAN_GNB_CU_UP_CounterCheckRequest_t;
typedef struct E1AP_NG_RAN_GNB_CU_UP_CounterCheckRequest {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_NG_RAN_GNB_CU_UP_CounterCheckRequest__value {
		E1AP_NG_RAN_GNB_CU_UP_CounterCheckRequest__value_PR present;
		union E1AP_NG_RAN_GNB_CU_UP_CounterCheckRequest__E1AP_value_u {
			E1AP_DRBs_Subject_To_Counter_Check_List_NG_RAN_t	 DRBs_Subject_To_Counter_Check_List_NG_RAN;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_NG_RAN_GNB_CU_UP_CounterCheckRequest_t;
typedef struct E1AP_GNB_CU_UP_StatusIndicationIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GNB_CU_UP_StatusIndicationIEs__value {
		E1AP_GNB_CU_UP_StatusIndicationIEs__value_PR present;
		union E1AP_GNB_CU_UP_StatusIndicationIEs__E1AP_value_u {
			E1AP_TransactionID_t	 TransactionID;
			E1AP_GNB_CU_UP_OverloadInformation_t	 GNB_CU_UP_OverloadInformation;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GNB_CU_UP_StatusIndicationIEs_t;
typedef struct E1AP_GNB_CU_CPMeasurementResultsInformationIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_GNB_CU_CPMeasurementResultsInformationIEs__value {
		E1AP_GNB_CU_CPMeasurementResultsInformationIEs__value_PR present;
		union E1AP_GNB_CU_CPMeasurementResultsInformationIEs__E1AP_value_u {
			E1AP_GNB_CU_CP_UE_E1AP_ID_t	 GNB_CU_CP_UE_E1AP_ID;
			E1AP_GNB_CU_UP_UE_E1AP_ID_t	 GNB_CU_UP_UE_E1AP_ID;
			E1AP_DRB_Measurement_Results_Information_List_t	 DRB_Measurement_Results_Information_List;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_GNB_CU_CPMeasurementResultsInformationIEs_t;
typedef struct E1AP_MRDC_DataUsageReportIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_MRDC_DataUsageReportIEs__value {
		E1AP_MRDC_DataUsageReportIEs__value_PR present;
		union E1AP_MRDC_DataUsageReportIEs__E1AP_value_u {
			E1AP_GNB_CU_CP_UE_E1AP_ID_t	 GNB_CU_CP_UE_E1AP_ID;
			E1AP_GNB_CU_UP_UE_E1AP_ID_t	 GNB_CU_UP_UE_E1AP_ID;
			E1AP_PDU_Session_Resource_Data_Usage_List_t	 PDU_Session_Resource_Data_Usage_List;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_MRDC_DataUsageReportIEs_t;
typedef struct E1AP_TraceStartIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_TraceStartIEs__value {
		E1AP_TraceStartIEs__value_PR present;
		union E1AP_TraceStartIEs__E1AP_value_u {
			E1AP_GNB_CU_CP_UE_E1AP_ID_t	 GNB_CU_CP_UE_E1AP_ID;
			E1AP_GNB_CU_UP_UE_E1AP_ID_t	 GNB_CU_UP_UE_E1AP_ID;
			E1AP_TraceActivation_t	 TraceActivation;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_TraceStartIEs_t;
typedef struct E1AP_DeactivateTraceIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_DeactivateTraceIEs__value {
		E1AP_DeactivateTraceIEs__value_PR present;
		union E1AP_DeactivateTraceIEs__E1AP_value_u {
			E1AP_GNB_CU_CP_UE_E1AP_ID_t	 GNB_CU_CP_UE_E1AP_ID;
			E1AP_GNB_CU_UP_UE_E1AP_ID_t	 GNB_CU_UP_UE_E1AP_ID;
			E1AP_TraceID_t	 TraceID;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_DeactivateTraceIEs_t;
typedef struct E1AP_CellTrafficTraceIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_CellTrafficTraceIEs__value {
		E1AP_CellTrafficTraceIEs__value_PR present;
		union E1AP_CellTrafficTraceIEs__E1AP_value_u {
			E1AP_GNB_CU_CP_UE_E1AP_ID_t	 GNB_CU_CP_UE_E1AP_ID;
			E1AP_GNB_CU_UP_UE_E1AP_ID_t	 GNB_CU_UP_UE_E1AP_ID;
			E1AP_TraceID_t	 TraceID;
			E1AP_TransportLayerAddress_t	 TransportLayerAddress;
			E1AP_PrivacyIndicator_t	 PrivacyIndicator;
			E1AP_URIaddress_t	 URIaddress;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_CellTrafficTraceIEs_t;
typedef struct E1AP_ResourceStatusRequestIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_ResourceStatusRequestIEs__value {
		E1AP_ResourceStatusRequestIEs__value_PR present;
		union E1AP_ResourceStatusRequestIEs__E1AP_value_u {
			E1AP_TransactionID_t	 TransactionID;
			long	 INTEGER_1_4095_;
			long	 INTEGER_1_4095__1;
			E1AP_RegistrationRequest_t	 RegistrationRequest;
			E1AP_ReportCharacteristics_t	 ReportCharacteristics;
			E1AP_ReportingPeriodicity_t	 ReportingPeriodicity;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ResourceStatusRequestIEs_t;
typedef struct E1AP_ResourceStatusResponseIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_ResourceStatusResponseIEs__value {
		E1AP_ResourceStatusResponseIEs__value_PR present;
		union E1AP_ResourceStatusResponseIEs__E1AP_value_u {
			E1AP_TransactionID_t	 TransactionID;
			long	 INTEGER_1_4095_;
			long	 INTEGER_1_4095__1;
			E1AP_CriticalityDiagnostics_t	 CriticalityDiagnostics;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ResourceStatusResponseIEs_t;
typedef struct E1AP_ResourceStatusFailureIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_ResourceStatusFailureIEs__value {
		E1AP_ResourceStatusFailureIEs__value_PR present;
		union E1AP_ResourceStatusFailureIEs__E1AP_value_u {
			E1AP_TransactionID_t	 TransactionID;
			long	 INTEGER_1_4095_;
			long	 INTEGER_1_4095__1;
			E1AP_Cause_t	 Cause;
			E1AP_CriticalityDiagnostics_t	 CriticalityDiagnostics;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ResourceStatusFailureIEs_t;
typedef struct E1AP_ResourceStatusUpdateIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_ResourceStatusUpdateIEs__value {
		E1AP_ResourceStatusUpdateIEs__value_PR present;
		union E1AP_ResourceStatusUpdateIEs__E1AP_value_u {
			E1AP_TransactionID_t	 TransactionID;
			long	 INTEGER_1_4095_;
			long	 INTEGER_1_4095__1;
			E1AP_TNL_AvailableCapacityIndicator_t	 TNL_AvailableCapacityIndicator;
			E1AP_HW_CapacityIndicator_t	 HW_CapacityIndicator;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_ResourceStatusUpdateIEs_t;
typedef struct E1AP_IAB_UPTNLAddressUpdateIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_IAB_UPTNLAddressUpdateIEs__value {
		E1AP_IAB_UPTNLAddressUpdateIEs__value_PR present;
		union E1AP_IAB_UPTNLAddressUpdateIEs__E1AP_value_u {
			E1AP_TransactionID_t	 TransactionID;
			E1AP_DLUPTNLAddressToUpdateList_t	 DLUPTNLAddressToUpdateList;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_IAB_UPTNLAddressUpdateIEs_t;
typedef struct E1AP_IAB_UPTNLAddressUpdateAcknowledgeIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_IAB_UPTNLAddressUpdateAcknowledgeIEs__value {
		E1AP_IAB_UPTNLAddressUpdateAcknowledgeIEs__value_PR present;
		union E1AP_IAB_UPTNLAddressUpdateAcknowledgeIEs__E1AP_value_u {
			E1AP_TransactionID_t	 TransactionID;
			E1AP_CriticalityDiagnostics_t	 CriticalityDiagnostics;
			E1AP_ULUPTNLAddressToUpdateList_t	 ULUPTNLAddressToUpdateList;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_IAB_UPTNLAddressUpdateAcknowledgeIEs_t;
typedef struct E1AP_IAB_UPTNLAddressUpdateFailureIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_IAB_UPTNLAddressUpdateFailureIEs__value {
		E1AP_IAB_UPTNLAddressUpdateFailureIEs__value_PR present;
		union E1AP_IAB_UPTNLAddressUpdateFailureIEs__E1AP_value_u {
			E1AP_TransactionID_t	 TransactionID;
			E1AP_Cause_t	 Cause;
			E1AP_TimeToWait_t	 TimeToWait;
			E1AP_CriticalityDiagnostics_t	 CriticalityDiagnostics;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_IAB_UPTNLAddressUpdateFailureIEs_t;
typedef struct E1AP_EarlyForwardingSNTransferIEs {
	E1AP_ProtocolIE_ID_t	 id;
	E1AP_Criticality_t	 criticality;
	struct E1AP_EarlyForwardingSNTransferIEs__value {
		E1AP_EarlyForwardingSNTransferIEs__value_PR present;
		union E1AP_EarlyForwardingSNTransferIEs__E1AP_value_u {
			E1AP_GNB_CU_CP_UE_E1AP_ID_t	 GNB_CU_CP_UE_E1AP_ID;
			E1AP_GNB_CU_UP_UE_E1AP_ID_t	 GNB_CU_UP_UE_E1AP_ID;
			E1AP_DRBs_Subject_To_Early_Forwarding_List_t	 DRBs_Subject_To_Early_Forwarding_List;
		} choice;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} value;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} E1AP_EarlyForwardingSNTransferIEs_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ResetType_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_ResetType_ExtIEs_specs_1;
extern asn_TYPE_member_t asn_MBR_E1AP_ResetType_ExtIEs_1[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_UE_associatedLogicalE1_ConnectionItemRes;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_UE_associatedLogicalE1_ConnectionItemRes_specs_5;
extern asn_TYPE_member_t asn_MBR_E1AP_UE_associatedLogicalE1_ConnectionItemRes_5[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_UE_associatedLogicalE1_ConnectionItemResAck;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_UE_associatedLogicalE1_ConnectionItemResAck_specs_9;
extern asn_TYPE_member_t asn_MBR_E1AP_UE_associatedLogicalE1_ConnectionItemResAck_9[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_System_BearerContextSetupRequest_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_System_BearerContextSetupRequest_ExtIEs_specs_13;
extern asn_TYPE_member_t asn_MBR_E1AP_System_BearerContextSetupRequest_ExtIEs_13[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_System_BearerContextSetupResponse_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_System_BearerContextSetupResponse_ExtIEs_specs_17;
extern asn_TYPE_member_t asn_MBR_E1AP_System_BearerContextSetupResponse_ExtIEs_17[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_System_BearerContextModificationRequest_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_System_BearerContextModificationRequest_ExtIEs_specs_21;
extern asn_TYPE_member_t asn_MBR_E1AP_System_BearerContextModificationRequest_ExtIEs_21[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_System_BearerContextModificationResponse_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_System_BearerContextModificationResponse_ExtIEs_specs_25;
extern asn_TYPE_member_t asn_MBR_E1AP_System_BearerContextModificationResponse_ExtIEs_25[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_System_BearerContextModificationRequired_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_System_BearerContextModificationRequired_ExtIEs_specs_29;
extern asn_TYPE_member_t asn_MBR_E1AP_System_BearerContextModificationRequired_ExtIEs_29[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_System_BearerContextModificationConfirm_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_System_BearerContextModificationConfirm_ExtIEs_specs_33;
extern asn_TYPE_member_t asn_MBR_E1AP_System_BearerContextModificationConfirm_ExtIEs_33[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_System_GNB_CU_UP_CounterCheckRequest_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_System_GNB_CU_UP_CounterCheckRequest_ExtIEs_specs_37;
extern asn_TYPE_member_t asn_MBR_E1AP_System_GNB_CU_UP_CounterCheckRequest_ExtIEs_37[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ActivityInformation_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_ActivityInformation_ExtIEs_specs_41;
extern asn_TYPE_member_t asn_MBR_E1AP_ActivityInformation_ExtIEs_41[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_Cause_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_Cause_ExtIEs_specs_45;
extern asn_TYPE_member_t asn_MBR_E1AP_Cause_ExtIEs_45[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_CP_TNL_Information_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_CP_TNL_Information_ExtIEs_specs_49;
extern asn_TYPE_member_t asn_MBR_E1AP_CP_TNL_Information_ExtIEs_49[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_EarlyForwardingCOUNTInfo_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_EarlyForwardingCOUNTInfo_ExtIEs_specs_53;
extern asn_TYPE_member_t asn_MBR_E1AP_EarlyForwardingCOUNTInfo_ExtIEs_53[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_MDTMode_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_MDTMode_ExtIEs_specs_57;
extern asn_TYPE_member_t asn_MBR_E1AP_MDTMode_ExtIEs_57[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_NPNSupportInfo_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_NPNSupportInfo_ExtIEs_specs_61;
extern asn_TYPE_member_t asn_MBR_E1AP_NPNSupportInfo_ExtIEs_61[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_NPNContextInfo_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_NPNContextInfo_ExtIEs_specs_65;
extern asn_TYPE_member_t asn_MBR_E1AP_NPNContextInfo_ExtIEs_65[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_QoS_Characteristics_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_QoS_Characteristics_ExtIEs_specs_69;
extern asn_TYPE_member_t asn_MBR_E1AP_QoS_Characteristics_ExtIEs_69[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ROHC_Parameters_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_ROHC_Parameters_ExtIEs_specs_73;
extern asn_TYPE_member_t asn_MBR_E1AP_ROHC_Parameters_ExtIEs_73[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_UP_TNL_Information_ExtIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_UP_TNL_Information_ExtIEs_specs_77;
extern asn_TYPE_member_t asn_MBR_E1AP_UP_TNL_Information_ExtIEs_77[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ResetIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_ResetIEs_specs_81;
extern asn_TYPE_member_t asn_MBR_E1AP_ResetIEs_81[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ResetAcknowledgeIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_ResetAcknowledgeIEs_specs_85;
extern asn_TYPE_member_t asn_MBR_E1AP_ResetAcknowledgeIEs_85[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ErrorIndication_IEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_ErrorIndication_IEs_specs_89;
extern asn_TYPE_member_t asn_MBR_E1AP_ErrorIndication_IEs_89[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GNB_CU_UP_E1SetupRequestIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GNB_CU_UP_E1SetupRequestIEs_specs_93;
extern asn_TYPE_member_t asn_MBR_E1AP_GNB_CU_UP_E1SetupRequestIEs_93[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GNB_CU_UP_E1SetupResponseIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GNB_CU_UP_E1SetupResponseIEs_specs_97;
extern asn_TYPE_member_t asn_MBR_E1AP_GNB_CU_UP_E1SetupResponseIEs_97[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GNB_CU_UP_E1SetupFailureIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GNB_CU_UP_E1SetupFailureIEs_specs_101;
extern asn_TYPE_member_t asn_MBR_E1AP_GNB_CU_UP_E1SetupFailureIEs_101[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GNB_CU_CP_E1SetupRequestIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GNB_CU_CP_E1SetupRequestIEs_specs_105;
extern asn_TYPE_member_t asn_MBR_E1AP_GNB_CU_CP_E1SetupRequestIEs_105[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GNB_CU_CP_E1SetupResponseIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GNB_CU_CP_E1SetupResponseIEs_specs_109;
extern asn_TYPE_member_t asn_MBR_E1AP_GNB_CU_CP_E1SetupResponseIEs_109[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GNB_CU_CP_E1SetupFailureIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GNB_CU_CP_E1SetupFailureIEs_specs_113;
extern asn_TYPE_member_t asn_MBR_E1AP_GNB_CU_CP_E1SetupFailureIEs_113[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GNB_CU_UP_ConfigurationUpdateIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GNB_CU_UP_ConfigurationUpdateIEs_specs_117;
extern asn_TYPE_member_t asn_MBR_E1AP_GNB_CU_UP_ConfigurationUpdateIEs_117[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GNB_CU_UP_ConfigurationUpdateAcknowledgeIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GNB_CU_UP_ConfigurationUpdateAcknowledgeIEs_specs_121;
extern asn_TYPE_member_t asn_MBR_E1AP_GNB_CU_UP_ConfigurationUpdateAcknowledgeIEs_121[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GNB_CU_UP_ConfigurationUpdateFailureIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GNB_CU_UP_ConfigurationUpdateFailureIEs_specs_125;
extern asn_TYPE_member_t asn_MBR_E1AP_GNB_CU_UP_ConfigurationUpdateFailureIEs_125[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GNB_CU_CP_ConfigurationUpdateIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GNB_CU_CP_ConfigurationUpdateIEs_specs_129;
extern asn_TYPE_member_t asn_MBR_E1AP_GNB_CU_CP_ConfigurationUpdateIEs_129[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GNB_CU_CP_ConfigurationUpdateAcknowledgeIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GNB_CU_CP_ConfigurationUpdateAcknowledgeIEs_specs_133;
extern asn_TYPE_member_t asn_MBR_E1AP_GNB_CU_CP_ConfigurationUpdateAcknowledgeIEs_133[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GNB_CU_CP_ConfigurationUpdateFailureIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GNB_CU_CP_ConfigurationUpdateFailureIEs_specs_137;
extern asn_TYPE_member_t asn_MBR_E1AP_GNB_CU_CP_ConfigurationUpdateFailureIEs_137[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_E1ReleaseRequestIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_E1ReleaseRequestIEs_specs_141;
extern asn_TYPE_member_t asn_MBR_E1AP_E1ReleaseRequestIEs_141[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_E1ReleaseResponseIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_E1ReleaseResponseIEs_specs_145;
extern asn_TYPE_member_t asn_MBR_E1AP_E1ReleaseResponseIEs_145[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_BearerContextSetupRequestIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_BearerContextSetupRequestIEs_specs_149;
extern asn_TYPE_member_t asn_MBR_E1AP_BearerContextSetupRequestIEs_149[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_EUTRAN_BearerContextSetupRequest;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_EUTRAN_BearerContextSetupRequest_specs_153;
extern asn_TYPE_member_t asn_MBR_E1AP_EUTRAN_BearerContextSetupRequest_153[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_NG_RAN_BearerContextSetupRequest;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_NG_RAN_BearerContextSetupRequest_specs_157;
extern asn_TYPE_member_t asn_MBR_E1AP_NG_RAN_BearerContextSetupRequest_157[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_BearerContextSetupResponseIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_BearerContextSetupResponseIEs_specs_161;
extern asn_TYPE_member_t asn_MBR_E1AP_BearerContextSetupResponseIEs_161[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_EUTRAN_BearerContextSetupResponse;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_EUTRAN_BearerContextSetupResponse_specs_165;
extern asn_TYPE_member_t asn_MBR_E1AP_EUTRAN_BearerContextSetupResponse_165[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_NG_RAN_BearerContextSetupResponse;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_NG_RAN_BearerContextSetupResponse_specs_169;
extern asn_TYPE_member_t asn_MBR_E1AP_NG_RAN_BearerContextSetupResponse_169[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_BearerContextSetupFailureIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_BearerContextSetupFailureIEs_specs_173;
extern asn_TYPE_member_t asn_MBR_E1AP_BearerContextSetupFailureIEs_173[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_BearerContextModificationRequestIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_BearerContextModificationRequestIEs_specs_177;
extern asn_TYPE_member_t asn_MBR_E1AP_BearerContextModificationRequestIEs_177[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_EUTRAN_BearerContextModificationRequest;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_EUTRAN_BearerContextModificationRequest_specs_181;
extern asn_TYPE_member_t asn_MBR_E1AP_EUTRAN_BearerContextModificationRequest_181[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_NG_RAN_BearerContextModificationRequest;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_NG_RAN_BearerContextModificationRequest_specs_185;
extern asn_TYPE_member_t asn_MBR_E1AP_NG_RAN_BearerContextModificationRequest_185[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_BearerContextModificationResponseIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_BearerContextModificationResponseIEs_specs_189;
extern asn_TYPE_member_t asn_MBR_E1AP_BearerContextModificationResponseIEs_189[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_EUTRAN_BearerContextModificationResponse;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_EUTRAN_BearerContextModificationResponse_specs_193;
extern asn_TYPE_member_t asn_MBR_E1AP_EUTRAN_BearerContextModificationResponse_193[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_NG_RAN_BearerContextModificationResponse;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_NG_RAN_BearerContextModificationResponse_specs_197;
extern asn_TYPE_member_t asn_MBR_E1AP_NG_RAN_BearerContextModificationResponse_197[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_BearerContextModificationFailureIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_BearerContextModificationFailureIEs_specs_201;
extern asn_TYPE_member_t asn_MBR_E1AP_BearerContextModificationFailureIEs_201[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_BearerContextModificationRequiredIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_BearerContextModificationRequiredIEs_specs_205;
extern asn_TYPE_member_t asn_MBR_E1AP_BearerContextModificationRequiredIEs_205[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_EUTRAN_BearerContextModificationRequired;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_EUTRAN_BearerContextModificationRequired_specs_209;
extern asn_TYPE_member_t asn_MBR_E1AP_EUTRAN_BearerContextModificationRequired_209[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_NG_RAN_BearerContextModificationRequired;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_NG_RAN_BearerContextModificationRequired_specs_213;
extern asn_TYPE_member_t asn_MBR_E1AP_NG_RAN_BearerContextModificationRequired_213[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_BearerContextModificationConfirmIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_BearerContextModificationConfirmIEs_specs_217;
extern asn_TYPE_member_t asn_MBR_E1AP_BearerContextModificationConfirmIEs_217[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_EUTRAN_BearerContextModificationConfirm;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_EUTRAN_BearerContextModificationConfirm_specs_221;
extern asn_TYPE_member_t asn_MBR_E1AP_EUTRAN_BearerContextModificationConfirm_221[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_NG_RAN_BearerContextModificationConfirm;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_NG_RAN_BearerContextModificationConfirm_specs_225;
extern asn_TYPE_member_t asn_MBR_E1AP_NG_RAN_BearerContextModificationConfirm_225[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_BearerContextReleaseCommandIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_BearerContextReleaseCommandIEs_specs_229;
extern asn_TYPE_member_t asn_MBR_E1AP_BearerContextReleaseCommandIEs_229[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_BearerContextReleaseCompleteIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_BearerContextReleaseCompleteIEs_specs_233;
extern asn_TYPE_member_t asn_MBR_E1AP_BearerContextReleaseCompleteIEs_233[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_BearerContextReleaseRequestIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_BearerContextReleaseRequestIEs_specs_237;
extern asn_TYPE_member_t asn_MBR_E1AP_BearerContextReleaseRequestIEs_237[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_BearerContextInactivityNotificationIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_BearerContextInactivityNotificationIEs_specs_241;
extern asn_TYPE_member_t asn_MBR_E1AP_BearerContextInactivityNotificationIEs_241[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DLDataNotificationIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DLDataNotificationIEs_specs_245;
extern asn_TYPE_member_t asn_MBR_E1AP_DLDataNotificationIEs_245[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ULDataNotificationIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_ULDataNotificationIEs_specs_249;
extern asn_TYPE_member_t asn_MBR_E1AP_ULDataNotificationIEs_249[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DataUsageReportIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DataUsageReportIEs_specs_253;
extern asn_TYPE_member_t asn_MBR_E1AP_DataUsageReportIEs_253[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GNB_CU_UP_CounterCheckRequestIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GNB_CU_UP_CounterCheckRequestIEs_specs_257;
extern asn_TYPE_member_t asn_MBR_E1AP_GNB_CU_UP_CounterCheckRequestIEs_257[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_EUTRAN_GNB_CU_UP_CounterCheckRequest;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_EUTRAN_GNB_CU_UP_CounterCheckRequest_specs_261;
extern asn_TYPE_member_t asn_MBR_E1AP_EUTRAN_GNB_CU_UP_CounterCheckRequest_261[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_NG_RAN_GNB_CU_UP_CounterCheckRequest;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_NG_RAN_GNB_CU_UP_CounterCheckRequest_specs_265;
extern asn_TYPE_member_t asn_MBR_E1AP_NG_RAN_GNB_CU_UP_CounterCheckRequest_265[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GNB_CU_UP_StatusIndicationIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GNB_CU_UP_StatusIndicationIEs_specs_269;
extern asn_TYPE_member_t asn_MBR_E1AP_GNB_CU_UP_StatusIndicationIEs_269[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_GNB_CU_CPMeasurementResultsInformationIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_GNB_CU_CPMeasurementResultsInformationIEs_specs_273;
extern asn_TYPE_member_t asn_MBR_E1AP_GNB_CU_CPMeasurementResultsInformationIEs_273[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_MRDC_DataUsageReportIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_MRDC_DataUsageReportIEs_specs_277;
extern asn_TYPE_member_t asn_MBR_E1AP_MRDC_DataUsageReportIEs_277[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_TraceStartIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_TraceStartIEs_specs_281;
extern asn_TYPE_member_t asn_MBR_E1AP_TraceStartIEs_281[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_DeactivateTraceIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_DeactivateTraceIEs_specs_285;
extern asn_TYPE_member_t asn_MBR_E1AP_DeactivateTraceIEs_285[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_CellTrafficTraceIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_CellTrafficTraceIEs_specs_289;
extern asn_TYPE_member_t asn_MBR_E1AP_CellTrafficTraceIEs_289[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ResourceStatusRequestIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_ResourceStatusRequestIEs_specs_293;
extern asn_TYPE_member_t asn_MBR_E1AP_ResourceStatusRequestIEs_293[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ResourceStatusResponseIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_ResourceStatusResponseIEs_specs_297;
extern asn_TYPE_member_t asn_MBR_E1AP_ResourceStatusResponseIEs_297[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ResourceStatusFailureIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_ResourceStatusFailureIEs_specs_301;
extern asn_TYPE_member_t asn_MBR_E1AP_ResourceStatusFailureIEs_301[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ResourceStatusUpdateIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_ResourceStatusUpdateIEs_specs_305;
extern asn_TYPE_member_t asn_MBR_E1AP_ResourceStatusUpdateIEs_305[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_IAB_UPTNLAddressUpdateIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_IAB_UPTNLAddressUpdateIEs_specs_309;
extern asn_TYPE_member_t asn_MBR_E1AP_IAB_UPTNLAddressUpdateIEs_309[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_IAB_UPTNLAddressUpdateAcknowledgeIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_IAB_UPTNLAddressUpdateAcknowledgeIEs_specs_313;
extern asn_TYPE_member_t asn_MBR_E1AP_IAB_UPTNLAddressUpdateAcknowledgeIEs_313[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_IAB_UPTNLAddressUpdateFailureIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_IAB_UPTNLAddressUpdateFailureIEs_specs_317;
extern asn_TYPE_member_t asn_MBR_E1AP_IAB_UPTNLAddressUpdateFailureIEs_317[3];
extern asn_TYPE_descriptor_t asn_DEF_E1AP_EarlyForwardingSNTransferIEs;
extern asn_SEQUENCE_specifics_t asn_SPC_E1AP_EarlyForwardingSNTransferIEs_specs_321;
extern asn_TYPE_member_t asn_MBR_E1AP_EarlyForwardingSNTransferIEs_321[3];

#ifdef __cplusplus
}
#endif

#endif	/* _E1AP_ProtocolIE_Field_H_ */
#include <asn_internal.h>
