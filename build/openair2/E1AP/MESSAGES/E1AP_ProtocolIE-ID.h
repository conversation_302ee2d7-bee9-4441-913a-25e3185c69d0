/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-CommonDataTypes"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#ifndef	_E1AP_ProtocolIE_ID_H_
#define	_E1AP_ProtocolIE_ID_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>

#ifdef __cplusplus
extern "C" {
#endif

/* E1AP_ProtocolIE-ID */
typedef long	 E1AP_ProtocolIE_ID_t;

/* Implementation */
extern asn_per_constraints_t asn_PER_type_E1AP_ProtocolIE_ID_constr_1;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_ID;
asn_struct_free_f E1AP_ProtocolIE_ID_free;
asn_struct_print_f E1AP_ProtocolIE_ID_print;
asn_constr_check_f E1AP_ProtocolIE_ID_constraint;
xer_type_decoder_f E1AP_ProtocolIE_ID_decode_xer;
xer_type_encoder_f E1AP_ProtocolIE_ID_encode_xer;
per_type_decoder_f E1AP_ProtocolIE_ID_decode_uper;
per_type_encoder_f E1AP_ProtocolIE_ID_encode_uper;
per_type_decoder_f E1AP_ProtocolIE_ID_decode_aper;
per_type_encoder_f E1AP_ProtocolIE_ID_encode_aper;
#define E1AP_ProtocolIE_ID_id_Cause	((E1AP_ProtocolIE_ID_t)0)
#define E1AP_ProtocolIE_ID_id_CriticalityDiagnostics	((E1AP_ProtocolIE_ID_t)1)
#define E1AP_ProtocolIE_ID_id_gNB_CU_CP_UE_E1AP_ID	((E1AP_ProtocolIE_ID_t)2)
#define E1AP_ProtocolIE_ID_id_gNB_CU_UP_UE_E1AP_ID	((E1AP_ProtocolIE_ID_t)3)
#define E1AP_ProtocolIE_ID_id_ResetType	((E1AP_ProtocolIE_ID_t)4)
#define E1AP_ProtocolIE_ID_id_UE_associatedLogicalE1_ConnectionItem	((E1AP_ProtocolIE_ID_t)5)
#define E1AP_ProtocolIE_ID_id_UE_associatedLogicalE1_ConnectionListResAck	((E1AP_ProtocolIE_ID_t)6)
#define E1AP_ProtocolIE_ID_id_gNB_CU_UP_ID	((E1AP_ProtocolIE_ID_t)7)
#define E1AP_ProtocolIE_ID_id_gNB_CU_UP_Name	((E1AP_ProtocolIE_ID_t)8)
#define E1AP_ProtocolIE_ID_id_gNB_CU_CP_Name	((E1AP_ProtocolIE_ID_t)9)
#define E1AP_ProtocolIE_ID_id_CNSupport	((E1AP_ProtocolIE_ID_t)10)
#define E1AP_ProtocolIE_ID_id_SupportedPLMNs	((E1AP_ProtocolIE_ID_t)11)
#define E1AP_ProtocolIE_ID_id_TimeToWait	((E1AP_ProtocolIE_ID_t)12)
#define E1AP_ProtocolIE_ID_id_SecurityInformation	((E1AP_ProtocolIE_ID_t)13)
#define E1AP_ProtocolIE_ID_id_UEDLAggregateMaximumBitRate	((E1AP_ProtocolIE_ID_t)14)
#define E1AP_ProtocolIE_ID_id_System_BearerContextSetupRequest	((E1AP_ProtocolIE_ID_t)15)
#define E1AP_ProtocolIE_ID_id_System_BearerContextSetupResponse	((E1AP_ProtocolIE_ID_t)16)
#define E1AP_ProtocolIE_ID_id_BearerContextStatusChange	((E1AP_ProtocolIE_ID_t)17)
#define E1AP_ProtocolIE_ID_id_System_BearerContextModificationRequest	((E1AP_ProtocolIE_ID_t)18)
#define E1AP_ProtocolIE_ID_id_System_BearerContextModificationResponse	((E1AP_ProtocolIE_ID_t)19)
#define E1AP_ProtocolIE_ID_id_System_BearerContextModificationConfirm	((E1AP_ProtocolIE_ID_t)20)
#define E1AP_ProtocolIE_ID_id_System_BearerContextModificationRequired	((E1AP_ProtocolIE_ID_t)21)
#define E1AP_ProtocolIE_ID_id_DRB_Status_List	((E1AP_ProtocolIE_ID_t)22)
#define E1AP_ProtocolIE_ID_id_ActivityNotificationLevel	((E1AP_ProtocolIE_ID_t)23)
#define E1AP_ProtocolIE_ID_id_ActivityInformation	((E1AP_ProtocolIE_ID_t)24)
#define E1AP_ProtocolIE_ID_id_Data_Usage_Report_List	((E1AP_ProtocolIE_ID_t)25)
#define E1AP_ProtocolIE_ID_id_New_UL_TNL_Information_Required	((E1AP_ProtocolIE_ID_t)26)
#define E1AP_ProtocolIE_ID_id_GNB_CU_CP_TNLA_To_Add_List	((E1AP_ProtocolIE_ID_t)27)
#define E1AP_ProtocolIE_ID_id_GNB_CU_CP_TNLA_To_Remove_List	((E1AP_ProtocolIE_ID_t)28)
#define E1AP_ProtocolIE_ID_id_GNB_CU_CP_TNLA_To_Update_List	((E1AP_ProtocolIE_ID_t)29)
#define E1AP_ProtocolIE_ID_id_GNB_CU_CP_TNLA_Setup_List	((E1AP_ProtocolIE_ID_t)30)
#define E1AP_ProtocolIE_ID_id_GNB_CU_CP_TNLA_Failed_To_Setup_List	((E1AP_ProtocolIE_ID_t)31)
#define E1AP_ProtocolIE_ID_id_DRB_To_Setup_List_EUTRAN	((E1AP_ProtocolIE_ID_t)32)
#define E1AP_ProtocolIE_ID_id_DRB_To_Modify_List_EUTRAN	((E1AP_ProtocolIE_ID_t)33)
#define E1AP_ProtocolIE_ID_id_DRB_To_Remove_List_EUTRAN	((E1AP_ProtocolIE_ID_t)34)
#define E1AP_ProtocolIE_ID_id_DRB_Required_To_Modify_List_EUTRAN	((E1AP_ProtocolIE_ID_t)35)
#define E1AP_ProtocolIE_ID_id_DRB_Required_To_Remove_List_EUTRAN	((E1AP_ProtocolIE_ID_t)36)
#define E1AP_ProtocolIE_ID_id_DRB_Setup_List_EUTRAN	((E1AP_ProtocolIE_ID_t)37)
#define E1AP_ProtocolIE_ID_id_DRB_Failed_List_EUTRAN	((E1AP_ProtocolIE_ID_t)38)
#define E1AP_ProtocolIE_ID_id_DRB_Modified_List_EUTRAN	((E1AP_ProtocolIE_ID_t)39)
#define E1AP_ProtocolIE_ID_id_DRB_Failed_To_Modify_List_EUTRAN	((E1AP_ProtocolIE_ID_t)40)
#define E1AP_ProtocolIE_ID_id_DRB_Confirm_Modified_List_EUTRAN	((E1AP_ProtocolIE_ID_t)41)
#define E1AP_ProtocolIE_ID_id_PDU_Session_Resource_To_Setup_List	((E1AP_ProtocolIE_ID_t)42)
#define E1AP_ProtocolIE_ID_id_PDU_Session_Resource_To_Modify_List	((E1AP_ProtocolIE_ID_t)43)
#define E1AP_ProtocolIE_ID_id_PDU_Session_Resource_To_Remove_List	((E1AP_ProtocolIE_ID_t)44)
#define E1AP_ProtocolIE_ID_id_PDU_Session_Resource_Required_To_Modify_List	((E1AP_ProtocolIE_ID_t)45)
#define E1AP_ProtocolIE_ID_id_PDU_Session_Resource_Setup_List	((E1AP_ProtocolIE_ID_t)46)
#define E1AP_ProtocolIE_ID_id_PDU_Session_Resource_Failed_List	((E1AP_ProtocolIE_ID_t)47)
#define E1AP_ProtocolIE_ID_id_PDU_Session_Resource_Modified_List	((E1AP_ProtocolIE_ID_t)48)
#define E1AP_ProtocolIE_ID_id_PDU_Session_Resource_Failed_To_Modify_List	((E1AP_ProtocolIE_ID_t)49)
#define E1AP_ProtocolIE_ID_id_PDU_Session_Resource_Confirm_Modified_List	((E1AP_ProtocolIE_ID_t)50)
#define E1AP_ProtocolIE_ID_id_DRB_To_Setup_Mod_List_EUTRAN	((E1AP_ProtocolIE_ID_t)51)
#define E1AP_ProtocolIE_ID_id_DRB_Setup_Mod_List_EUTRAN	((E1AP_ProtocolIE_ID_t)52)
#define E1AP_ProtocolIE_ID_id_DRB_Failed_Mod_List_EUTRAN	((E1AP_ProtocolIE_ID_t)53)
#define E1AP_ProtocolIE_ID_id_PDU_Session_Resource_Setup_Mod_List	((E1AP_ProtocolIE_ID_t)54)
#define E1AP_ProtocolIE_ID_id_PDU_Session_Resource_Failed_Mod_List	((E1AP_ProtocolIE_ID_t)55)
#define E1AP_ProtocolIE_ID_id_PDU_Session_Resource_To_Setup_Mod_List	((E1AP_ProtocolIE_ID_t)56)
#define E1AP_ProtocolIE_ID_id_TransactionID	((E1AP_ProtocolIE_ID_t)57)
#define E1AP_ProtocolIE_ID_id_Serving_PLMN	((E1AP_ProtocolIE_ID_t)58)
#define E1AP_ProtocolIE_ID_id_UE_Inactivity_Timer	((E1AP_ProtocolIE_ID_t)59)
#define E1AP_ProtocolIE_ID_id_System_GNB_CU_UP_CounterCheckRequest	((E1AP_ProtocolIE_ID_t)60)
#define E1AP_ProtocolIE_ID_id_DRBs_Subject_To_Counter_Check_List_EUTRAN	((E1AP_ProtocolIE_ID_t)61)
#define E1AP_ProtocolIE_ID_id_DRBs_Subject_To_Counter_Check_List_NG_RAN	((E1AP_ProtocolIE_ID_t)62)
#define E1AP_ProtocolIE_ID_id_PPI	((E1AP_ProtocolIE_ID_t)63)
#define E1AP_ProtocolIE_ID_id_gNB_CU_UP_Capacity	((E1AP_ProtocolIE_ID_t)64)
#define E1AP_ProtocolIE_ID_id_GNB_CU_UP_OverloadInformation	((E1AP_ProtocolIE_ID_t)65)
#define E1AP_ProtocolIE_ID_id_UEDLMaximumIntegrityProtectedDataRate	((E1AP_ProtocolIE_ID_t)66)
#define E1AP_ProtocolIE_ID_id_PDU_Session_To_Notify_List	((E1AP_ProtocolIE_ID_t)67)
#define E1AP_ProtocolIE_ID_id_PDU_Session_Resource_Data_Usage_List	((E1AP_ProtocolIE_ID_t)68)
#define E1AP_ProtocolIE_ID_id_SNSSAI	((E1AP_ProtocolIE_ID_t)69)
#define E1AP_ProtocolIE_ID_id_DataDiscardRequired	((E1AP_ProtocolIE_ID_t)70)
#define E1AP_ProtocolIE_ID_id_OldQoSFlowMap_ULendmarkerexpected	((E1AP_ProtocolIE_ID_t)71)
#define E1AP_ProtocolIE_ID_id_DRB_QoS	((E1AP_ProtocolIE_ID_t)72)
#define E1AP_ProtocolIE_ID_id_GNB_CU_UP_TNLA_To_Remove_List	((E1AP_ProtocolIE_ID_t)73)
#define E1AP_ProtocolIE_ID_id_endpoint_IP_Address_and_Port	((E1AP_ProtocolIE_ID_t)74)
#define E1AP_ProtocolIE_ID_id_TNLAssociationTransportLayerAddressgNBCUUP	((E1AP_ProtocolIE_ID_t)75)
#define E1AP_ProtocolIE_ID_id_RANUEID	((E1AP_ProtocolIE_ID_t)76)
#define E1AP_ProtocolIE_ID_id_GNB_DU_ID	((E1AP_ProtocolIE_ID_t)77)
#define E1AP_ProtocolIE_ID_id_CommonNetworkInstance	((E1AP_ProtocolIE_ID_t)78)
#define E1AP_ProtocolIE_ID_id_NetworkInstance	((E1AP_ProtocolIE_ID_t)79)
#define E1AP_ProtocolIE_ID_id_QoSFlowMappingIndication	((E1AP_ProtocolIE_ID_t)80)
#define E1AP_ProtocolIE_ID_id_TraceActivation	((E1AP_ProtocolIE_ID_t)81)
#define E1AP_ProtocolIE_ID_id_TraceID	((E1AP_ProtocolIE_ID_t)82)
#define E1AP_ProtocolIE_ID_id_SubscriberProfileIDforRFP	((E1AP_ProtocolIE_ID_t)83)
#define E1AP_ProtocolIE_ID_id_AdditionalRRMPriorityIndex	((E1AP_ProtocolIE_ID_t)84)
#define E1AP_ProtocolIE_ID_id_RetainabilityMeasurementsInfo	((E1AP_ProtocolIE_ID_t)85)
#define E1AP_ProtocolIE_ID_id_Transport_Layer_Address_Info	((E1AP_ProtocolIE_ID_t)86)
#define E1AP_ProtocolIE_ID_id_QoSMonitoringRequest	((E1AP_ProtocolIE_ID_t)87)
#define E1AP_ProtocolIE_ID_id_PDCP_StatusReportIndication	((E1AP_ProtocolIE_ID_t)88)
#define E1AP_ProtocolIE_ID_id_gNB_CU_CP_Measurement_ID	((E1AP_ProtocolIE_ID_t)89)
#define E1AP_ProtocolIE_ID_id_gNB_CU_UP_Measurement_ID	((E1AP_ProtocolIE_ID_t)90)
#define E1AP_ProtocolIE_ID_id_RegistrationRequest	((E1AP_ProtocolIE_ID_t)91)
#define E1AP_ProtocolIE_ID_id_ReportCharacteristics	((E1AP_ProtocolIE_ID_t)92)
#define E1AP_ProtocolIE_ID_id_ReportingPeriodicity	((E1AP_ProtocolIE_ID_t)93)
#define E1AP_ProtocolIE_ID_id_TNL_AvailableCapacityIndicator	((E1AP_ProtocolIE_ID_t)94)
#define E1AP_ProtocolIE_ID_id_HW_CapacityIndicator	((E1AP_ProtocolIE_ID_t)95)
#define E1AP_ProtocolIE_ID_id_RedundantCommonNetworkInstance	((E1AP_ProtocolIE_ID_t)96)
#define E1AP_ProtocolIE_ID_id_redundant_nG_UL_UP_TNL_Information	((E1AP_ProtocolIE_ID_t)97)
#define E1AP_ProtocolIE_ID_id_redundant_nG_DL_UP_TNL_Information	((E1AP_ProtocolIE_ID_t)98)
#define E1AP_ProtocolIE_ID_id_RedundantQosFlowIndicator	((E1AP_ProtocolIE_ID_t)99)
#define E1AP_ProtocolIE_ID_id_TSCTrafficCharacteristics	((E1AP_ProtocolIE_ID_t)100)
#define E1AP_ProtocolIE_ID_id_CNPacketDelayBudgetDownlink	((E1AP_ProtocolIE_ID_t)101)
#define E1AP_ProtocolIE_ID_id_CNPacketDelayBudgetUplink	((E1AP_ProtocolIE_ID_t)102)
#define E1AP_ProtocolIE_ID_id_ExtendedPacketDelayBudget	((E1AP_ProtocolIE_ID_t)103)
#define E1AP_ProtocolIE_ID_id_AdditionalPDCPduplicationInformation	((E1AP_ProtocolIE_ID_t)104)
#define E1AP_ProtocolIE_ID_id_RedundantPDUSessionInformation	((E1AP_ProtocolIE_ID_t)105)
#define E1AP_ProtocolIE_ID_id_RedundantPDUSessionInformation_used	((E1AP_ProtocolIE_ID_t)106)
#define E1AP_ProtocolIE_ID_id_QoS_Mapping_Information	((E1AP_ProtocolIE_ID_t)107)
#define E1AP_ProtocolIE_ID_id_DLUPTNLAddressToUpdateList	((E1AP_ProtocolIE_ID_t)108)
#define E1AP_ProtocolIE_ID_id_ULUPTNLAddressToUpdateList	((E1AP_ProtocolIE_ID_t)109)
#define E1AP_ProtocolIE_ID_id_NPNSupportInfo	((E1AP_ProtocolIE_ID_t)110)
#define E1AP_ProtocolIE_ID_id_NPNContextInfo	((E1AP_ProtocolIE_ID_t)111)
#define E1AP_ProtocolIE_ID_id_MDTConfiguration	((E1AP_ProtocolIE_ID_t)112)
#define E1AP_ProtocolIE_ID_id_ManagementBasedMDTPLMNList	((E1AP_ProtocolIE_ID_t)113)
#define E1AP_ProtocolIE_ID_id_TraceCollectionEntityIPAddress	((E1AP_ProtocolIE_ID_t)114)
#define E1AP_ProtocolIE_ID_id_PrivacyIndicator	((E1AP_ProtocolIE_ID_t)115)
#define E1AP_ProtocolIE_ID_id_TraceCollectionEntityURI	((E1AP_ProtocolIE_ID_t)116)
#define E1AP_ProtocolIE_ID_id_URIaddress	((E1AP_ProtocolIE_ID_t)117)
#define E1AP_ProtocolIE_ID_id_EHC_Parameters	((E1AP_ProtocolIE_ID_t)118)
#define E1AP_ProtocolIE_ID_id_DRBs_Subject_To_Early_Forwarding_List	((E1AP_ProtocolIE_ID_t)119)
#define E1AP_ProtocolIE_ID_id_DAPSRequestInfo	((E1AP_ProtocolIE_ID_t)120)
#define E1AP_ProtocolIE_ID_id_CHOInitiation	((E1AP_ProtocolIE_ID_t)121)
#define E1AP_ProtocolIE_ID_id_EarlyForwardingCOUNTReq	((E1AP_ProtocolIE_ID_t)122)
#define E1AP_ProtocolIE_ID_id_EarlyForwardingCOUNTInfo	((E1AP_ProtocolIE_ID_t)123)
#define E1AP_ProtocolIE_ID_id_AlternativeQoSParaSetList	((E1AP_ProtocolIE_ID_t)124)
#define E1AP_ProtocolIE_ID_id_ExtendedSliceSupportList	((E1AP_ProtocolIE_ID_t)125)
#define E1AP_ProtocolIE_ID_id_MCG_OfferedGBRQoSFlowInfo	((E1AP_ProtocolIE_ID_t)126)
#define E1AP_ProtocolIE_ID_id_Number_of_tunnels	((E1AP_ProtocolIE_ID_t)127)
#define E1AP_ProtocolIE_ID_id_DRB_Measurement_Results_Information_List	((E1AP_ProtocolIE_ID_t)128)
#define E1AP_ProtocolIE_ID_id_Extended_GNB_CU_CP_Name	((E1AP_ProtocolIE_ID_t)129)
#define E1AP_ProtocolIE_ID_id_Extended_GNB_CU_UP_Name	((E1AP_ProtocolIE_ID_t)130)
#define E1AP_ProtocolIE_ID_id_DataForwardingtoE_UTRANInformationList	((E1AP_ProtocolIE_ID_t)131)
#define E1AP_ProtocolIE_ID_id_QosMonitoringReportingFrequency	((E1AP_ProtocolIE_ID_t)132)
#define E1AP_ProtocolIE_ID_id_QoSMonitoringDisabled	((E1AP_ProtocolIE_ID_t)133)
#define E1AP_ProtocolIE_ID_id_AdditionalHandoverInfo	((E1AP_ProtocolIE_ID_t)134)
#define E1AP_ProtocolIE_ID_id_Extended_NR_CGI_Support_List	((E1AP_ProtocolIE_ID_t)135)
#define E1AP_ProtocolIE_ID_id_DataForwardingtoNG_RANQoSFlowInformationList	((E1AP_ProtocolIE_ID_t)136)
#define E1AP_ProtocolIE_ID_id_MaxCIDEHCDL	((E1AP_ProtocolIE_ID_t)137)
#define E1AP_ProtocolIE_ID_id_ignoreMappingRuleIndication	((E1AP_ProtocolIE_ID_t)138)
#define E1AP_ProtocolIE_ID_id_DirectForwardingPathAvailability	((E1AP_ProtocolIE_ID_t)139)
#define E1AP_ProtocolIE_ID_id_EarlyDataForwardingIndicator	((E1AP_ProtocolIE_ID_t)140)
#define E1AP_ProtocolIE_ID_id_QoSFlowsDRBRemapping	((E1AP_ProtocolIE_ID_t)141)

#ifdef __cplusplus
}
#endif

#endif	/* _E1AP_ProtocolIE_ID_H_ */
#include <asn_internal.h>
