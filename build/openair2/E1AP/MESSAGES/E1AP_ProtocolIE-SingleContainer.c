/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-Containers"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#include "E1AP_ProtocolIE-SingleContainer.h"

/*
 * This type is implemented using E1AP_ResetType_ExtIEs,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using E1AP_UE_associatedLogicalE1_ConnectionItemRes,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using E1AP_UE_associatedLogicalE1_ConnectionItemResAck,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using E1AP_System_BearerContextSetupRequest_ExtIEs,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using E1AP_System_BearerContextSetupResponse_ExtIEs,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using E1AP_System_BearerContextModificationRequest_ExtIEs,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using E1AP_System_BearerContextModificationResponse_ExtIEs,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using E1AP_System_BearerContextModificationRequired_ExtIEs,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using E1AP_System_BearerContextModificationConfirm_ExtIEs,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using E1AP_System_GNB_CU_UP_CounterCheckRequest_ExtIEs,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using E1AP_ActivityInformation_ExtIEs,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using E1AP_Cause_ExtIEs,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using E1AP_CP_TNL_Information_ExtIEs,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using E1AP_EarlyForwardingCOUNTInfo_ExtIEs,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using E1AP_MDTMode_ExtIEs,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using E1AP_NPNSupportInfo_ExtIEs,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using E1AP_NPNContextInfo_ExtIEs,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using E1AP_QoS_Characteristics_ExtIEs,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using E1AP_ROHC_Parameters_ExtIEs,
 * so here we adjust the DEF accordingly.
 */
/*
 * This type is implemented using E1AP_UP_TNL_Information_ExtIEs,
 * so here we adjust the DEF accordingly.
 */
static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P0_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P0 = {
	"ProtocolIE-SingleContainer",
	"ProtocolIE-SingleContainer",
	&asn_OP_SEQUENCE,
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P0_tags_1,
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P0_tags_1)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P0_tags_1[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P0_tags_1,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P0_tags_1)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P0_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_E1AP_ResetType_ExtIEs_1,
	3,	/* Elements count */
	&asn_SPC_E1AP_ResetType_ExtIEs_specs_1	/* Additional specs */
};

static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P1_tags_2[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P1 = {
	"ProtocolIE-SingleContainer",
	"ProtocolIE-SingleContainer",
	&asn_OP_SEQUENCE,
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P1_tags_2,
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P1_tags_2)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P1_tags_2[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P1_tags_2,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P1_tags_2)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P1_tags_2[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_E1AP_UE_associatedLogicalE1_ConnectionItemRes_5,
	3,	/* Elements count */
	&asn_SPC_E1AP_UE_associatedLogicalE1_ConnectionItemRes_specs_5	/* Additional specs */
};

static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P2_tags_3[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P2 = {
	"ProtocolIE-SingleContainer",
	"ProtocolIE-SingleContainer",
	&asn_OP_SEQUENCE,
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P2_tags_3,
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P2_tags_3)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P2_tags_3[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P2_tags_3,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P2_tags_3)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P2_tags_3[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_E1AP_UE_associatedLogicalE1_ConnectionItemResAck_9,
	3,	/* Elements count */
	&asn_SPC_E1AP_UE_associatedLogicalE1_ConnectionItemResAck_specs_9	/* Additional specs */
};

static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P3_tags_4[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P3 = {
	"ProtocolIE-SingleContainer",
	"ProtocolIE-SingleContainer",
	&asn_OP_SEQUENCE,
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P3_tags_4,
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P3_tags_4)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P3_tags_4[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P3_tags_4,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P3_tags_4)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P3_tags_4[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_E1AP_System_BearerContextSetupRequest_ExtIEs_13,
	3,	/* Elements count */
	&asn_SPC_E1AP_System_BearerContextSetupRequest_ExtIEs_specs_13	/* Additional specs */
};

static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P4_tags_5[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P4 = {
	"ProtocolIE-SingleContainer",
	"ProtocolIE-SingleContainer",
	&asn_OP_SEQUENCE,
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P4_tags_5,
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P4_tags_5)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P4_tags_5[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P4_tags_5,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P4_tags_5)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P4_tags_5[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_E1AP_System_BearerContextSetupResponse_ExtIEs_17,
	3,	/* Elements count */
	&asn_SPC_E1AP_System_BearerContextSetupResponse_ExtIEs_specs_17	/* Additional specs */
};

static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P5_tags_6[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P5 = {
	"ProtocolIE-SingleContainer",
	"ProtocolIE-SingleContainer",
	&asn_OP_SEQUENCE,
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P5_tags_6,
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P5_tags_6)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P5_tags_6[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P5_tags_6,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P5_tags_6)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P5_tags_6[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_E1AP_System_BearerContextModificationRequest_ExtIEs_21,
	3,	/* Elements count */
	&asn_SPC_E1AP_System_BearerContextModificationRequest_ExtIEs_specs_21	/* Additional specs */
};

static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P6_tags_7[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P6 = {
	"ProtocolIE-SingleContainer",
	"ProtocolIE-SingleContainer",
	&asn_OP_SEQUENCE,
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P6_tags_7,
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P6_tags_7)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P6_tags_7[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P6_tags_7,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P6_tags_7)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P6_tags_7[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_E1AP_System_BearerContextModificationResponse_ExtIEs_25,
	3,	/* Elements count */
	&asn_SPC_E1AP_System_BearerContextModificationResponse_ExtIEs_specs_25	/* Additional specs */
};

static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P7_tags_8[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P7 = {
	"ProtocolIE-SingleContainer",
	"ProtocolIE-SingleContainer",
	&asn_OP_SEQUENCE,
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P7_tags_8,
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P7_tags_8)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P7_tags_8[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P7_tags_8,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P7_tags_8)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P7_tags_8[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_E1AP_System_BearerContextModificationRequired_ExtIEs_29,
	3,	/* Elements count */
	&asn_SPC_E1AP_System_BearerContextModificationRequired_ExtIEs_specs_29	/* Additional specs */
};

static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P8_tags_9[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P8 = {
	"ProtocolIE-SingleContainer",
	"ProtocolIE-SingleContainer",
	&asn_OP_SEQUENCE,
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P8_tags_9,
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P8_tags_9)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P8_tags_9[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P8_tags_9,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P8_tags_9)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P8_tags_9[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_E1AP_System_BearerContextModificationConfirm_ExtIEs_33,
	3,	/* Elements count */
	&asn_SPC_E1AP_System_BearerContextModificationConfirm_ExtIEs_specs_33	/* Additional specs */
};

static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P9_tags_10[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P9 = {
	"ProtocolIE-SingleContainer",
	"ProtocolIE-SingleContainer",
	&asn_OP_SEQUENCE,
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P9_tags_10,
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P9_tags_10)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P9_tags_10[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P9_tags_10,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P9_tags_10)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P9_tags_10[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_E1AP_System_GNB_CU_UP_CounterCheckRequest_ExtIEs_37,
	3,	/* Elements count */
	&asn_SPC_E1AP_System_GNB_CU_UP_CounterCheckRequest_ExtIEs_specs_37	/* Additional specs */
};

static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P10_tags_11[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P10 = {
	"ProtocolIE-SingleContainer",
	"ProtocolIE-SingleContainer",
	&asn_OP_SEQUENCE,
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P10_tags_11,
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P10_tags_11)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P10_tags_11[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P10_tags_11,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P10_tags_11)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P10_tags_11[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_E1AP_ActivityInformation_ExtIEs_41,
	3,	/* Elements count */
	&asn_SPC_E1AP_ActivityInformation_ExtIEs_specs_41	/* Additional specs */
};

static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P11_tags_12[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P11 = {
	"ProtocolIE-SingleContainer",
	"ProtocolIE-SingleContainer",
	&asn_OP_SEQUENCE,
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P11_tags_12,
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P11_tags_12)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P11_tags_12[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P11_tags_12,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P11_tags_12)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P11_tags_12[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_E1AP_Cause_ExtIEs_45,
	3,	/* Elements count */
	&asn_SPC_E1AP_Cause_ExtIEs_specs_45	/* Additional specs */
};

static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P12_tags_13[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P12 = {
	"ProtocolIE-SingleContainer",
	"ProtocolIE-SingleContainer",
	&asn_OP_SEQUENCE,
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P12_tags_13,
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P12_tags_13)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P12_tags_13[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P12_tags_13,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P12_tags_13)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P12_tags_13[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_E1AP_CP_TNL_Information_ExtIEs_49,
	3,	/* Elements count */
	&asn_SPC_E1AP_CP_TNL_Information_ExtIEs_specs_49	/* Additional specs */
};

static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P13_tags_14[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P13 = {
	"ProtocolIE-SingleContainer",
	"ProtocolIE-SingleContainer",
	&asn_OP_SEQUENCE,
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P13_tags_14,
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P13_tags_14)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P13_tags_14[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P13_tags_14,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P13_tags_14)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P13_tags_14[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_E1AP_EarlyForwardingCOUNTInfo_ExtIEs_53,
	3,	/* Elements count */
	&asn_SPC_E1AP_EarlyForwardingCOUNTInfo_ExtIEs_specs_53	/* Additional specs */
};

static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P14_tags_15[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P14 = {
	"ProtocolIE-SingleContainer",
	"ProtocolIE-SingleContainer",
	&asn_OP_SEQUENCE,
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P14_tags_15,
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P14_tags_15)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P14_tags_15[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P14_tags_15,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P14_tags_15)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P14_tags_15[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_E1AP_MDTMode_ExtIEs_57,
	3,	/* Elements count */
	&asn_SPC_E1AP_MDTMode_ExtIEs_specs_57	/* Additional specs */
};

static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P15_tags_16[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P15 = {
	"ProtocolIE-SingleContainer",
	"ProtocolIE-SingleContainer",
	&asn_OP_SEQUENCE,
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P15_tags_16,
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P15_tags_16)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P15_tags_16[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P15_tags_16,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P15_tags_16)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P15_tags_16[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_E1AP_NPNSupportInfo_ExtIEs_61,
	3,	/* Elements count */
	&asn_SPC_E1AP_NPNSupportInfo_ExtIEs_specs_61	/* Additional specs */
};

static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P16_tags_17[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P16 = {
	"ProtocolIE-SingleContainer",
	"ProtocolIE-SingleContainer",
	&asn_OP_SEQUENCE,
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P16_tags_17,
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P16_tags_17)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P16_tags_17[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P16_tags_17,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P16_tags_17)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P16_tags_17[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_E1AP_NPNContextInfo_ExtIEs_65,
	3,	/* Elements count */
	&asn_SPC_E1AP_NPNContextInfo_ExtIEs_specs_65	/* Additional specs */
};

static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P17_tags_18[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P17 = {
	"ProtocolIE-SingleContainer",
	"ProtocolIE-SingleContainer",
	&asn_OP_SEQUENCE,
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P17_tags_18,
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P17_tags_18)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P17_tags_18[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P17_tags_18,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P17_tags_18)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P17_tags_18[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_E1AP_QoS_Characteristics_ExtIEs_69,
	3,	/* Elements count */
	&asn_SPC_E1AP_QoS_Characteristics_ExtIEs_specs_69	/* Additional specs */
};

static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P18_tags_19[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P18 = {
	"ProtocolIE-SingleContainer",
	"ProtocolIE-SingleContainer",
	&asn_OP_SEQUENCE,
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P18_tags_19,
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P18_tags_19)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P18_tags_19[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P18_tags_19,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P18_tags_19)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P18_tags_19[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_E1AP_ROHC_Parameters_ExtIEs_73,
	3,	/* Elements count */
	&asn_SPC_E1AP_ROHC_Parameters_ExtIEs_specs_73	/* Additional specs */
};

static const ber_tlv_tag_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P19_tags_20[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P19 = {
	"ProtocolIE-SingleContainer",
	"ProtocolIE-SingleContainer",
	&asn_OP_SEQUENCE,
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P19_tags_20,
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P19_tags_20)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P19_tags_20[0]), /* 1 */
	asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P19_tags_20,	/* Same as above */
	sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P19_tags_20)
		/sizeof(asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P19_tags_20[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_E1AP_UP_TNL_Information_ExtIEs_77,
	3,	/* Elements count */
	&asn_SPC_E1AP_UP_TNL_Information_ExtIEs_specs_77	/* Additional specs */
};

