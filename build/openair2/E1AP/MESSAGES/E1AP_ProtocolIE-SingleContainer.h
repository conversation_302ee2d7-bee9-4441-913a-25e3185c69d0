/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-Containers"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#ifndef	_E1AP_ProtocolIE_SingleContainer_H_
#define	_E1AP_ProtocolIE_SingleContainer_H_


#include <asn_application.h>

/* Including external dependencies */
#include "E1AP_ProtocolIE-Field.h"

#ifdef __cplusplus
extern "C" {
#endif

/* E1AP_ProtocolIE-SingleContainer */
typedef E1AP_ResetType_ExtIEs_t	 E1AP_ProtocolIE_SingleContainer_4935P0_t;
typedef E1AP_UE_associatedLogicalE1_ConnectionItemRes_t	 E1AP_ProtocolIE_SingleContainer_4935P1_t;
typedef E1AP_UE_associatedLogicalE1_ConnectionItemResAck_t	 E1AP_ProtocolIE_SingleContainer_4935P2_t;
typedef E1AP_System_BearerContextSetupRequest_ExtIEs_t	 E1AP_ProtocolIE_SingleContainer_4935P3_t;
typedef E1AP_System_BearerContextSetupResponse_ExtIEs_t	 E1AP_ProtocolIE_SingleContainer_4935P4_t;
typedef E1AP_System_BearerContextModificationRequest_ExtIEs_t	 E1AP_ProtocolIE_SingleContainer_4935P5_t;
typedef E1AP_System_BearerContextModificationResponse_ExtIEs_t	 E1AP_ProtocolIE_SingleContainer_4935P6_t;
typedef E1AP_System_BearerContextModificationRequired_ExtIEs_t	 E1AP_ProtocolIE_SingleContainer_4935P7_t;
typedef E1AP_System_BearerContextModificationConfirm_ExtIEs_t	 E1AP_ProtocolIE_SingleContainer_4935P8_t;
typedef E1AP_System_GNB_CU_UP_CounterCheckRequest_ExtIEs_t	 E1AP_ProtocolIE_SingleContainer_4935P9_t;
typedef E1AP_ActivityInformation_ExtIEs_t	 E1AP_ProtocolIE_SingleContainer_4935P10_t;
typedef E1AP_Cause_ExtIEs_t	 E1AP_ProtocolIE_SingleContainer_4935P11_t;
typedef E1AP_CP_TNL_Information_ExtIEs_t	 E1AP_ProtocolIE_SingleContainer_4935P12_t;
typedef E1AP_EarlyForwardingCOUNTInfo_ExtIEs_t	 E1AP_ProtocolIE_SingleContainer_4935P13_t;
typedef E1AP_MDTMode_ExtIEs_t	 E1AP_ProtocolIE_SingleContainer_4935P14_t;
typedef E1AP_NPNSupportInfo_ExtIEs_t	 E1AP_ProtocolIE_SingleContainer_4935P15_t;
typedef E1AP_NPNContextInfo_ExtIEs_t	 E1AP_ProtocolIE_SingleContainer_4935P16_t;
typedef E1AP_QoS_Characteristics_ExtIEs_t	 E1AP_ProtocolIE_SingleContainer_4935P17_t;
typedef E1AP_ROHC_Parameters_ExtIEs_t	 E1AP_ProtocolIE_SingleContainer_4935P18_t;
typedef E1AP_UP_TNL_Information_ExtIEs_t	 E1AP_ProtocolIE_SingleContainer_4935P19_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P0;
asn_struct_free_f E1AP_ProtocolIE_SingleContainer_4935P0_free;
asn_struct_print_f E1AP_ProtocolIE_SingleContainer_4935P0_print;
asn_constr_check_f E1AP_ProtocolIE_SingleContainer_4935P0_constraint;
xer_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P0_decode_xer;
xer_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P0_encode_xer;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P0_decode_uper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P0_encode_uper;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P0_decode_aper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P0_encode_aper;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P1;
asn_struct_free_f E1AP_ProtocolIE_SingleContainer_4935P1_free;
asn_struct_print_f E1AP_ProtocolIE_SingleContainer_4935P1_print;
asn_constr_check_f E1AP_ProtocolIE_SingleContainer_4935P1_constraint;
xer_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P1_decode_xer;
xer_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P1_encode_xer;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P1_decode_uper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P1_encode_uper;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P1_decode_aper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P1_encode_aper;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P2;
asn_struct_free_f E1AP_ProtocolIE_SingleContainer_4935P2_free;
asn_struct_print_f E1AP_ProtocolIE_SingleContainer_4935P2_print;
asn_constr_check_f E1AP_ProtocolIE_SingleContainer_4935P2_constraint;
xer_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P2_decode_xer;
xer_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P2_encode_xer;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P2_decode_uper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P2_encode_uper;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P2_decode_aper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P2_encode_aper;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P3;
asn_struct_free_f E1AP_ProtocolIE_SingleContainer_4935P3_free;
asn_struct_print_f E1AP_ProtocolIE_SingleContainer_4935P3_print;
asn_constr_check_f E1AP_ProtocolIE_SingleContainer_4935P3_constraint;
xer_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P3_decode_xer;
xer_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P3_encode_xer;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P3_decode_uper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P3_encode_uper;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P3_decode_aper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P3_encode_aper;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P4;
asn_struct_free_f E1AP_ProtocolIE_SingleContainer_4935P4_free;
asn_struct_print_f E1AP_ProtocolIE_SingleContainer_4935P4_print;
asn_constr_check_f E1AP_ProtocolIE_SingleContainer_4935P4_constraint;
xer_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P4_decode_xer;
xer_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P4_encode_xer;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P4_decode_uper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P4_encode_uper;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P4_decode_aper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P4_encode_aper;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P5;
asn_struct_free_f E1AP_ProtocolIE_SingleContainer_4935P5_free;
asn_struct_print_f E1AP_ProtocolIE_SingleContainer_4935P5_print;
asn_constr_check_f E1AP_ProtocolIE_SingleContainer_4935P5_constraint;
xer_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P5_decode_xer;
xer_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P5_encode_xer;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P5_decode_uper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P5_encode_uper;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P5_decode_aper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P5_encode_aper;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P6;
asn_struct_free_f E1AP_ProtocolIE_SingleContainer_4935P6_free;
asn_struct_print_f E1AP_ProtocolIE_SingleContainer_4935P6_print;
asn_constr_check_f E1AP_ProtocolIE_SingleContainer_4935P6_constraint;
xer_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P6_decode_xer;
xer_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P6_encode_xer;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P6_decode_uper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P6_encode_uper;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P6_decode_aper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P6_encode_aper;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P7;
asn_struct_free_f E1AP_ProtocolIE_SingleContainer_4935P7_free;
asn_struct_print_f E1AP_ProtocolIE_SingleContainer_4935P7_print;
asn_constr_check_f E1AP_ProtocolIE_SingleContainer_4935P7_constraint;
xer_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P7_decode_xer;
xer_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P7_encode_xer;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P7_decode_uper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P7_encode_uper;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P7_decode_aper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P7_encode_aper;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P8;
asn_struct_free_f E1AP_ProtocolIE_SingleContainer_4935P8_free;
asn_struct_print_f E1AP_ProtocolIE_SingleContainer_4935P8_print;
asn_constr_check_f E1AP_ProtocolIE_SingleContainer_4935P8_constraint;
xer_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P8_decode_xer;
xer_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P8_encode_xer;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P8_decode_uper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P8_encode_uper;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P8_decode_aper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P8_encode_aper;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P9;
asn_struct_free_f E1AP_ProtocolIE_SingleContainer_4935P9_free;
asn_struct_print_f E1AP_ProtocolIE_SingleContainer_4935P9_print;
asn_constr_check_f E1AP_ProtocolIE_SingleContainer_4935P9_constraint;
xer_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P9_decode_xer;
xer_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P9_encode_xer;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P9_decode_uper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P9_encode_uper;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P9_decode_aper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P9_encode_aper;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P10;
asn_struct_free_f E1AP_ProtocolIE_SingleContainer_4935P10_free;
asn_struct_print_f E1AP_ProtocolIE_SingleContainer_4935P10_print;
asn_constr_check_f E1AP_ProtocolIE_SingleContainer_4935P10_constraint;
xer_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P10_decode_xer;
xer_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P10_encode_xer;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P10_decode_uper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P10_encode_uper;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P10_decode_aper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P10_encode_aper;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P11;
asn_struct_free_f E1AP_ProtocolIE_SingleContainer_4935P11_free;
asn_struct_print_f E1AP_ProtocolIE_SingleContainer_4935P11_print;
asn_constr_check_f E1AP_ProtocolIE_SingleContainer_4935P11_constraint;
xer_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P11_decode_xer;
xer_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P11_encode_xer;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P11_decode_uper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P11_encode_uper;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P11_decode_aper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P11_encode_aper;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P12;
asn_struct_free_f E1AP_ProtocolIE_SingleContainer_4935P12_free;
asn_struct_print_f E1AP_ProtocolIE_SingleContainer_4935P12_print;
asn_constr_check_f E1AP_ProtocolIE_SingleContainer_4935P12_constraint;
xer_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P12_decode_xer;
xer_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P12_encode_xer;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P12_decode_uper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P12_encode_uper;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P12_decode_aper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P12_encode_aper;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P13;
asn_struct_free_f E1AP_ProtocolIE_SingleContainer_4935P13_free;
asn_struct_print_f E1AP_ProtocolIE_SingleContainer_4935P13_print;
asn_constr_check_f E1AP_ProtocolIE_SingleContainer_4935P13_constraint;
xer_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P13_decode_xer;
xer_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P13_encode_xer;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P13_decode_uper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P13_encode_uper;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P13_decode_aper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P13_encode_aper;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P14;
asn_struct_free_f E1AP_ProtocolIE_SingleContainer_4935P14_free;
asn_struct_print_f E1AP_ProtocolIE_SingleContainer_4935P14_print;
asn_constr_check_f E1AP_ProtocolIE_SingleContainer_4935P14_constraint;
xer_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P14_decode_xer;
xer_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P14_encode_xer;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P14_decode_uper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P14_encode_uper;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P14_decode_aper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P14_encode_aper;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P15;
asn_struct_free_f E1AP_ProtocolIE_SingleContainer_4935P15_free;
asn_struct_print_f E1AP_ProtocolIE_SingleContainer_4935P15_print;
asn_constr_check_f E1AP_ProtocolIE_SingleContainer_4935P15_constraint;
xer_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P15_decode_xer;
xer_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P15_encode_xer;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P15_decode_uper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P15_encode_uper;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P15_decode_aper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P15_encode_aper;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P16;
asn_struct_free_f E1AP_ProtocolIE_SingleContainer_4935P16_free;
asn_struct_print_f E1AP_ProtocolIE_SingleContainer_4935P16_print;
asn_constr_check_f E1AP_ProtocolIE_SingleContainer_4935P16_constraint;
xer_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P16_decode_xer;
xer_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P16_encode_xer;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P16_decode_uper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P16_encode_uper;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P16_decode_aper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P16_encode_aper;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P17;
asn_struct_free_f E1AP_ProtocolIE_SingleContainer_4935P17_free;
asn_struct_print_f E1AP_ProtocolIE_SingleContainer_4935P17_print;
asn_constr_check_f E1AP_ProtocolIE_SingleContainer_4935P17_constraint;
xer_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P17_decode_xer;
xer_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P17_encode_xer;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P17_decode_uper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P17_encode_uper;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P17_decode_aper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P17_encode_aper;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P18;
asn_struct_free_f E1AP_ProtocolIE_SingleContainer_4935P18_free;
asn_struct_print_f E1AP_ProtocolIE_SingleContainer_4935P18_print;
asn_constr_check_f E1AP_ProtocolIE_SingleContainer_4935P18_constraint;
xer_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P18_decode_xer;
xer_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P18_encode_xer;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P18_decode_uper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P18_encode_uper;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P18_decode_aper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P18_encode_aper;
extern asn_TYPE_descriptor_t asn_DEF_E1AP_ProtocolIE_SingleContainer_4935P19;
asn_struct_free_f E1AP_ProtocolIE_SingleContainer_4935P19_free;
asn_struct_print_f E1AP_ProtocolIE_SingleContainer_4935P19_print;
asn_constr_check_f E1AP_ProtocolIE_SingleContainer_4935P19_constraint;
xer_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P19_decode_xer;
xer_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P19_encode_xer;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P19_decode_uper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P19_encode_uper;
per_type_decoder_f E1AP_ProtocolIE_SingleContainer_4935P19_decode_aper;
per_type_encoder_f E1AP_ProtocolIE_SingleContainer_4935P19_encode_aper;

#ifdef __cplusplus
}
#endif

#endif	/* _E1AP_ProtocolIE_SingleContainer_H_ */
#include <asn_internal.h>
