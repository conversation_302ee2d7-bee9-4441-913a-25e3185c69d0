/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-IEs"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#include "E1AP_QoS-Flow-Removed-Item.h"

#include "E1AP_ProtocolExtensionContainer.h"
/*
 * This type is implemented using NativeEnumerated,
 * so here we adjust the DEF accordingly.
 */
static int
memb_E1AP_qoS_Flow_Accumulated_Session_Time_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	const OCTET_STRING_t *st = (const OCTET_STRING_t *)sptr;
	size_t size;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	size = st->size;
	
	if((size == 5UL)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_type_E1AP_qoS_Flow_Released_In_Session_constr_3 CC_NOTUSED = {
	{ APC_CONSTRAINED | APC_EXTENSIBLE,  1,  1,  0,  1 }	/* (0..1,...) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_E1AP_qoS_Flow_Accumulated_Session_Time_constr_7 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_CONSTRAINED,	 0,  0,  5,  5 }	/* (SIZE(5..5)) */,
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static const asn_INTEGER_enum_map_t asn_MAP_E1AP_qoS_Flow_Released_In_Session_value2enum_3[] = {
	{ 0,	19,	"released-in-session" },
	{ 1,	23,	"not-released-in-session" }
	/* This list is extensible */
};
static const unsigned int asn_MAP_E1AP_qoS_Flow_Released_In_Session_enum2value_3[] = {
	1,	/* not-released-in-session(1) */
	0	/* released-in-session(0) */
	/* This list is extensible */
};
static const asn_INTEGER_specifics_t asn_SPC_E1AP_qoS_Flow_Released_In_Session_specs_3 = {
	asn_MAP_E1AP_qoS_Flow_Released_In_Session_value2enum_3,	/* "tag" => N; sorted by tag */
	asn_MAP_E1AP_qoS_Flow_Released_In_Session_enum2value_3,	/* N => "tag"; sorted by N */
	2,	/* Number of elements in the maps */
	3,	/* Extensions before this member */
	1,	/* Strict enumeration */
	0,	/* Native long size */
	0
};
static const ber_tlv_tag_t asn_DEF_E1AP_qoS_Flow_Released_In_Session_tags_3[] = {
	(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
	(ASN_TAG_CLASS_UNIVERSAL | (10 << 2))
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_E1AP_qoS_Flow_Released_In_Session_3 = {
	"qoS-Flow-Released-In-Session",
	"qoS-Flow-Released-In-Session",
	&asn_OP_NativeEnumerated,
	asn_DEF_E1AP_qoS_Flow_Released_In_Session_tags_3,
	sizeof(asn_DEF_E1AP_qoS_Flow_Released_In_Session_tags_3)
		/sizeof(asn_DEF_E1AP_qoS_Flow_Released_In_Session_tags_3[0]) - 1, /* 1 */
	asn_DEF_E1AP_qoS_Flow_Released_In_Session_tags_3,	/* Same as above */
	sizeof(asn_DEF_E1AP_qoS_Flow_Released_In_Session_tags_3)
		/sizeof(asn_DEF_E1AP_qoS_Flow_Released_In_Session_tags_3[0]), /* 2 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		&asn_PER_type_E1AP_qoS_Flow_Released_In_Session_constr_3,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		NativeEnumerated_constraint
	},
	0, 0,	/* Defined elsewhere */
	&asn_SPC_E1AP_qoS_Flow_Released_In_Session_specs_3	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_QoS_Flow_Removed_Item_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct E1AP_QoS_Flow_Removed_Item, qoS_Flow_Identifier),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_E1AP_QoS_Flow_Identifier,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"qoS-Flow-Identifier"
		},
	{ ATF_POINTER, 3, offsetof(struct E1AP_QoS_Flow_Removed_Item, qoS_Flow_Released_In_Session),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_E1AP_qoS_Flow_Released_In_Session_3,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"qoS-Flow-Released-In-Session"
		},
	{ ATF_POINTER, 2, offsetof(struct E1AP_QoS_Flow_Removed_Item, qoS_Flow_Accumulated_Session_Time),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_OCTET_STRING,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_E1AP_qoS_Flow_Accumulated_Session_Time_constr_7,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_E1AP_qoS_Flow_Accumulated_Session_Time_constraint_1
		},
		0, 0, /* No default value */
		"qoS-Flow-Accumulated-Session-Time"
		},
	{ ATF_POINTER, 1, offsetof(struct E1AP_QoS_Flow_Removed_Item, iE_Extensions),
		(ASN_TAG_CLASS_CONTEXT | (3 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_E1AP_ProtocolExtensionContainer_4961P114,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"iE-Extensions"
		},
};
static const int asn_MAP_E1AP_QoS_Flow_Removed_Item_oms_1[] = { 1, 2, 3 };
static const ber_tlv_tag_t asn_DEF_E1AP_QoS_Flow_Removed_Item_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_E1AP_QoS_Flow_Removed_Item_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* qoS-Flow-Identifier */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* qoS-Flow-Released-In-Session */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 }, /* qoS-Flow-Accumulated-Session-Time */
    { (ASN_TAG_CLASS_CONTEXT | (3 << 2)), 3, 0, 0 } /* iE-Extensions */
};
asn_SEQUENCE_specifics_t asn_SPC_E1AP_QoS_Flow_Removed_Item_specs_1 = {
	sizeof(struct E1AP_QoS_Flow_Removed_Item),
	offsetof(struct E1AP_QoS_Flow_Removed_Item, _asn_ctx),
	asn_MAP_E1AP_QoS_Flow_Removed_Item_tag2el_1,
	4,	/* Count of tags in the map */
	asn_MAP_E1AP_QoS_Flow_Removed_Item_oms_1,	/* Optional members */
	3, 0,	/* Root/Additions */
	4,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_QoS_Flow_Removed_Item = {
	"QoS-Flow-Removed-Item",
	"QoS-Flow-Removed-Item",
	&asn_OP_SEQUENCE,
	asn_DEF_E1AP_QoS_Flow_Removed_Item_tags_1,
	sizeof(asn_DEF_E1AP_QoS_Flow_Removed_Item_tags_1)
		/sizeof(asn_DEF_E1AP_QoS_Flow_Removed_Item_tags_1[0]), /* 1 */
	asn_DEF_E1AP_QoS_Flow_Removed_Item_tags_1,	/* Same as above */
	sizeof(asn_DEF_E1AP_QoS_Flow_Removed_Item_tags_1)
		/sizeof(asn_DEF_E1AP_QoS_Flow_Removed_Item_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_E1AP_QoS_Flow_Removed_Item_1,
	4,	/* Elements count */
	&asn_SPC_E1AP_QoS_Flow_Removed_Item_specs_1	/* Additional specs */
};

