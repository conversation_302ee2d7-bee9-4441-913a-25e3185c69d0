/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 * From ASN.1 module "E1AP-PDU-Descriptions"
 * 	found in "/home/<USER>/openairinterface5g/openair2/E1AP/MESSAGES/ASN.1/38463-g80.R16.78.0.asn"
 * 	`asn1c -gen-APER -gen-UPER -no-gen-JER -no-gen-BER -no-gen-OER -fcompound-names -no-gen-example -findirect-choice -fno-include-deps -D /home/<USER>/openairinterface5g/build/openair2/E1AP/MESSAGES`
 */

#include "E1AP_SuccessfulOutcome.h"

static const long asn_VAL_1_E1AP_id_reset = 0;
static const long asn_VAL_1_E1AP_reject = 0;
static const long asn_VAL_2_E1AP_id_gNB_CU_UP_E1Setup = 3;
static const long asn_VAL_2_E1AP_reject = 0;
static const long asn_VAL_3_E1AP_id_gNB_CU_CP_E1Setup = 4;
static const long asn_VAL_3_E1AP_reject = 0;
static const long asn_VAL_4_E1AP_id_gNB_CU_UP_ConfigurationUpdate = 5;
static const long asn_VAL_4_E1AP_reject = 0;
static const long asn_VAL_5_E1AP_id_gNB_CU_CP_ConfigurationUpdate = 6;
static const long asn_VAL_5_E1AP_reject = 0;
static const long asn_VAL_6_E1AP_id_e1Release = 7;
static const long asn_VAL_6_E1AP_reject = 0;
static const long asn_VAL_7_E1AP_id_bearerContextSetup = 8;
static const long asn_VAL_7_E1AP_reject = 0;
static const long asn_VAL_8_E1AP_id_bearerContextModification = 9;
static const long asn_VAL_8_E1AP_reject = 0;
static const long asn_VAL_9_E1AP_id_bearerContextModificationRequired = 10;
static const long asn_VAL_9_E1AP_reject = 0;
static const long asn_VAL_10_E1AP_id_bearerContextRelease = 11;
static const long asn_VAL_10_E1AP_reject = 0;
static const long asn_VAL_11_E1AP_id_resourceStatusReportingInitiation = 22;
static const long asn_VAL_11_E1AP_reject = 0;
static const long asn_VAL_12_E1AP_id_iAB_UPTNLAddressUpdate = 24;
static const long asn_VAL_12_E1AP_reject = 0;
static const long asn_VAL_13_E1AP_id_errorIndication = 1;
static const long asn_VAL_13_E1AP_ignore = 1;
static const long asn_VAL_14_E1AP_id_bearerContextReleaseRequest = 12;
static const long asn_VAL_14_E1AP_ignore = 1;
static const long asn_VAL_15_E1AP_id_bearerContextInactivityNotification = 13;
static const long asn_VAL_15_E1AP_ignore = 1;
static const long asn_VAL_16_E1AP_id_dLDataNotification = 14;
static const long asn_VAL_16_E1AP_ignore = 1;
static const long asn_VAL_17_E1AP_id_uLDataNotification = 18;
static const long asn_VAL_17_E1AP_ignore = 1;
static const long asn_VAL_18_E1AP_id_dataUsageReport = 15;
static const long asn_VAL_18_E1AP_ignore = 1;
static const long asn_VAL_19_E1AP_id_gNB_CU_UP_CounterCheck = 16;
static const long asn_VAL_19_E1AP_ignore = 1;
static const long asn_VAL_20_E1AP_id_gNB_CU_UP_StatusIndication = 17;
static const long asn_VAL_20_E1AP_ignore = 1;
static const long asn_VAL_21_E1AP_id_mRDC_DataUsageReport = 19;
static const long asn_VAL_21_E1AP_ignore = 1;
static const long asn_VAL_22_E1AP_id_DeactivateTrace = 21;
static const long asn_VAL_22_E1AP_ignore = 1;
static const long asn_VAL_23_E1AP_id_TraceStart = 20;
static const long asn_VAL_23_E1AP_ignore = 1;
static const long asn_VAL_24_E1AP_id_privateMessage = 2;
static const long asn_VAL_24_E1AP_ignore = 1;
static const long asn_VAL_25_E1AP_id_CellTrafficTrace = 25;
static const long asn_VAL_25_E1AP_ignore = 1;
static const long asn_VAL_26_E1AP_id_resourceStatusReporting = 23;
static const long asn_VAL_26_E1AP_ignore = 1;
static const long asn_VAL_27_E1AP_id_earlyForwardingSNTransfer = 26;
static const long asn_VAL_27_E1AP_ignore = 1;
static const long asn_VAL_28_E1AP_id_gNB_CU_CPMeasurementResultsInformation = 27;
static const long asn_VAL_28_E1AP_ignore = 1;
static const asn_ioc_cell_t asn_IOS_E1AP_E1AP_ELEMENTARY_PROCEDURES_1_rows[] = {
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_Reset },
	{ "&SuccessfulOutcome", aioc__type, &asn_DEF_E1AP_ResetAcknowledge },
	{ "&UnsuccessfulOutcome",  },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_1_E1AP_id_reset },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_1_E1AP_reject },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_GNB_CU_UP_E1SetupRequest },
	{ "&SuccessfulOutcome", aioc__type, &asn_DEF_E1AP_GNB_CU_UP_E1SetupResponse },
	{ "&UnsuccessfulOutcome", aioc__type, &asn_DEF_E1AP_GNB_CU_UP_E1SetupFailure },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_2_E1AP_id_gNB_CU_UP_E1Setup },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_2_E1AP_reject },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_GNB_CU_CP_E1SetupRequest },
	{ "&SuccessfulOutcome", aioc__type, &asn_DEF_E1AP_GNB_CU_CP_E1SetupResponse },
	{ "&UnsuccessfulOutcome", aioc__type, &asn_DEF_E1AP_GNB_CU_CP_E1SetupFailure },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_3_E1AP_id_gNB_CU_CP_E1Setup },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_3_E1AP_reject },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_GNB_CU_UP_ConfigurationUpdate },
	{ "&SuccessfulOutcome", aioc__type, &asn_DEF_E1AP_GNB_CU_UP_ConfigurationUpdateAcknowledge },
	{ "&UnsuccessfulOutcome", aioc__type, &asn_DEF_E1AP_GNB_CU_UP_ConfigurationUpdateFailure },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_4_E1AP_id_gNB_CU_UP_ConfigurationUpdate },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_4_E1AP_reject },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_GNB_CU_CP_ConfigurationUpdate },
	{ "&SuccessfulOutcome", aioc__type, &asn_DEF_E1AP_GNB_CU_CP_ConfigurationUpdateAcknowledge },
	{ "&UnsuccessfulOutcome", aioc__type, &asn_DEF_E1AP_GNB_CU_CP_ConfigurationUpdateFailure },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_5_E1AP_id_gNB_CU_CP_ConfigurationUpdate },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_5_E1AP_reject },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_E1ReleaseRequest },
	{ "&SuccessfulOutcome", aioc__type, &asn_DEF_E1AP_E1ReleaseResponse },
	{ "&UnsuccessfulOutcome",  },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_6_E1AP_id_e1Release },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_6_E1AP_reject },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_BearerContextSetupRequest },
	{ "&SuccessfulOutcome", aioc__type, &asn_DEF_E1AP_BearerContextSetupResponse },
	{ "&UnsuccessfulOutcome", aioc__type, &asn_DEF_E1AP_BearerContextSetupFailure },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_7_E1AP_id_bearerContextSetup },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_7_E1AP_reject },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_BearerContextModificationRequest },
	{ "&SuccessfulOutcome", aioc__type, &asn_DEF_E1AP_BearerContextModificationResponse },
	{ "&UnsuccessfulOutcome", aioc__type, &asn_DEF_E1AP_BearerContextModificationFailure },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_8_E1AP_id_bearerContextModification },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_8_E1AP_reject },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_BearerContextModificationRequired },
	{ "&SuccessfulOutcome", aioc__type, &asn_DEF_E1AP_BearerContextModificationConfirm },
	{ "&UnsuccessfulOutcome",  },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_9_E1AP_id_bearerContextModificationRequired },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_9_E1AP_reject },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_BearerContextReleaseCommand },
	{ "&SuccessfulOutcome", aioc__type, &asn_DEF_E1AP_BearerContextReleaseComplete },
	{ "&UnsuccessfulOutcome",  },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_10_E1AP_id_bearerContextRelease },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_10_E1AP_reject },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_ResourceStatusRequest },
	{ "&SuccessfulOutcome", aioc__type, &asn_DEF_E1AP_ResourceStatusResponse },
	{ "&UnsuccessfulOutcome", aioc__type, &asn_DEF_E1AP_ResourceStatusFailure },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_11_E1AP_id_resourceStatusReportingInitiation },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_11_E1AP_reject },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_IAB_UPTNLAddressUpdate },
	{ "&SuccessfulOutcome", aioc__type, &asn_DEF_E1AP_IAB_UPTNLAddressUpdateAcknowledge },
	{ "&UnsuccessfulOutcome", aioc__type, &asn_DEF_E1AP_IAB_UPTNLAddressUpdateFailure },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_12_E1AP_id_iAB_UPTNLAddressUpdate },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_12_E1AP_reject },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_ErrorIndication },
	{ "&SuccessfulOutcome",  },
	{ "&UnsuccessfulOutcome",  },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_13_E1AP_id_errorIndication },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_13_E1AP_ignore },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_BearerContextReleaseRequest },
	{ "&SuccessfulOutcome",  },
	{ "&UnsuccessfulOutcome",  },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_14_E1AP_id_bearerContextReleaseRequest },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_14_E1AP_ignore },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_BearerContextInactivityNotification },
	{ "&SuccessfulOutcome",  },
	{ "&UnsuccessfulOutcome",  },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_15_E1AP_id_bearerContextInactivityNotification },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_15_E1AP_ignore },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_DLDataNotification },
	{ "&SuccessfulOutcome",  },
	{ "&UnsuccessfulOutcome",  },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_16_E1AP_id_dLDataNotification },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_16_E1AP_ignore },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_ULDataNotification },
	{ "&SuccessfulOutcome",  },
	{ "&UnsuccessfulOutcome",  },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_17_E1AP_id_uLDataNotification },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_17_E1AP_ignore },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_DataUsageReport },
	{ "&SuccessfulOutcome",  },
	{ "&UnsuccessfulOutcome",  },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_18_E1AP_id_dataUsageReport },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_18_E1AP_ignore },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_GNB_CU_UP_CounterCheckRequest },
	{ "&SuccessfulOutcome",  },
	{ "&UnsuccessfulOutcome",  },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_19_E1AP_id_gNB_CU_UP_CounterCheck },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_19_E1AP_ignore },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_GNB_CU_UP_StatusIndication },
	{ "&SuccessfulOutcome",  },
	{ "&UnsuccessfulOutcome",  },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_20_E1AP_id_gNB_CU_UP_StatusIndication },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_20_E1AP_ignore },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_MRDC_DataUsageReport },
	{ "&SuccessfulOutcome",  },
	{ "&UnsuccessfulOutcome",  },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_21_E1AP_id_mRDC_DataUsageReport },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_21_E1AP_ignore },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_DeactivateTrace },
	{ "&SuccessfulOutcome",  },
	{ "&UnsuccessfulOutcome",  },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_22_E1AP_id_DeactivateTrace },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_22_E1AP_ignore },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_TraceStart },
	{ "&SuccessfulOutcome",  },
	{ "&UnsuccessfulOutcome",  },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_23_E1AP_id_TraceStart },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_23_E1AP_ignore },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_PrivateMessage },
	{ "&SuccessfulOutcome",  },
	{ "&UnsuccessfulOutcome",  },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_24_E1AP_id_privateMessage },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_24_E1AP_ignore },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_CellTrafficTrace },
	{ "&SuccessfulOutcome",  },
	{ "&UnsuccessfulOutcome",  },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_25_E1AP_id_CellTrafficTrace },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_25_E1AP_ignore },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_ResourceStatusUpdate },
	{ "&SuccessfulOutcome",  },
	{ "&UnsuccessfulOutcome",  },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_26_E1AP_id_resourceStatusReporting },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_26_E1AP_ignore },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_EarlyForwardingSNTransfer },
	{ "&SuccessfulOutcome",  },
	{ "&UnsuccessfulOutcome",  },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_27_E1AP_id_earlyForwardingSNTransfer },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_27_E1AP_ignore },
	{ "&InitiatingMessage", aioc__type, &asn_DEF_E1AP_GNB_CU_CPMeasurementResultsInformation },
	{ "&SuccessfulOutcome",  },
	{ "&UnsuccessfulOutcome",  },
	{ "&procedureCode", aioc__value, &asn_DEF_E1AP_ProcedureCode, &asn_VAL_28_E1AP_id_gNB_CU_CPMeasurementResultsInformation },
	{ "&criticality", aioc__value, &asn_DEF_E1AP_Criticality, &asn_VAL_28_E1AP_ignore }
};
static const asn_ioc_set_t asn_IOS_E1AP_E1AP_ELEMENTARY_PROCEDURES_1[] = {
	{ 28, 5, asn_IOS_E1AP_E1AP_ELEMENTARY_PROCEDURES_1_rows }
};
static int
memb_E1AP_procedureCode_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	long value;
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	value = *(const long *)sptr;
	
	if((value >= 0L && value <= 255L)) {
		/* Constraint check succeeded */
		return 0;
	} else {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: constraint failed (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
}

static asn_type_selector_result_t
select_SuccessfulOutcome_E1AP_criticality_type(const asn_TYPE_descriptor_t *parent_type, const void *parent_sptr) {
	asn_type_selector_result_t result = {0, 0};
	const asn_ioc_set_t *itable = asn_IOS_E1AP_E1AP_ELEMENTARY_PROCEDURES_1;
	size_t constraining_column = 3; /* &procedureCode */
	size_t for_column = 4; /* &criticality */
	size_t row, presence_index = 0;
	const long *constraining_value = (const long *)((const char *)parent_sptr + offsetof(struct E1AP_SuccessfulOutcome, procedureCode));
	
	for(row=0; row < itable->rows_count; row++) {
	    const asn_ioc_cell_t *constraining_cell = &itable->rows[row * itable->columns_count + constraining_column];
	    const asn_ioc_cell_t *type_cell = &itable->rows[row * itable->columns_count + for_column];
	
	    if(type_cell->cell_kind == aioc__undefined)
	        continue;
	
	    presence_index++;
	    if(constraining_cell->type_descriptor->op->compare_struct(constraining_cell->type_descriptor, constraining_value, constraining_cell->value_sptr) == 0) {
	        result.type_descriptor = type_cell->type_descriptor;
	        result.presence_index = presence_index;
	        break;
	    }
	}
	
	return result;
}

static int
memb_E1AP_criticality_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	
	if(1 /* No applicable constraints whatsoever */) {
		/* Nothing is here. See below */
	}
	
	return td->encoding_constraints.general_constraints(td, sptr, ctfailcb, app_key);
}

static asn_type_selector_result_t
select_SuccessfulOutcome_E1AP_value_type(const asn_TYPE_descriptor_t *parent_type, const void *parent_sptr) {
	asn_type_selector_result_t result = {0, 0};
	const asn_ioc_set_t *itable = asn_IOS_E1AP_E1AP_ELEMENTARY_PROCEDURES_1;
	size_t constraining_column = 3; /* &procedureCode */
	size_t for_column = 1; /* &SuccessfulOutcome */
	size_t row, presence_index = 0;
	const long *constraining_value = (const long *)((const char *)parent_sptr + offsetof(struct E1AP_SuccessfulOutcome, procedureCode));
	
	for(row=0; row < itable->rows_count; row++) {
	    const asn_ioc_cell_t *constraining_cell = &itable->rows[row * itable->columns_count + constraining_column];
	    const asn_ioc_cell_t *type_cell = &itable->rows[row * itable->columns_count + for_column];
	
	    if(type_cell->cell_kind == aioc__undefined)
	        continue;
	
	    presence_index++;
	    if(constraining_cell->type_descriptor->op->compare_struct(constraining_cell->type_descriptor, constraining_value, constraining_cell->value_sptr) == 0) {
	        result.type_descriptor = type_cell->type_descriptor;
	        result.presence_index = presence_index;
	        break;
	    }
	}
	
	return result;
}

static int
memb_E1AP_value_constraint_1(const asn_TYPE_descriptor_t *td, const void *sptr,
			asn_app_constraint_failed_f *ctfailcb, void *app_key) {
	
	if(!sptr) {
		ASN__CTFAIL(app_key, td, sptr,
			"%s: value not given (%s:%d)",
			td->name, __FILE__, __LINE__);
		return -1;
	}
	
	
	if(1 /* No applicable constraints whatsoever */) {
		/* Nothing is here. See below */
	}
	
	return td->encoding_constraints.general_constraints(td, sptr, ctfailcb, app_key);
}

#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_E1AP_procedureCode_constr_2 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 8,  8,  0,  255 }	/* (0..255) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_E1AP_criticality_constr_3 CC_NOTUSED = {
	{ APC_CONSTRAINED,	 2,  2,  0,  2 }	/* (0..2) */,
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
static asn_per_constraints_t asn_PER_memb_E1AP_value_constr_4 CC_NOTUSED = {
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	{ APC_UNCONSTRAINED,	-1, -1,  0,  0 },
	0, 0	/* No PER value map */
};
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
static asn_TYPE_member_t asn_MBR_E1AP_value_4[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct E1AP_SuccessfulOutcome__value, choice.ResetAcknowledge),
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_ResetAcknowledge,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ResetAcknowledge"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct E1AP_SuccessfulOutcome__value, choice.GNB_CU_UP_E1SetupResponse),
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_UP_E1SetupResponse,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"GNB-CU-UP-E1SetupResponse"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct E1AP_SuccessfulOutcome__value, choice.GNB_CU_CP_E1SetupResponse),
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_CP_E1SetupResponse,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"GNB-CU-CP-E1SetupResponse"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct E1AP_SuccessfulOutcome__value, choice.GNB_CU_UP_ConfigurationUpdateAcknowledge),
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_UP_ConfigurationUpdateAcknowledge,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"GNB-CU-UP-ConfigurationUpdateAcknowledge"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct E1AP_SuccessfulOutcome__value, choice.GNB_CU_CP_ConfigurationUpdateAcknowledge),
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_GNB_CU_CP_ConfigurationUpdateAcknowledge,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"GNB-CU-CP-ConfigurationUpdateAcknowledge"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct E1AP_SuccessfulOutcome__value, choice.E1ReleaseResponse),
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_E1ReleaseResponse,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"E1ReleaseResponse"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct E1AP_SuccessfulOutcome__value, choice.BearerContextSetupResponse),
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_BearerContextSetupResponse,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"BearerContextSetupResponse"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct E1AP_SuccessfulOutcome__value, choice.BearerContextModificationResponse),
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_BearerContextModificationResponse,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"BearerContextModificationResponse"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct E1AP_SuccessfulOutcome__value, choice.BearerContextModificationConfirm),
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_BearerContextModificationConfirm,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"BearerContextModificationConfirm"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct E1AP_SuccessfulOutcome__value, choice.BearerContextReleaseComplete),
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_BearerContextReleaseComplete,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"BearerContextReleaseComplete"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct E1AP_SuccessfulOutcome__value, choice.ResourceStatusResponse),
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_ResourceStatusResponse,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"ResourceStatusResponse"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct E1AP_SuccessfulOutcome__value, choice.IAB_UPTNLAddressUpdateAcknowledge),
		(ASN_TAG_CLASS_UNIVERSAL | (16 << 2)),
		0,
		&asn_DEF_E1AP_IAB_UPTNLAddressUpdateAcknowledge,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			0
		},
		0, 0, /* No default value */
		"IAB-UPTNLAddressUpdateAcknowledge"
		},
};
static const asn_TYPE_tag2member_t asn_MAP_E1AP_value_tag2el_4[] = {
    { (ASN_TAG_CLASS_UNIVERSAL | (16 << 2)), 0, 0, 11 }, /* ResetAcknowledge */
    { (ASN_TAG_CLASS_UNIVERSAL | (16 << 2)), 1, -1, 10 }, /* GNB-CU-UP-E1SetupResponse */
    { (ASN_TAG_CLASS_UNIVERSAL | (16 << 2)), 2, -2, 9 }, /* GNB-CU-CP-E1SetupResponse */
    { (ASN_TAG_CLASS_UNIVERSAL | (16 << 2)), 3, -3, 8 }, /* GNB-CU-UP-ConfigurationUpdateAcknowledge */
    { (ASN_TAG_CLASS_UNIVERSAL | (16 << 2)), 4, -4, 7 }, /* GNB-CU-CP-ConfigurationUpdateAcknowledge */
    { (ASN_TAG_CLASS_UNIVERSAL | (16 << 2)), 5, -5, 6 }, /* E1ReleaseResponse */
    { (ASN_TAG_CLASS_UNIVERSAL | (16 << 2)), 6, -6, 5 }, /* BearerContextSetupResponse */
    { (ASN_TAG_CLASS_UNIVERSAL | (16 << 2)), 7, -7, 4 }, /* BearerContextModificationResponse */
    { (ASN_TAG_CLASS_UNIVERSAL | (16 << 2)), 8, -8, 3 }, /* BearerContextModificationConfirm */
    { (ASN_TAG_CLASS_UNIVERSAL | (16 << 2)), 9, -9, 2 }, /* BearerContextReleaseComplete */
    { (ASN_TAG_CLASS_UNIVERSAL | (16 << 2)), 10, -10, 1 }, /* ResourceStatusResponse */
    { (ASN_TAG_CLASS_UNIVERSAL | (16 << 2)), 11, -11, 0 } /* IAB-UPTNLAddressUpdateAcknowledge */
};
static asn_CHOICE_specifics_t asn_SPC_E1AP_value_specs_4 = {
	sizeof(struct E1AP_SuccessfulOutcome__value),
	offsetof(struct E1AP_SuccessfulOutcome__value, _asn_ctx),
	offsetof(struct E1AP_SuccessfulOutcome__value, present),
	sizeof(((struct E1AP_SuccessfulOutcome__value *)0)->present),
	asn_MAP_E1AP_value_tag2el_4,
	12,	/* Count of tags in the map */
	0, 0,
	-1	/* Extensions start */
};
static /* Use -fall-defs-global to expose */
asn_TYPE_descriptor_t asn_DEF_E1AP_value_4 = {
	"value",
	"value",
	&asn_OP_OPEN_TYPE,
	0,	/* No effective tags (pointer) */
	0,	/* No effective tags (count) */
	0,	/* No tags (pointer) */
	0,	/* No tags (count) */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		OPEN_TYPE_constraint
	},
	asn_MBR_E1AP_value_4,
	12,	/* Elements count */
	&asn_SPC_E1AP_value_specs_4	/* Additional specs */
};

asn_TYPE_member_t asn_MBR_E1AP_SuccessfulOutcome_1[] = {
	{ ATF_NOFLAGS, 0, offsetof(struct E1AP_SuccessfulOutcome, procedureCode),
		(ASN_TAG_CLASS_CONTEXT | (0 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_E1AP_ProcedureCode,
		0,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_E1AP_procedureCode_constr_2,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_E1AP_procedureCode_constraint_1
		},
		0, 0, /* No default value */
		"procedureCode"
		},
	{ ATF_NOFLAGS, 0, offsetof(struct E1AP_SuccessfulOutcome, criticality),
		(ASN_TAG_CLASS_CONTEXT | (1 << 2)),
		-1,	/* IMPLICIT tag at current level */
		&asn_DEF_E1AP_Criticality,
		select_SuccessfulOutcome_E1AP_criticality_type,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_E1AP_criticality_constr_3,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_E1AP_criticality_constraint_1
		},
		0, 0, /* No default value */
		"criticality"
		},
	{ ATF_OPEN_TYPE | ATF_NOFLAGS, 0, offsetof(struct E1AP_SuccessfulOutcome, value),
		(ASN_TAG_CLASS_CONTEXT | (2 << 2)),
		+1,	/* EXPLICIT tag at current level */
		&asn_DEF_E1AP_value_4,
		select_SuccessfulOutcome_E1AP_value_type,
		{
#if !defined(ASN_DISABLE_OER_SUPPORT)
			0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
			&asn_PER_memb_E1AP_value_constr_4,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
			memb_E1AP_value_constraint_1
		},
		0, 0, /* No default value */
		"value"
		},
};
static const ber_tlv_tag_t asn_DEF_E1AP_SuccessfulOutcome_tags_1[] = {
	(ASN_TAG_CLASS_UNIVERSAL | (16 << 2))
};
static const asn_TYPE_tag2member_t asn_MAP_E1AP_SuccessfulOutcome_tag2el_1[] = {
    { (ASN_TAG_CLASS_CONTEXT | (0 << 2)), 0, 0, 0 }, /* procedureCode */
    { (ASN_TAG_CLASS_CONTEXT | (1 << 2)), 1, 0, 0 }, /* criticality */
    { (ASN_TAG_CLASS_CONTEXT | (2 << 2)), 2, 0, 0 } /* value */
};
asn_SEQUENCE_specifics_t asn_SPC_E1AP_SuccessfulOutcome_specs_1 = {
	sizeof(struct E1AP_SuccessfulOutcome),
	offsetof(struct E1AP_SuccessfulOutcome, _asn_ctx),
	asn_MAP_E1AP_SuccessfulOutcome_tag2el_1,
	3,	/* Count of tags in the map */
	0, 0, 0,	/* Optional elements (not needed) */
	-1,	/* First extension addition */
};
asn_TYPE_descriptor_t asn_DEF_E1AP_SuccessfulOutcome = {
	"SuccessfulOutcome",
	"SuccessfulOutcome",
	&asn_OP_SEQUENCE,
	asn_DEF_E1AP_SuccessfulOutcome_tags_1,
	sizeof(asn_DEF_E1AP_SuccessfulOutcome_tags_1)
		/sizeof(asn_DEF_E1AP_SuccessfulOutcome_tags_1[0]), /* 1 */
	asn_DEF_E1AP_SuccessfulOutcome_tags_1,	/* Same as above */
	sizeof(asn_DEF_E1AP_SuccessfulOutcome_tags_1)
		/sizeof(asn_DEF_E1AP_SuccessfulOutcome_tags_1[0]), /* 1 */
	{
#if !defined(ASN_DISABLE_OER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_OER_SUPPORT) */
#if !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT)
		0,
#endif  /* !defined(ASN_DISABLE_UPER_SUPPORT) || !defined(ASN_DISABLE_APER_SUPPORT) */
		SEQUENCE_constraint
	},
	asn_MBR_E1AP_SuccessfulOutcome_1,
	3,	/* Elements count */
	&asn_SPC_E1AP_SuccessfulOutcome_specs_1	/* Additional specs */
};

