/*
 * Generated by asn1c-0.9.29 (http://lionet.info/asn1c)
 */

#ifndef _E1AP_ASN_CONSTANT_H
#define _E1AP_ASN_CONSTANT_H

#ifdef __cplusplus
extern "C" {
#endif

#define min_val_E1AP_GNB_CU_CP_UE_E1AP_ID (0)
#define max_val_E1AP_GNB_CU_CP_UE_E1AP_ID (4294967295)
#define min_val_E1AP_GNB_CU_UP_Capacity (0)
#define max_val_E1AP_GNB_CU_UP_Capacity (255)
#define min_val_E1AP_GNB_CU_UP_ID (0)
#define max_val_E1AP_GNB_CU_UP_ID (68719476735)
#define min_val_E1AP_GNB_CU_UP_UE_E1AP_ID (0)
#define max_val_E1AP_GNB_CU_UP_UE_E1AP_ID (4294967295)
#define min_val_E1AP_GNB_DU_ID (0)
#define max_val_E1AP_GNB_DU_ID (68719476735)
#define min_val_E1AP_HFN (0)
#define max_val_E1AP_HFN (4294967295)
#define min_val_E1AP_PDCP_SN (0)
#define max_val_E1AP_PDCP_SN (262143)
#define min_val_E1AP_PDU_Session_ID (0)
#define max_val_E1AP_PDU_Session_ID (255)
#define min_val_E1AP_PriorityLevel (0)
#define max_val_E1AP_PriorityLevel (15)
#define min_val_E1AP_QCI (0)
#define max_val_E1AP_QCI (255)
#define min_val_E1AP_QoS_Flow_Identifier (0)
#define max_val_E1AP_QoS_Flow_Identifier (63)
#define E1AP_maxPrivateIEs (65535)
#define E1AP_maxProtocolExtensions (65535)
#define E1AP_maxProtocolIEs (65535)
#define min_val_E1AP_ProcedureCode (0)
#define max_val_E1AP_ProcedureCode (255)
#define min_val_E1AP_ProtocolExtensionID (0)
#define max_val_E1AP_ProtocolExtensionID (65535)
#define min_val_E1AP_ProtocolIE_ID (0)
#define max_val_E1AP_ProtocolIE_ID (65535)
#define E1AP_maxnoofErrors (256)
#define E1AP_maxnoofSPLMNs (12)
#define E1AP_maxnoofSliceItems (1024)
#define E1AP_maxnoofIndividualE1ConnectionsToReset (65536)
#define E1AP_maxnoofEUTRANQOSParameters (256)
#define E1AP_maxnoofNGRANQOSParameters (256)
#define E1AP_maxnoofDRBs (32)
#define E1AP_maxnoofNRCGI (512)
#define E1AP_maxnoofPDUSessionResource (256)
#define E1AP_maxnoofQoSFlows (64)
#define E1AP_maxnoofUPParameters (8)
#define E1AP_maxnoofCellGroups (4)
#define E1AP_maxnooftimeperiods (2)
#define E1AP_maxnoofTNLAssociations (32)
#define E1AP_maxnoofTLAs (16)
#define E1AP_maxnoofGTPTLAs (16)
#define E1AP_maxnoofTNLAddresses (8)
#define E1AP_maxnoofMDTPLMNs (16)
#define E1AP_maxnoofQoSParaSets (8)
#define E1AP_maxnoofExtSliceItems (65535)
#define E1AP_maxnoofDataForwardingTunneltoE_UTRAN (256)
#define E1AP_maxnoofExtNRCGI (16384)


#ifdef __cplusplus
}
#endif

#endif /* _E1AP_ASN_CONSTANT_H */
